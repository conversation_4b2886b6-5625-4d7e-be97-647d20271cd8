{"AndroidPlayerBuildProgram.Actions.ActionGenerateProjectFiles+Arguments": {"ProjectPath": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/Android/Prj/IL2CPP/Gradle", "Architectures": "ARM64", "BuildSystem": "<PERSON><PERSON><PERSON>", "GradleProjectCreateInfo": {"HostPlatform": "Windows", "ApplicationType": "APK", "BuildType": "Release", "AndroidSDKPath": "C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.44f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK", "AndroidNDKPath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK", "PreferredHeapSizeForJVM": 4096, "GradleVersion": "7.5.1", "UnityLibraryTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\GradleTemplates\\mainTemplate.gradle", "UnityLibraryTemplatePathUsed": false, "LauncherTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\GradleTemplates\\launcherTemplate.gradle", "LauncherTemplatePathUsed": false, "BaseProjectTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\GradleTemplates\\baseProjectTemplate.gradle", "BaseProjectTemplatePathUsed": false, "GradlePropertiesTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\GradleTemplates\\gradleTemplate.properties", "GradlePropertiesTemplatePathUsed": false, "GradleLibraryTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\GradleTemplates\\libTemplate.gradle", "GradleLibraryTemplatePathUsed": false, "UnityProguardTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\UnityProGuardTemplate.txt", "ProguardUserPath": "", "SettingsTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools\\GradleTemplates\\settingsTemplate.gradle", "SettingsTemplatePathUsed": false, "BuildTools": "34.0.0", "TargetSDKVersion": 29, "MinSDKVersion": 29, "PackageName": "com.DefaultCompany.VRAssembly", "Architectures": "ARM64", "BuildApkPerCpuArchitecture": false, "VersionCode": 1, "VersionName": "0.1", "Minify": 1, "NoCompressOptions": {"RelativeFilePaths": [], "FileExtensions": []}, "UseCustomKeystore": false, "KeystorePath": "", "KeystoreName": "", "KeystorePassword": "", "KeystoreAliasName": "", "KeystoreAliasPassword": "", "ScriptingImplementation": "IL2CPP", "AndroidLibraries": ["Library/PackageCache/com.unity.xr.management@4.4.0/xrmanifest.androidlib"], "AARFiles": ["D:/PICO Unity Integration SDK-3.2.0-20250529/Runtime/Android/loader-1.0.5.ForUnitySDK.aar", "D:/PICO Unity Integration SDK-3.2.0-20250529/Enterprise/android/tob_api-release.aar", "D:/PICO Unity Integration SDK-3.2.0-20250529/Enterprise/android/capturelib-0.0.7.aar", "D:/PICO Unity Integration SDK-3.2.0-20250529/Enterprise/android/CameraRenderingPlugin.aar", "D:/PICO Unity Integration SDK-3.2.0-20250529/Runtime/Android/PxrPlatform.aar", "D:/PICO Unity Integration SDK-3.2.0-20250529/Enterprise/android/tobservicelib-release.aar", "D:/PICO Unity Integration SDK-3.2.0-20250529/Enterprise/android/BAuthLib-1.0.0.aar"], "BuiltinJavaSourcePaths": ["com/unity3d/player/UnityPlayerActivity.java"], "JavaSourcePaths": [], "KotlinSourcePaths": [], "PlayerPackage": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer", "PlayerPackageTools": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools", "SymlinkSources": false, "InstallIntoBuildsFolder": false, "UnityPath": "", "UnityProjectPath": "D:/nwu/Assembly/UnityProjects/VRAssembly", "Dependencies": [], "AAPT2Path": "C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.44f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\build-tools\\34.0.0\\aapt2.exe"}}, "Bee.TundraBackend.CSharpActionInvocationInformation": {"typeFullName": "AndroidPlayerBuildProgram.Actions.ActionGenerateProjectFiles", "methodName": "Run", "assemblyLocation": "C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.44f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\AndroidPlayerBuildProgram.exe", "targets": ["D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/Android/Prj/IL2CPP/Gradle/build.gradle", "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build.gradle", "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/build.gradle", "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/Android/Prj/IL2CPP/Gradle/gradle.properties", "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/Android/Prj/IL2CPP/Gradle/local.properties", "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/Android/Prj/IL2CPP/Gradle/settings.gradle", "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/xrmanifest.androidlib/build.gradle", "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/proguard-unity.txt", "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/Android/Prj/IL2CPP/Gradle/gradle/wrapper/gradle-wrapper.properties"], "inputs": ["C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/mainTemplate.gradle", "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/launcherTemplate.gradle", "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/baseProjectTemplate.gradle", "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/gradleTemplate.properties", "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/libTemplate.gradle", "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/GradleTemplates/settingsTemplate.gradle", "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Tools/UnityProGuardTemplate.txt"], "targetDirectories": []}}