using UnityEngine;
using UnityEngine.XR;
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.SpatialTracking;

/// <summary>
/// PICO控制器设置脚本
/// 自动为控制器添加必要的组件
/// </summary>
public class PICOControllerSetup : MonoBehaviour
{
    [Header("自动设置")]
    [SerializeField] private bool autoSetupOnStart = true;
    [SerializeField] private bool forceReconfigure = false;

    void Start()
    {
        if (autoSetupOnStart)
        {
            SetupControllers();
        }
    }

    /// <summary>
    /// 设置控制器
    /// </summary>
    [ContextMenu("设置控制器")]
    public void SetupControllers()
    {
        Debug.Log("=== 开始设置PICO控制器 ===");

        // 查找控制器对象
        GameObject leftControllerObj = GameObject.Find("Left Controller");
        GameObject rightControllerObj = GameObject.Find("Right Controller");

        if (leftControllerObj != null)
        {
            SetupSingleController(leftControllerObj, XRNode.LeftHand, "左手");
        }
        else
        {
            Debug.LogWarning("未找到Left Controller对象");
        }

        if (rightControllerObj != null)
        {
            SetupSingleController(rightControllerObj, XRNode.RightHand, "右手");
        }
        else
        {
            Debug.LogWarning("未找到Right Controller对象");
        }

        Debug.Log("=== PICO控制器设置完成 ===");
    }

    /// <summary>
    /// 设置单个控制器
    /// </summary>
    private void SetupSingleController(GameObject controllerObj, XRNode node, string handName)
    {
        Debug.Log($"--- 设置{handName}控制器: {controllerObj.name} ---");
        // 1. 添加或配置XRController组件
        var xrController = controllerObj.GetComponent<XRController>();
        if (xrController == null || forceReconfigure)
        {
            if (xrController != null && forceReconfigure)
            {
                DestroyImmediate(xrController);
            }
            
            xrController = controllerObj.AddComponent<XRController>();
            xrController.controllerNode = node;
            Debug.Log($"✓ 已添加XRController组件到{handName}控制器");
        }
        else
        {
            xrController.controllerNode = node;
            Debug.Log($"✓ {handName}控制器XRController组件已存在");
        }

        // 2. 添加或配置TrackedPoseDriver组件
        var trackedPoseDriver = controllerObj.GetComponent<TrackedPoseDriver>();
        if (trackedPoseDriver == null || forceReconfigure)
        {
            if (trackedPoseDriver != null && forceReconfigure)
            {
                DestroyImmediate(trackedPoseDriver);
            }
            
            trackedPoseDriver = controllerObj.AddComponent<TrackedPoseDriver>();
            // TrackedPoseDriver的属性在新版本中是只读的，会自动配置
            Debug.Log($"✓ 已添加TrackedPoseDriver组件到{handName}控制器");
        }
        else
        {
            Debug.Log($"✓ {handName}控制器TrackedPoseDriver组件已存在");
        }

        // 3. 添加或配置XRRayInteractor组件
        var rayInteractor = controllerObj.GetComponent<XRRayInteractor>();
        if (rayInteractor == null || forceReconfigure)
        {
            if (rayInteractor != null && forceReconfigure)
            {
                DestroyImmediate(rayInteractor);
            }
            
            rayInteractor = controllerObj.AddComponent<XRRayInteractor>();
            Debug.Log($"✓ 已添加XRRayInteractor组件到{handName}控制器");
        }
        else
        {
            Debug.Log($"✓ {handName}控制器XRRayInteractor组件已存在");
        }

        // 4. 添加或配置LineRenderer组件
        var lineRenderer = controllerObj.GetComponent<LineRenderer>();
        if (lineRenderer == null || forceReconfigure)
        {
            if (lineRenderer != null && forceReconfigure)
            {
                DestroyImmediate(lineRenderer);
            }
            
            lineRenderer = controllerObj.AddComponent<LineRenderer>();
            SetupLineRenderer(lineRenderer);
            Debug.Log($"✓ 已添加LineRenderer组件到{handName}控制器");
        }
        else
        {
            Debug.Log($"✓ {handName}控制器LineRenderer组件已存在");
        }

        // 5. 添加或配置XRInteractorLineVisual组件
        var lineVisual = controllerObj.GetComponent<XRInteractorLineVisual>();
        if (lineVisual == null || forceReconfigure)
        {
            if (lineVisual != null && forceReconfigure)
            {
                DestroyImmediate(lineVisual);
            }
            
            lineVisual = controllerObj.AddComponent<XRInteractorLineVisual>();
            Debug.Log($"✓ 已添加XRInteractorLineVisual组件到{handName}控制器");
        }
        else
        {
            Debug.Log($"✓ {handName}控制器XRInteractorLineVisual组件已存在");
        }

        Debug.Log($"✓ {handName}控制器设置完成");
    }

    /// <summary>
    /// 设置LineRenderer
    /// </summary>
    private void SetupLineRenderer(LineRenderer lineRenderer)
    {
        lineRenderer.material = new Material(Shader.Find("Sprites/Default"));
       // lineRenderer.color = Color.blue;
        lineRenderer.startWidth = 0.01f;
        lineRenderer.endWidth = 0.01f;
        lineRenderer.positionCount = 2;
        lineRenderer.useWorldSpace = true;
        lineRenderer.enabled = true;
    }

    /// <summary>
    /// 检查控制器状态
    /// </summary>
    [ContextMenu("检查控制器状态")]
    public void CheckControllerStatus()
    {
        Debug.Log("=== 检查控制器状态 ===");
        var controllers = FindObjectsOfType<XRController>();
        Debug.Log($"找到 {controllers.Length} 个XRController组件");

        foreach (var controller in controllers)
        {
            string handName = controller.controllerNode == XRNode.LeftHand ? "左手" : 
                             controller.controllerNode == XRNode.RightHand ? "右手" : "未知";
            
            Debug.Log($"控制器: {controller.name}");
            Debug.Log($"  - 节点: {controller.controllerNode} ({handName})");
            Debug.Log($"  - 设备有效: {controller.inputDevice.isValid}");
            
            if (controller.inputDevice.isValid)
            {
                Debug.Log($"  - 设备名称: {controller.inputDevice.name}");
                
                // 测试基本输入
                bool triggerPressed = false;
                controller.inputDevice.TryGetFeatureValue(CommonUsages.triggerButton, out triggerPressed);
                Debug.Log($"  - 扳机状态: {(triggerPressed ? "按下" : "释放")}");
            }
        }

        // 检查场景中的控制器对象
        GameObject leftObj = GameObject.Find("Left Controller");
        GameObject rightObj = GameObject.Find("Right Controller");
        
        Debug.Log($"Left Controller对象: {(leftObj != null ? "存在" : "不存在")}");
        Debug.Log($"Right Controller对象: {(rightObj != null ? "存在" : "不存在")}");
    }

    /// <summary>
    /// 列出所有控制器相关对象
    /// </summary>
    [ContextMenu("列出控制器对象")]
    public void ListControllerObjects()
    {
        Debug.Log("=== 控制器相关对象列表 ===");
        
        var allObjects = FindObjectsOfType<GameObject>();
        int count = 0;
        
        foreach (var obj in allObjects)
        {
            if (obj.name.ToLower().Contains("controller") || 
                obj.name.ToLower().Contains("hand") ||
                obj.name.ToLower().Contains("left") ||
                obj.name.ToLower().Contains("right"))
            {
                count++;
                Debug.Log($"{count}. {obj.name} (父对象: {(obj.transform.parent != null ? obj.transform.parent.name : "无")})");
                
                // 列出组件
                var components = obj.GetComponents<Component>();
                foreach (var comp in components)
                {
                    if (comp.GetType().Name.Contains("XR") || comp.GetType().Name.Contains("Tracked"))
                    {
                        Debug.Log($"   - {comp.GetType().Name}");
                    }
                }
            }
        }
        
        Debug.Log($"总共找到 {count} 个相关对象");
    }

    /// <summary>
    /// 强制重新配置所有控制器
    /// </summary>
    [ContextMenu("强制重新配置")]
    public void ForceReconfigure()
    {
        forceReconfigure = true;
        SetupControllers();
        forceReconfigure = false;
    }
}
