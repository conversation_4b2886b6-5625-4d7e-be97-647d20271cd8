D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\build.gradle:20: Error: Google Play requires that apps target API level 31 or higher.
 [ExpiredTargetSdkVersion]
        targetSdkVersion 29
        ~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ExpiredTargetSdkVersion":
   As of the second half of 2018, Google Play requires that new apps and app
   updates target API level 26 or higher.

   Configuring your app to target a recent API level ensures that users
   benefit from significant security and performance improvements, while still
   allowing your app to run on older Android versions (down to the
   minSdkVersion).

   To update your targetSdkVersion, follow the steps from "Meeting Google Play
   requirements for target API level",
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://support.google.com/googleplay/android-developer/answer/113469#targetsdk
   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

1 errors, 0 warnings
