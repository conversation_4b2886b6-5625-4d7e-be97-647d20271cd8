<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 7.4.2" type="conditional_incidents">

    <incident
        id="ExpiredTargetSdkVersion"
        severity="fatal"
        message="Google Play requires that apps target API level 31 or higher.&#xA;">
        <fix-replace
            description="Update targetSdkVersion to 35"
            oldString="29"
            replacement="35"/>
        <location
            file="${:launcher*projectDir}/build.gradle"
            line="20"
            column="9"
            startOffset="511"
            endLine="20"
            endColumn="28"
            endOffset="530"/>
    </incident>

</incidents>
