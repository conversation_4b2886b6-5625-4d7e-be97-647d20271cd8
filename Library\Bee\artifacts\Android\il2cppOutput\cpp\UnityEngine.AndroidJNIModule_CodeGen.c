﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void UnityEngine.AndroidJavaRunnable::.ctor(System.Object,System.IntPtr)
extern void AndroidJavaRunnable__ctor_m000E4FEB2DE8031A1CD733610D76E2BF60490334 (void);
// 0x00000002 System.Void UnityEngine.AndroidJavaRunnable::Invoke()
extern void AndroidJavaRunnable_Invoke_m98CFB1479B942F71BF29F53CFDAC1CB9DAFAEBE1 (void);
// 0x00000003 System.Void UnityEngine.AndroidJavaException::.ctor(System.String,System.String)
extern void AndroidJavaException__ctor_mD4B5992BB074504F8E86D79EA98752D3CB154541 (void);
// 0x00000004 System.String UnityEngine.AndroidJavaException::get_StackTrace()
extern void AndroidJavaException_get_StackTrace_m28AC922BCC16051CCBA4C7E5F69698264AA7CC27 (void);
// 0x00000005 System.Void UnityEngine.GlobalJavaObjectRef::.ctor(System.IntPtr)
extern void GlobalJavaObjectRef__ctor_mFE5679D1B51F51CBF11721773C0D767286AC22E8 (void);
// 0x00000006 System.Void UnityEngine.GlobalJavaObjectRef::Finalize()
extern void GlobalJavaObjectRef_Finalize_m2EE89F98A391773F885A4A312FD4BD134E0D46D8 (void);
// 0x00000007 System.IntPtr UnityEngine.GlobalJavaObjectRef::op_Implicit(UnityEngine.GlobalJavaObjectRef)
extern void GlobalJavaObjectRef_op_Implicit_m444B263750F9B778C87C30EA918CDC0B62271F24 (void);
// 0x00000008 System.Void UnityEngine.GlobalJavaObjectRef::Dispose()
extern void GlobalJavaObjectRef_Dispose_m45E67345587866D5A50D250D1C17425110703520 (void);
// 0x00000009 System.Void UnityEngine.AndroidJavaRunnableProxy::.ctor(UnityEngine.AndroidJavaRunnable)
extern void AndroidJavaRunnableProxy__ctor_mB173256AF7629962B226343C4F6F94FFFF7317C3 (void);
// 0x0000000A System.Void UnityEngine.AndroidJavaRunnableProxy::run()
extern void AndroidJavaRunnableProxy_run_m014F4E0A8ED56A054096F2BAC90653716D2A0D46 (void);
// 0x0000000B System.Void UnityEngine.AndroidJavaProxy::.ctor(System.String)
extern void AndroidJavaProxy__ctor_m2832886A0E1BBF6702653A7C6A4609F11FB712C7 (void);
// 0x0000000C System.Void UnityEngine.AndroidJavaProxy::.ctor(UnityEngine.AndroidJavaClass)
extern void AndroidJavaProxy__ctor_mFA05DF6B31FC284C65D378C02A2A34F277DFE6E5 (void);
// 0x0000000D System.Void UnityEngine.AndroidJavaProxy::Finalize()
extern void AndroidJavaProxy_Finalize_m6E4C294F2117D7A07E82A315081C9239AFA217E8 (void);
// 0x0000000E UnityEngine.AndroidJavaObject UnityEngine.AndroidJavaProxy::Invoke(System.String,System.Object[])
extern void AndroidJavaProxy_Invoke_m9D765F3E7DC37C5CB14C4884F2873B48D2F96BFB (void);
// 0x0000000F UnityEngine.AndroidJavaObject UnityEngine.AndroidJavaProxy::Invoke(System.String,UnityEngine.AndroidJavaObject[])
extern void AndroidJavaProxy_Invoke_mCAE9C5E669AD50DE372494E12224FF1F31A43F1D (void);
// 0x00000010 System.Boolean UnityEngine.AndroidJavaProxy::equals(UnityEngine.AndroidJavaObject)
extern void AndroidJavaProxy_equals_mC390139E035408E858940EB523D45ED3C8377110 (void);
// 0x00000011 System.Int32 UnityEngine.AndroidJavaProxy::hashCode()
extern void AndroidJavaProxy_hashCode_m7991233D3D6D5F994E7BC59C3CB65DBBEDF8CA93 (void);
// 0x00000012 System.String UnityEngine.AndroidJavaProxy::toString()
extern void AndroidJavaProxy_toString_mF77EEDD3BB413F1273D9970BFB0D7C388366B256 (void);
// 0x00000013 UnityEngine.AndroidJavaObject UnityEngine.AndroidJavaProxy::GetProxyObject()
extern void AndroidJavaProxy_GetProxyObject_mBFD2FBEF9ED9D4AE23DECF5836E5C73A886E2109 (void);
// 0x00000014 System.IntPtr UnityEngine.AndroidJavaProxy::GetRawProxy()
extern void AndroidJavaProxy_GetRawProxy_m685E066A4D378B596CD88385B954AE90CBF328A9 (void);
// 0x00000015 System.Void UnityEngine.AndroidJavaProxy::.cctor()
extern void AndroidJavaProxy__cctor_m44B4074B6A6D8193D86E65DB4C165C2371615D63 (void);
// 0x00000016 System.Void UnityEngine.AndroidJavaObject::.ctor(System.String,System.String[])
extern void AndroidJavaObject__ctor_mB47CA3FC88F645DAB31FB0FAAA32E9159B1DB19E (void);
// 0x00000017 System.Void UnityEngine.AndroidJavaObject::.ctor(System.String,UnityEngine.AndroidJavaObject[])
extern void AndroidJavaObject__ctor_m1F1F88504475490860A246714F36205FB7D53362 (void);
// 0x00000018 System.Void UnityEngine.AndroidJavaObject::.ctor(System.String,UnityEngine.AndroidJavaClass[])
extern void AndroidJavaObject__ctor_m262439771D3A3EFBD18E5D06188D11989D562635 (void);
// 0x00000019 System.Void UnityEngine.AndroidJavaObject::.ctor(System.String,UnityEngine.AndroidJavaProxy[])
extern void AndroidJavaObject__ctor_m0F50ADD04B4BEA5ACB6B614BB206EBFA9353CF6B (void);
// 0x0000001A System.Void UnityEngine.AndroidJavaObject::.ctor(System.String,UnityEngine.AndroidJavaRunnable[])
extern void AndroidJavaObject__ctor_mA61E481C9C0F990FF9BEBFE9E1299612BC174E0E (void);
// 0x0000001B System.Void UnityEngine.AndroidJavaObject::.ctor(System.String,System.Object[])
extern void AndroidJavaObject__ctor_m5A65B5D325C2CEFAC4097A0D3813F8E158178DD7 (void);
// 0x0000001C System.Void UnityEngine.AndroidJavaObject::Dispose()
extern void AndroidJavaObject_Dispose_m2B1593C20B3CE1C8FF95982F638F50985F9DD9E6 (void);
// 0x0000001D System.Void UnityEngine.AndroidJavaObject::Call(System.String,T[])
// 0x0000001E System.Void UnityEngine.AndroidJavaObject::Call(System.String,System.Object[])
extern void AndroidJavaObject_Call_mDEF7846E2AB1C5379069BB21049ED55A9D837B1C (void);
// 0x0000001F System.Void UnityEngine.AndroidJavaObject::CallStatic(System.String,T[])
// 0x00000020 System.Void UnityEngine.AndroidJavaObject::CallStatic(System.String,System.Object[])
extern void AndroidJavaObject_CallStatic_mB677DE04369EDD8E6DECAF2F233116EE1F06555C (void);
// 0x00000021 FieldType UnityEngine.AndroidJavaObject::Get(System.String)
// 0x00000022 System.Void UnityEngine.AndroidJavaObject::Set(System.String,FieldType)
// 0x00000023 FieldType UnityEngine.AndroidJavaObject::GetStatic(System.String)
// 0x00000024 System.Void UnityEngine.AndroidJavaObject::SetStatic(System.String,FieldType)
// 0x00000025 System.IntPtr UnityEngine.AndroidJavaObject::GetRawObject()
extern void AndroidJavaObject_GetRawObject_m536F043B5CE2C21369FF6173C9D2A9A62136BC48 (void);
// 0x00000026 System.IntPtr UnityEngine.AndroidJavaObject::GetRawClass()
extern void AndroidJavaObject_GetRawClass_mE4FB4DC4F856A52E10C6AAD0B65BEBF47B5071F5 (void);
// 0x00000027 UnityEngine.AndroidJavaObject UnityEngine.AndroidJavaObject::CloneReference()
extern void AndroidJavaObject_CloneReference_m6DF6E2BF8D91804B303C93C2026E4A39977E8428 (void);
// 0x00000028 ReturnType UnityEngine.AndroidJavaObject::Call(System.String,T[])
// 0x00000029 ReturnType UnityEngine.AndroidJavaObject::Call(System.String,System.Object[])
// 0x0000002A ReturnType UnityEngine.AndroidJavaObject::CallStatic(System.String,T[])
// 0x0000002B ReturnType UnityEngine.AndroidJavaObject::CallStatic(System.String,System.Object[])
// 0x0000002C System.Void UnityEngine.AndroidJavaObject::DebugPrint(System.String)
extern void AndroidJavaObject_DebugPrint_m047934BF3D1E6676FDDBDA038E1AF387C5413533 (void);
// 0x0000002D System.Void UnityEngine.AndroidJavaObject::DebugPrint(System.String,System.String,System.String,System.Object[])
extern void AndroidJavaObject_DebugPrint_m41CA713464E773016D31C1B6C1489AC34A542CE6 (void);
// 0x0000002E System.Void UnityEngine.AndroidJavaObject::_AndroidJavaObject(System.String,System.Object[])
extern void AndroidJavaObject__AndroidJavaObject_m1284CB7198514B8C06A2BF794ACDC909DC26443F (void);
// 0x0000002F System.Void UnityEngine.AndroidJavaObject::.ctor(System.IntPtr)
extern void AndroidJavaObject__ctor_m0CEE7D570807333CE2C193A82AB3AB8D4F873A6B (void);
// 0x00000030 System.Void UnityEngine.AndroidJavaObject::.ctor()
extern void AndroidJavaObject__ctor_m67B4EEAB015B123D5A3EDCAD914B4795A3B67F04 (void);
// 0x00000031 System.Void UnityEngine.AndroidJavaObject::Finalize()
extern void AndroidJavaObject_Finalize_m87374EE46B27BE3559CACED8A1B62475200AB5AA (void);
// 0x00000032 System.Void UnityEngine.AndroidJavaObject::Dispose(System.Boolean)
extern void AndroidJavaObject_Dispose_m87886676A84FA079C0FE45E6C31D790D764652BE (void);
// 0x00000033 System.Void UnityEngine.AndroidJavaObject::_Call(System.String,System.Object[])
extern void AndroidJavaObject__Call_m4C4D7D7287030773175BDF47681EA018DFA4DF1A (void);
// 0x00000034 ReturnType UnityEngine.AndroidJavaObject::_Call(System.String,System.Object[])
// 0x00000035 FieldType UnityEngine.AndroidJavaObject::_Get(System.String)
// 0x00000036 System.Void UnityEngine.AndroidJavaObject::_Set(System.String,FieldType)
// 0x00000037 System.Void UnityEngine.AndroidJavaObject::_CallStatic(System.String,System.Object[])
extern void AndroidJavaObject__CallStatic_mD63902D30CD5626DAEAD1D6484AF7A9ACA85590E (void);
// 0x00000038 ReturnType UnityEngine.AndroidJavaObject::_CallStatic(System.String,System.Object[])
// 0x00000039 FieldType UnityEngine.AndroidJavaObject::_GetStatic(System.String)
// 0x0000003A System.Void UnityEngine.AndroidJavaObject::_SetStatic(System.String,FieldType)
// 0x0000003B UnityEngine.AndroidJavaObject UnityEngine.AndroidJavaObject::AndroidJavaObjectDeleteLocalRef(System.IntPtr)
extern void AndroidJavaObject_AndroidJavaObjectDeleteLocalRef_m2ECEEAF6389ABB9D6B963B8A98568ECD9413DF3C (void);
// 0x0000003C UnityEngine.AndroidJavaClass UnityEngine.AndroidJavaObject::AndroidJavaClassDeleteLocalRef(System.IntPtr)
extern void AndroidJavaObject_AndroidJavaClassDeleteLocalRef_m56C84D7516BCB51A84E8AFDB3FCA46BAF494548F (void);
// 0x0000003D ReturnType UnityEngine.AndroidJavaObject::FromJavaArrayDeleteLocalRef(System.IntPtr)
// 0x0000003E System.IntPtr UnityEngine.AndroidJavaObject::_GetRawObject()
extern void AndroidJavaObject__GetRawObject_mC5B8B60BEF515F5EE2A113D60991A433DA740C69 (void);
// 0x0000003F System.IntPtr UnityEngine.AndroidJavaObject::_GetRawClass()
extern void AndroidJavaObject__GetRawClass_m470EAEBF8B0BD365FD13F1C6F55119836452FDFA (void);
// 0x00000040 System.Void UnityEngine.AndroidJavaClass::.ctor(System.String)
extern void AndroidJavaClass__ctor_mB5466169E1151B8CC44C8FED234D79984B431389 (void);
// 0x00000041 System.Void UnityEngine.AndroidJavaClass::_AndroidJavaClass(System.String)
extern void AndroidJavaClass__AndroidJavaClass_mF481A9584D78F32C64219FDA49CB84B6F0A017DD (void);
// 0x00000042 System.Void UnityEngine.AndroidJavaClass::.ctor(System.IntPtr)
extern void AndroidJavaClass__ctor_mB206D3CB990755BD56E308F61CD43BB9EA4421D0 (void);
// 0x00000043 System.Boolean UnityEngine.AndroidReflection::IsPrimitive(System.Type)
extern void AndroidReflection_IsPrimitive_mA41A9ECECE3D73679C79DC8B0FDD32B59570DF25 (void);
// 0x00000044 System.Boolean UnityEngine.AndroidReflection::IsAssignableFrom(System.Type,System.Type)
extern void AndroidReflection_IsAssignableFrom_mBAE0D5121AD208959B89E9BDAF62F0E0BAB917C6 (void);
// 0x00000045 System.IntPtr UnityEngine.AndroidReflection::GetStaticMethodID(System.String,System.String,System.String)
extern void AndroidReflection_GetStaticMethodID_mF8378F28D71354360CA9F8AAF8EAF24A3B9594CF (void);
// 0x00000046 System.IntPtr UnityEngine.AndroidReflection::GetMethodID(System.String,System.String,System.String)
extern void AndroidReflection_GetMethodID_m4318CE3C372444B966909092BF046833A9733EE7 (void);
// 0x00000047 System.IntPtr UnityEngine.AndroidReflection::GetConstructorMember(System.IntPtr,System.String)
extern void AndroidReflection_GetConstructorMember_m6380904C6B5AF39F973E79B13B3C2B53DFD759B3 (void);
// 0x00000048 System.IntPtr UnityEngine.AndroidReflection::GetMethodMember(System.IntPtr,System.String,System.String,System.Boolean)
extern void AndroidReflection_GetMethodMember_m2871C6DC2BA1AE5FF3FA448AC36022CC8B19C9EA (void);
// 0x00000049 System.IntPtr UnityEngine.AndroidReflection::GetFieldMember(System.IntPtr,System.String,System.String,System.Boolean)
extern void AndroidReflection_GetFieldMember_m6E589FB4DEDCFEE84B2CFD9C416D0C21EDB09D2F (void);
// 0x0000004A System.IntPtr UnityEngine.AndroidReflection::GetFieldClass(System.IntPtr)
extern void AndroidReflection_GetFieldClass_m88D4993FF662A0EF387AADD915A4FD8054F0DF27 (void);
// 0x0000004B System.String UnityEngine.AndroidReflection::GetFieldSignature(System.IntPtr)
extern void AndroidReflection_GetFieldSignature_mF36DDDDB3E57742AD66487E2D7CECBE81736B259 (void);
// 0x0000004C System.IntPtr UnityEngine.AndroidReflection::NewProxyInstance(System.IntPtr,System.IntPtr,System.IntPtr)
extern void AndroidReflection_NewProxyInstance_m052E9828C670A85F3B9DD9D4632A9A6B52FB5EDF (void);
// 0x0000004D System.IntPtr UnityEngine.AndroidReflection::CreateInvocationError(System.Exception,System.Boolean)
extern void AndroidReflection_CreateInvocationError_m20254A88DCE4AEE011AAC3D42B94DC7B588AB20F (void);
// 0x0000004E System.Void UnityEngine.AndroidReflection::.cctor()
extern void AndroidReflection__cctor_m59365F63F057115EA8FBCB0AFBAF94012DAAC622 (void);
// 0x0000004F System.IntPtr UnityEngine._AndroidJNIHelper::CreateJavaProxy(System.IntPtr,System.IntPtr,UnityEngine.AndroidJavaProxy)
extern void _AndroidJNIHelper_CreateJavaProxy_mAC059F7C2716D4E9099EA7268F5378260E6DDF36 (void);
// 0x00000050 System.IntPtr UnityEngine._AndroidJNIHelper::CreateJavaRunnable(UnityEngine.AndroidJavaRunnable)
extern void _AndroidJNIHelper_CreateJavaRunnable_m25981D4249F420FA78240CDC2F7E53B9EAD5F31A (void);
// 0x00000051 System.IntPtr UnityEngine._AndroidJNIHelper::InvokeJavaProxyMethod(UnityEngine.AndroidJavaProxy,System.IntPtr,System.IntPtr)
extern void _AndroidJNIHelper_InvokeJavaProxyMethod_mCA9BD35B5AE99B6DFABBFE9968DD1EBE931F70C2 (void);
// 0x00000052 UnityEngine.jvalue[] UnityEngine._AndroidJNIHelper::CreateJNIArgArray(System.Object[])
extern void _AndroidJNIHelper_CreateJNIArgArray_mFB22FA0559B96CAA29D2A63EA56EF62F0280AA39 (void);
// 0x00000053 System.Object UnityEngine._AndroidJNIHelper::UnboxArray(UnityEngine.AndroidJavaObject)
extern void _AndroidJNIHelper_UnboxArray_m5A46896F07017C9B0908AE3D72DA1285071DEA4A (void);
// 0x00000054 System.Object UnityEngine._AndroidJNIHelper::Unbox(UnityEngine.AndroidJavaObject)
extern void _AndroidJNIHelper_Unbox_m7D786B14E4A90E5411FC9AA7F985451DEBC95731 (void);
// 0x00000055 UnityEngine.AndroidJavaObject UnityEngine._AndroidJNIHelper::Box(System.Object)
extern void _AndroidJNIHelper_Box_mA0B7B8444C6AC2BABBDC7B1A7FACDA3EA6F816B0 (void);
// 0x00000056 System.Void UnityEngine._AndroidJNIHelper::DeleteJNIArgArray(System.Object[],UnityEngine.jvalue[])
extern void _AndroidJNIHelper_DeleteJNIArgArray_m3920D9869CEC7D9898B83ADC4457509D94897473 (void);
// 0x00000057 System.IntPtr UnityEngine._AndroidJNIHelper::ConvertToJNIArray(System.Array)
extern void _AndroidJNIHelper_ConvertToJNIArray_m6E451CD0C15DE2812F103C604CB1EF1D358D1213 (void);
// 0x00000058 ArrayType UnityEngine._AndroidJNIHelper::ConvertFromJNIArray(System.IntPtr)
// 0x00000059 System.IntPtr UnityEngine._AndroidJNIHelper::GetConstructorID(System.IntPtr,System.Object[])
extern void _AndroidJNIHelper_GetConstructorID_mD9CF17C7FA280BED12A8BB5D6F12E0D2C6778C3F (void);
// 0x0000005A System.IntPtr UnityEngine._AndroidJNIHelper::GetMethodID(System.IntPtr,System.String,System.Object[],System.Boolean)
extern void _AndroidJNIHelper_GetMethodID_m185ABE7B65325FA580775AF81FE17687994C98DD (void);
// 0x0000005B System.IntPtr UnityEngine._AndroidJNIHelper::GetMethodID(System.IntPtr,System.String,System.Object[],System.Boolean)
// 0x0000005C System.IntPtr UnityEngine._AndroidJNIHelper::GetFieldID(System.IntPtr,System.String,System.Boolean)
// 0x0000005D System.IntPtr UnityEngine._AndroidJNIHelper::GetConstructorID(System.IntPtr,System.String)
extern void _AndroidJNIHelper_GetConstructorID_m89A45100B1A05DEBF6586AB234A7DBE2044B2490 (void);
// 0x0000005E System.IntPtr UnityEngine._AndroidJNIHelper::GetMethodID(System.IntPtr,System.String,System.String,System.Boolean)
extern void _AndroidJNIHelper_GetMethodID_mECADE8D5FC2712DED913CDECBB9D7E8C46812184 (void);
// 0x0000005F System.IntPtr UnityEngine._AndroidJNIHelper::GetMethodIDFallback(System.IntPtr,System.String,System.String,System.Boolean)
extern void _AndroidJNIHelper_GetMethodIDFallback_m7155B2DE7C4F4E71C6AE29F856A1A9618ADAAD4F (void);
// 0x00000060 System.IntPtr UnityEngine._AndroidJNIHelper::GetFieldID(System.IntPtr,System.String,System.String,System.Boolean)
extern void _AndroidJNIHelper_GetFieldID_m698CE89D968CF7A131861E95FF72272A6A14FB95 (void);
// 0x00000061 System.String UnityEngine._AndroidJNIHelper::GetSignature(System.Object)
extern void _AndroidJNIHelper_GetSignature_m414A7A6B98FB5565075E4C51F22C482F2BCEEF5F (void);
// 0x00000062 System.String UnityEngine._AndroidJNIHelper::GetSignature(System.Object[])
extern void _AndroidJNIHelper_GetSignature_m309D35CC998B59CFEDA2D6EB5D0E221B3F1374F7 (void);
// 0x00000063 System.String UnityEngine._AndroidJNIHelper::GetSignature(System.Object[])
// 0x00000064 System.Void UnityEngine._AndroidJNIHelper::.ctor()
extern void _AndroidJNIHelper__ctor_m0535562F59B589E117E57B8EA07ECE900848F509 (void);
// 0x00000065 System.Boolean UnityEngine.AndroidJNIHelper::get_debug()
extern void AndroidJNIHelper_get_debug_m0F67D4E412EBE1D511C42F5F12294C63E669C907 (void);
// 0x00000066 System.Void UnityEngine.AndroidJNIHelper::set_debug(System.Boolean)
extern void AndroidJNIHelper_set_debug_m66E40D20DC9DC69BD7B139DA5C367BA1185FCA3F (void);
// 0x00000067 System.IntPtr UnityEngine.AndroidJNIHelper::GetConstructorID(System.IntPtr)
extern void AndroidJNIHelper_GetConstructorID_mCF5EAC779FFBD1129C2E28FE1C2171E6FF8AAE95 (void);
// 0x00000068 System.IntPtr UnityEngine.AndroidJNIHelper::GetConstructorID(System.IntPtr,System.String)
extern void AndroidJNIHelper_GetConstructorID_m2D883140A087C1CDB74FE9195D14643CB9A854F0 (void);
// 0x00000069 System.IntPtr UnityEngine.AndroidJNIHelper::GetMethodID(System.IntPtr,System.String)
extern void AndroidJNIHelper_GetMethodID_m5D0526B2FE20191F966D72521647D686980EE06B (void);
// 0x0000006A System.IntPtr UnityEngine.AndroidJNIHelper::GetMethodID(System.IntPtr,System.String,System.String)
extern void AndroidJNIHelper_GetMethodID_m58B68CA9B567BF23064BD8891CEA509FF8FA7C76 (void);
// 0x0000006B System.IntPtr UnityEngine.AndroidJNIHelper::GetMethodID(System.IntPtr,System.String,System.String,System.Boolean)
extern void AndroidJNIHelper_GetMethodID_mDB705DC228B1BB30E6595068797FB3F2A1817BB8 (void);
// 0x0000006C System.IntPtr UnityEngine.AndroidJNIHelper::GetFieldID(System.IntPtr,System.String)
extern void AndroidJNIHelper_GetFieldID_mB209E2F32D04CA5BCD7D6EC10A0C8DC0ED5D0D3B (void);
// 0x0000006D System.IntPtr UnityEngine.AndroidJNIHelper::GetFieldID(System.IntPtr,System.String,System.String)
extern void AndroidJNIHelper_GetFieldID_m736A1E39AEE90F1DD3C352FA3F1988D4CFD9778F (void);
// 0x0000006E System.IntPtr UnityEngine.AndroidJNIHelper::GetFieldID(System.IntPtr,System.String,System.String,System.Boolean)
extern void AndroidJNIHelper_GetFieldID_mDA4775DFA37126A7EFEBCA8E68EF9640F3D6EF01 (void);
// 0x0000006F System.IntPtr UnityEngine.AndroidJNIHelper::CreateJavaRunnable(UnityEngine.AndroidJavaRunnable)
extern void AndroidJNIHelper_CreateJavaRunnable_mBEE8C5060C69EE5C6922D4BA06C261055A3FF99F (void);
// 0x00000070 System.IntPtr UnityEngine.AndroidJNIHelper::CreateJavaProxy(UnityEngine.AndroidJavaProxy)
extern void AndroidJNIHelper_CreateJavaProxy_m75CA3C0BF15517CD52658E08F9FCBA1022822E6F (void);
// 0x00000071 System.IntPtr UnityEngine.AndroidJNIHelper::ConvertToJNIArray(System.Array)
extern void AndroidJNIHelper_ConvertToJNIArray_mBEAE4605FF297D19AFB8CE4E8443C9C0F87E9A13 (void);
// 0x00000072 UnityEngine.jvalue[] UnityEngine.AndroidJNIHelper::CreateJNIArgArray(System.Object[])
extern void AndroidJNIHelper_CreateJNIArgArray_mC12C279EEB43D740F42C5E90301DC54F6E04B876 (void);
// 0x00000073 System.Void UnityEngine.AndroidJNIHelper::DeleteJNIArgArray(System.Object[],UnityEngine.jvalue[])
extern void AndroidJNIHelper_DeleteJNIArgArray_mBDC874B32FF09E6B48E18B2A58794C0A4DE2FA23 (void);
// 0x00000074 System.IntPtr UnityEngine.AndroidJNIHelper::GetConstructorID(System.IntPtr,System.Object[])
extern void AndroidJNIHelper_GetConstructorID_m0FDAC24E463246206BA8FBDE44B4A73D6CBF3D40 (void);
// 0x00000075 System.IntPtr UnityEngine.AndroidJNIHelper::GetMethodID(System.IntPtr,System.String,System.Object[],System.Boolean)
extern void AndroidJNIHelper_GetMethodID_m283E294AA8DD3F6721A4173CE4C31038B98AA7E5 (void);
// 0x00000076 System.String UnityEngine.AndroidJNIHelper::GetSignature(System.Object)
extern void AndroidJNIHelper_GetSignature_mE8DBE8ABB6506BBF2806880DA2FBB876DD88A047 (void);
// 0x00000077 System.String UnityEngine.AndroidJNIHelper::GetSignature(System.Object[])
extern void AndroidJNIHelper_GetSignature_m1B2D7B48C9E9D7BB1F10AD60D541EA7F9BE18F45 (void);
// 0x00000078 ArrayType UnityEngine.AndroidJNIHelper::ConvertFromJNIArray(System.IntPtr)
// 0x00000079 System.IntPtr UnityEngine.AndroidJNIHelper::GetMethodID(System.IntPtr,System.String,System.Object[],System.Boolean)
// 0x0000007A System.IntPtr UnityEngine.AndroidJNIHelper::GetFieldID(System.IntPtr,System.String,System.Boolean)
// 0x0000007B System.String UnityEngine.AndroidJNIHelper::GetSignature(System.Object[])
// 0x0000007C System.Int32 UnityEngine.AndroidJNI::AttachCurrentThread()
extern void AndroidJNI_AttachCurrentThread_m412647220ED1F7D42F9D41F692D1D7DC4696B6D4 (void);
// 0x0000007D System.Int32 UnityEngine.AndroidJNI::DetachCurrentThread()
extern void AndroidJNI_DetachCurrentThread_mC0F3D8974C0681528B801EF0AC2A54E5C7A2E933 (void);
// 0x0000007E System.Int32 UnityEngine.AndroidJNI::GetVersion()
extern void AndroidJNI_GetVersion_m8609243FCDE009D1C5DEC7C7E40664386D5C4608 (void);
// 0x0000007F System.IntPtr UnityEngine.AndroidJNI::FindClass(System.String)
extern void AndroidJNI_FindClass_m6E9908108F8D828563AD510C5A355E71A9A493E9 (void);
// 0x00000080 System.IntPtr UnityEngine.AndroidJNI::FromReflectedMethod(System.IntPtr)
extern void AndroidJNI_FromReflectedMethod_m1613634C8A528B0A2A1F6828B609F31792B7DF43 (void);
// 0x00000081 System.IntPtr UnityEngine.AndroidJNI::FromReflectedField(System.IntPtr)
extern void AndroidJNI_FromReflectedField_m88709F5C3EDD6CB503B1C6EE32CFF6BC328171E8 (void);
// 0x00000082 System.IntPtr UnityEngine.AndroidJNI::ToReflectedMethod(System.IntPtr,System.IntPtr,System.Boolean)
extern void AndroidJNI_ToReflectedMethod_m50B8B87366FDAB0506EF2023352AB602E14CA7E2 (void);
// 0x00000083 System.IntPtr UnityEngine.AndroidJNI::ToReflectedField(System.IntPtr,System.IntPtr,System.Boolean)
extern void AndroidJNI_ToReflectedField_mEE3F5294258CD32FB6066A2EB256B75B04B19D3B (void);
// 0x00000084 System.IntPtr UnityEngine.AndroidJNI::GetSuperclass(System.IntPtr)
extern void AndroidJNI_GetSuperclass_m755BF9AA2AADBE96A2C64F15AE8114BD1807AD6A (void);
// 0x00000085 System.Boolean UnityEngine.AndroidJNI::IsAssignableFrom(System.IntPtr,System.IntPtr)
extern void AndroidJNI_IsAssignableFrom_m28DB6B523875A7837F7CC60807F32D58E90F9C25 (void);
// 0x00000086 System.Int32 UnityEngine.AndroidJNI::Throw(System.IntPtr)
extern void AndroidJNI_Throw_m7DE4EC0AF3864EDC05EE828A57B20A1EB0C71F5E (void);
// 0x00000087 System.Int32 UnityEngine.AndroidJNI::ThrowNew(System.IntPtr,System.String)
extern void AndroidJNI_ThrowNew_mDEAF3A3716A9F8D6A9DF48B6928D17740C0112AE (void);
// 0x00000088 System.IntPtr UnityEngine.AndroidJNI::ExceptionOccurred()
extern void AndroidJNI_ExceptionOccurred_mAE2AE7C57E06059383EDCAB956A01BDF7D03F54C (void);
// 0x00000089 System.Void UnityEngine.AndroidJNI::ExceptionDescribe()
extern void AndroidJNI_ExceptionDescribe_m20B24A293E6A8467E312E028E6AA250934684508 (void);
// 0x0000008A System.Void UnityEngine.AndroidJNI::ExceptionClear()
extern void AndroidJNI_ExceptionClear_m1205CD178ADB8E0E7EBDBE349AFA767D5D7758B8 (void);
// 0x0000008B System.Void UnityEngine.AndroidJNI::FatalError(System.String)
extern void AndroidJNI_FatalError_m6F93C97D6802B5FFB6A6A99D16C74234E79AF5D6 (void);
// 0x0000008C System.Int32 UnityEngine.AndroidJNI::PushLocalFrame(System.Int32)
extern void AndroidJNI_PushLocalFrame_m2D8050A3799AEBB4A7E506E6790839EB66932E10 (void);
// 0x0000008D System.IntPtr UnityEngine.AndroidJNI::PopLocalFrame(System.IntPtr)
extern void AndroidJNI_PopLocalFrame_m32AF6F9065F09D80BFDD3F573B62C782F392E609 (void);
// 0x0000008E System.IntPtr UnityEngine.AndroidJNI::NewGlobalRef(System.IntPtr)
extern void AndroidJNI_NewGlobalRef_m9A06F23234FB1ECF3F482AF3A6A6148A5916E9A7 (void);
// 0x0000008F System.Void UnityEngine.AndroidJNI::DeleteGlobalRef(System.IntPtr)
extern void AndroidJNI_DeleteGlobalRef_mC50B6C056F32BB9F44B800949FA169C728D4C41D (void);
// 0x00000090 System.IntPtr UnityEngine.AndroidJNI::NewWeakGlobalRef(System.IntPtr)
extern void AndroidJNI_NewWeakGlobalRef_m27D74DAC1D1F0A11796E4FA669D8CB8DBB1AF2BA (void);
// 0x00000091 System.Void UnityEngine.AndroidJNI::DeleteWeakGlobalRef(System.IntPtr)
extern void AndroidJNI_DeleteWeakGlobalRef_mA1F19C1656B86A22A339497C335C156648736E6D (void);
// 0x00000092 System.IntPtr UnityEngine.AndroidJNI::NewLocalRef(System.IntPtr)
extern void AndroidJNI_NewLocalRef_m286E59F912B94D07D1CE54DFE93A631B2162CD65 (void);
// 0x00000093 System.Void UnityEngine.AndroidJNI::DeleteLocalRef(System.IntPtr)
extern void AndroidJNI_DeleteLocalRef_mD2A2B4F1C17A4F5863BB94F88F268E72FD120DBB (void);
// 0x00000094 System.Boolean UnityEngine.AndroidJNI::IsSameObject(System.IntPtr,System.IntPtr)
extern void AndroidJNI_IsSameObject_mA37D2BE7C0E40F30E6D438A937B038E7703DFDAB (void);
// 0x00000095 System.Int32 UnityEngine.AndroidJNI::EnsureLocalCapacity(System.Int32)
extern void AndroidJNI_EnsureLocalCapacity_mD27645E03F7F82811D5AFFF6B068E226C9C93227 (void);
// 0x00000096 System.IntPtr UnityEngine.AndroidJNI::AllocObject(System.IntPtr)
extern void AndroidJNI_AllocObject_m5E81D0A72F3DCDFC5D0AD62C2B93816083EE3926 (void);
// 0x00000097 System.IntPtr UnityEngine.AndroidJNI::NewObject(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_NewObject_mF026198FBA1D5E69719DEB52F41E9FDB8B7F93A4 (void);
// 0x00000098 System.IntPtr UnityEngine.AndroidJNI::GetObjectClass(System.IntPtr)
extern void AndroidJNI_GetObjectClass_m418C2D7DAE432AD104209833A0DF06B16E1E74B5 (void);
// 0x00000099 System.Boolean UnityEngine.AndroidJNI::IsInstanceOf(System.IntPtr,System.IntPtr)
extern void AndroidJNI_IsInstanceOf_m09B386C15D3FBD4A6589D9F6DD3E5F8D259F51B1 (void);
// 0x0000009A System.IntPtr UnityEngine.AndroidJNI::GetMethodID(System.IntPtr,System.String,System.String)
extern void AndroidJNI_GetMethodID_mA7FF961764CA4D68C4789E5A17926CE5FF9B3549 (void);
// 0x0000009B System.IntPtr UnityEngine.AndroidJNI::GetFieldID(System.IntPtr,System.String,System.String)
extern void AndroidJNI_GetFieldID_mC6BFB1F17EF5FC82D45F08D25C11D346E51673F2 (void);
// 0x0000009C System.IntPtr UnityEngine.AndroidJNI::GetStaticMethodID(System.IntPtr,System.String,System.String)
extern void AndroidJNI_GetStaticMethodID_mA13B58796C4E210B46956723FE664B6D0130C5A3 (void);
// 0x0000009D System.IntPtr UnityEngine.AndroidJNI::GetStaticFieldID(System.IntPtr,System.String,System.String)
extern void AndroidJNI_GetStaticFieldID_m89476A442BF57C00C1CBB0DA588077C2B4171654 (void);
// 0x0000009E System.IntPtr UnityEngine.AndroidJNI::NewString(System.String)
extern void AndroidJNI_NewString_m6F3143989EFE907B5D0091850D1754421795A39B (void);
// 0x0000009F System.IntPtr UnityEngine.AndroidJNI::NewStringFromStr(System.String)
extern void AndroidJNI_NewStringFromStr_mF56D4A6456A326916DEF2E148E826F6EBC93B3CC (void);
// 0x000000A0 System.IntPtr UnityEngine.AndroidJNI::NewString(System.Char[])
extern void AndroidJNI_NewString_m74F9DCF2F56735C9BE6DC3831134A96C7BCA9F9F (void);
// 0x000000A1 System.IntPtr UnityEngine.AndroidJNI::NewStringUTF(System.String)
extern void AndroidJNI_NewStringUTF_mB6A0A0A1C3F931503A6329D4E232DB501B95B5B0 (void);
// 0x000000A2 System.String UnityEngine.AndroidJNI::GetStringChars(System.IntPtr)
extern void AndroidJNI_GetStringChars_mB61E4F713A2457F7DF053DBFE4455A559DA9623C (void);
// 0x000000A3 System.Int32 UnityEngine.AndroidJNI::GetStringLength(System.IntPtr)
extern void AndroidJNI_GetStringLength_mD2A44213EB9B131E1DECEF34A315F2B817384760 (void);
// 0x000000A4 System.Int32 UnityEngine.AndroidJNI::GetStringUTFLength(System.IntPtr)
extern void AndroidJNI_GetStringUTFLength_m5BE0CC7EE4108BA654A9F7647E66871D831B812A (void);
// 0x000000A5 System.String UnityEngine.AndroidJNI::GetStringUTFChars(System.IntPtr)
extern void AndroidJNI_GetStringUTFChars_m61CFD69CF31D17C2728F23656079D3E26D9D2BBB (void);
// 0x000000A6 System.String UnityEngine.AndroidJNI::CallStringMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStringMethod_m52FAF2826B75AF2AEA8F848AEC973A682216EC4C (void);
// 0x000000A7 System.IntPtr UnityEngine.AndroidJNI::CallObjectMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallObjectMethod_m21944B19534CA3EB8885BE3F1057E6A72AAC4355 (void);
// 0x000000A8 System.Int32 UnityEngine.AndroidJNI::CallIntMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallIntMethod_mD1299CB9F99E26D2734A0F02D376DE6EF49F1741 (void);
// 0x000000A9 System.Boolean UnityEngine.AndroidJNI::CallBooleanMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallBooleanMethod_m78FC31B29854285F1164C75974AB463FE5716F84 (void);
// 0x000000AA System.Int16 UnityEngine.AndroidJNI::CallShortMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallShortMethod_mC3C11BD9E32604C41710756104D5A8AFCFA792E4 (void);
// 0x000000AB System.Byte UnityEngine.AndroidJNI::CallByteMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallByteMethod_m7E2B355303C36B0992364D036BFCCF1CB4DD8154 (void);
// 0x000000AC System.SByte UnityEngine.AndroidJNI::CallSByteMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallSByteMethod_mA98E61BB8186A06CBF4A175E29E2F0F194FB8507 (void);
// 0x000000AD System.Char UnityEngine.AndroidJNI::CallCharMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallCharMethod_m560F0E113CA6E4F4A73BDAF93D53DADDA8D2047B (void);
// 0x000000AE System.Single UnityEngine.AndroidJNI::CallFloatMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallFloatMethod_mF94056CFCC7E045F7B350D2D3285335482A2AE8E (void);
// 0x000000AF System.Double UnityEngine.AndroidJNI::CallDoubleMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallDoubleMethod_m9001B2EF56623D6F17B4E9E87788CDD3E760A897 (void);
// 0x000000B0 System.Int64 UnityEngine.AndroidJNI::CallLongMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallLongMethod_mDE82FA5F26CBE0E0F8251D3A7698D376524E1A4B (void);
// 0x000000B1 System.Void UnityEngine.AndroidJNI::CallVoidMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallVoidMethod_mFCFF6A5FF4A51305C3D2DBFB3A0699617EF40D48 (void);
// 0x000000B2 System.String UnityEngine.AndroidJNI::GetStringField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStringField_m6A2FA57794ADA8735B103FF5D4819F3C4813992F (void);
// 0x000000B3 System.IntPtr UnityEngine.AndroidJNI::GetObjectField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetObjectField_m5E3C5FF1582F4A62155220FDCB849574C0E36AD6 (void);
// 0x000000B4 System.Boolean UnityEngine.AndroidJNI::GetBooleanField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetBooleanField_mC900428E2FD1E55BA21808A31B760FB0F10BC047 (void);
// 0x000000B5 System.Byte UnityEngine.AndroidJNI::GetByteField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetByteField_m675BEFB1024363DE587C000406B8A09C5762B847 (void);
// 0x000000B6 System.SByte UnityEngine.AndroidJNI::GetSByteField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetSByteField_mB81412A4748ABB311535E2B73092569AAE6CB735 (void);
// 0x000000B7 System.Char UnityEngine.AndroidJNI::GetCharField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetCharField_m7DA94A98ED33A97EE7874872C9F51515F151F019 (void);
// 0x000000B8 System.Int16 UnityEngine.AndroidJNI::GetShortField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetShortField_m5EEACBB3920623AD54D9DF77499E8BA92B35E3D8 (void);
// 0x000000B9 System.Int32 UnityEngine.AndroidJNI::GetIntField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetIntField_m6B78A3F6F8EE6D1ADEDECF1EC145BC9C5AE37E88 (void);
// 0x000000BA System.Int64 UnityEngine.AndroidJNI::GetLongField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetLongField_mC2DC315C44320CE9A406B95A4CAA1117A0FF56A8 (void);
// 0x000000BB System.Single UnityEngine.AndroidJNI::GetFloatField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetFloatField_m5C92103D7307A19F72F28DD40118F84D91C19A39 (void);
// 0x000000BC System.Double UnityEngine.AndroidJNI::GetDoubleField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetDoubleField_mE2B23D9F1363B48811B6207BEF8A18B39CB3B22B (void);
// 0x000000BD System.Void UnityEngine.AndroidJNI::SetStringField(System.IntPtr,System.IntPtr,System.String)
extern void AndroidJNI_SetStringField_mE9B2983BC7C61C0EEEA3FA31484B570B48E210DC (void);
// 0x000000BE System.Void UnityEngine.AndroidJNI::SetObjectField(System.IntPtr,System.IntPtr,System.IntPtr)
extern void AndroidJNI_SetObjectField_m6BA777B66D76ECD1E34D69D800A8F2F51C51026C (void);
// 0x000000BF System.Void UnityEngine.AndroidJNI::SetBooleanField(System.IntPtr,System.IntPtr,System.Boolean)
extern void AndroidJNI_SetBooleanField_m9A22242BD25A8B3802C05F70C2EB5ACE7E8BF2A0 (void);
// 0x000000C0 System.Void UnityEngine.AndroidJNI::SetByteField(System.IntPtr,System.IntPtr,System.Byte)
extern void AndroidJNI_SetByteField_mA1EBC3D4A3734B8064330782DC240DD1775C7C4B (void);
// 0x000000C1 System.Void UnityEngine.AndroidJNI::SetSByteField(System.IntPtr,System.IntPtr,System.SByte)
extern void AndroidJNI_SetSByteField_mA360303CC36670BDC442E1366D64333A5363D09F (void);
// 0x000000C2 System.Void UnityEngine.AndroidJNI::SetCharField(System.IntPtr,System.IntPtr,System.Char)
extern void AndroidJNI_SetCharField_m86543FBDB219D090EFEA141F679CCD22E195B680 (void);
// 0x000000C3 System.Void UnityEngine.AndroidJNI::SetShortField(System.IntPtr,System.IntPtr,System.Int16)
extern void AndroidJNI_SetShortField_m8811FC677647B47F855FC9533D51437517B53066 (void);
// 0x000000C4 System.Void UnityEngine.AndroidJNI::SetIntField(System.IntPtr,System.IntPtr,System.Int32)
extern void AndroidJNI_SetIntField_mCAB8E0B5C4F1773F6CAF81731DFB224FB78F0138 (void);
// 0x000000C5 System.Void UnityEngine.AndroidJNI::SetLongField(System.IntPtr,System.IntPtr,System.Int64)
extern void AndroidJNI_SetLongField_m763D39D8B341907F54921AF5EFE4E0C37EB44B00 (void);
// 0x000000C6 System.Void UnityEngine.AndroidJNI::SetFloatField(System.IntPtr,System.IntPtr,System.Single)
extern void AndroidJNI_SetFloatField_m953B7DC631E56432E2AB59154CAC15EE10B28E02 (void);
// 0x000000C7 System.Void UnityEngine.AndroidJNI::SetDoubleField(System.IntPtr,System.IntPtr,System.Double)
extern void AndroidJNI_SetDoubleField_m30FB81E1DEE48DB3C22117F3D017813A204B474D (void);
// 0x000000C8 System.String UnityEngine.AndroidJNI::CallStaticStringMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticStringMethod_m7E8E3AFF8296764C324060E65B052B23500C18AB (void);
// 0x000000C9 System.IntPtr UnityEngine.AndroidJNI::CallStaticObjectMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticObjectMethod_m5D0C02761602E6ED1AE4FAD90B7762A6376A35D5 (void);
// 0x000000CA System.Int32 UnityEngine.AndroidJNI::CallStaticIntMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticIntMethod_m7AA48D4603F398E99E45DF3E057BB58FB6D374FC (void);
// 0x000000CB System.Boolean UnityEngine.AndroidJNI::CallStaticBooleanMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticBooleanMethod_m6D035B0525AF900D6BF3F91C174C093FE2531453 (void);
// 0x000000CC System.Int16 UnityEngine.AndroidJNI::CallStaticShortMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticShortMethod_m10E61636B448E36034CD6DE902A286B0257C7586 (void);
// 0x000000CD System.Byte UnityEngine.AndroidJNI::CallStaticByteMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticByteMethod_mFC6A3C4731086DD6CD298C5EDC86C0211B90C69C (void);
// 0x000000CE System.SByte UnityEngine.AndroidJNI::CallStaticSByteMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticSByteMethod_mA2BA2E72F9CF73A04E15420CDCCFD7CFD809E0DA (void);
// 0x000000CF System.Char UnityEngine.AndroidJNI::CallStaticCharMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticCharMethod_mCC7C287BA2CD34A0526577D194237277F9708353 (void);
// 0x000000D0 System.Single UnityEngine.AndroidJNI::CallStaticFloatMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticFloatMethod_m19DBC375D9E707CA40BE8D997C84F553EF71040A (void);
// 0x000000D1 System.Double UnityEngine.AndroidJNI::CallStaticDoubleMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticDoubleMethod_m052484ED56097C439FA22A89CA0FE393BBFFD305 (void);
// 0x000000D2 System.Int64 UnityEngine.AndroidJNI::CallStaticLongMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticLongMethod_mC103D7C5C92E7DC15B7AC043BD5D7FE398F559AC (void);
// 0x000000D3 System.Void UnityEngine.AndroidJNI::CallStaticVoidMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNI_CallStaticVoidMethod_m2DB4A797A541A547320D853111F8D1E5D27D9C5E (void);
// 0x000000D4 System.String UnityEngine.AndroidJNI::GetStaticStringField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticStringField_mFE7F821C85A677C32C199BB9B23CEB66A523A977 (void);
// 0x000000D5 System.IntPtr UnityEngine.AndroidJNI::GetStaticObjectField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticObjectField_m52268140CD4BD65B9FAC976669DBBD65D763731C (void);
// 0x000000D6 System.Boolean UnityEngine.AndroidJNI::GetStaticBooleanField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticBooleanField_m6BC154F7001DA04748F5F96F61878A3D6205ECA4 (void);
// 0x000000D7 System.Byte UnityEngine.AndroidJNI::GetStaticByteField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticByteField_m1817BBECBE096B84C719026A308F3F0961025070 (void);
// 0x000000D8 System.SByte UnityEngine.AndroidJNI::GetStaticSByteField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticSByteField_m0A5D05E28F47C16783818258361281644C5C6585 (void);
// 0x000000D9 System.Char UnityEngine.AndroidJNI::GetStaticCharField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticCharField_m3B1D9B99424A25FB6F665DA504125C0F20CEC0BF (void);
// 0x000000DA System.Int16 UnityEngine.AndroidJNI::GetStaticShortField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticShortField_m66353DB84BAFDD75B35914D8AA5056AC6B3C0BDB (void);
// 0x000000DB System.Int32 UnityEngine.AndroidJNI::GetStaticIntField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticIntField_m039F7CB6BD326410250D18A49836F55CD1DD87F9 (void);
// 0x000000DC System.Int64 UnityEngine.AndroidJNI::GetStaticLongField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticLongField_mD403EAC792740D06B021D1E9D34D25CAFEE59194 (void);
// 0x000000DD System.Single UnityEngine.AndroidJNI::GetStaticFloatField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticFloatField_m68704C9BF92DF84E6982FCB03EAC0935F3934399 (void);
// 0x000000DE System.Double UnityEngine.AndroidJNI::GetStaticDoubleField(System.IntPtr,System.IntPtr)
extern void AndroidJNI_GetStaticDoubleField_mF882F4F690FE87E2A81D8779BB62C905DC217700 (void);
// 0x000000DF System.Void UnityEngine.AndroidJNI::SetStaticStringField(System.IntPtr,System.IntPtr,System.String)
extern void AndroidJNI_SetStaticStringField_m42D87A914D4AD4DAE0B66661BAE6B708F4ED3AF8 (void);
// 0x000000E0 System.Void UnityEngine.AndroidJNI::SetStaticObjectField(System.IntPtr,System.IntPtr,System.IntPtr)
extern void AndroidJNI_SetStaticObjectField_mFA62D317DCE3F48E5D63FBA0AC3464977C0459A3 (void);
// 0x000000E1 System.Void UnityEngine.AndroidJNI::SetStaticBooleanField(System.IntPtr,System.IntPtr,System.Boolean)
extern void AndroidJNI_SetStaticBooleanField_m18BC2337480DF6ED4F40F9B674D5DAA19225F3D2 (void);
// 0x000000E2 System.Void UnityEngine.AndroidJNI::SetStaticByteField(System.IntPtr,System.IntPtr,System.Byte)
extern void AndroidJNI_SetStaticByteField_m22249B7319EA4C4751995AFAE6CB41317EAF7190 (void);
// 0x000000E3 System.Void UnityEngine.AndroidJNI::SetStaticSByteField(System.IntPtr,System.IntPtr,System.SByte)
extern void AndroidJNI_SetStaticSByteField_mCA2EA01B1AD261349CD5BE1E3F7AD43A8596837C (void);
// 0x000000E4 System.Void UnityEngine.AndroidJNI::SetStaticCharField(System.IntPtr,System.IntPtr,System.Char)
extern void AndroidJNI_SetStaticCharField_m73B891760479DB05E0C3EC3D60F90D4503AA67A0 (void);
// 0x000000E5 System.Void UnityEngine.AndroidJNI::SetStaticShortField(System.IntPtr,System.IntPtr,System.Int16)
extern void AndroidJNI_SetStaticShortField_mBC6DC87D8235408532C23E5CDB19C178C5F2D77E (void);
// 0x000000E6 System.Void UnityEngine.AndroidJNI::SetStaticIntField(System.IntPtr,System.IntPtr,System.Int32)
extern void AndroidJNI_SetStaticIntField_mCBEADFA609B9541779AC51B1FE200B50D51C10F7 (void);
// 0x000000E7 System.Void UnityEngine.AndroidJNI::SetStaticLongField(System.IntPtr,System.IntPtr,System.Int64)
extern void AndroidJNI_SetStaticLongField_mEEDC663D7A85F4DAEE0CA7935EF5E27CD377E5FA (void);
// 0x000000E8 System.Void UnityEngine.AndroidJNI::SetStaticFloatField(System.IntPtr,System.IntPtr,System.Single)
extern void AndroidJNI_SetStaticFloatField_m836FCB6A7FB278526013F0C7BCAFCE33F2D7C016 (void);
// 0x000000E9 System.Void UnityEngine.AndroidJNI::SetStaticDoubleField(System.IntPtr,System.IntPtr,System.Double)
extern void AndroidJNI_SetStaticDoubleField_mF7F4869A92C98895DC79F0EEBEB0DA0C576CBDF7 (void);
// 0x000000EA System.IntPtr UnityEngine.AndroidJNI::ToBooleanArray(System.Boolean[])
extern void AndroidJNI_ToBooleanArray_m06017AECA409DC3207C993113FA1F88277F1D71B (void);
// 0x000000EB System.IntPtr UnityEngine.AndroidJNI::ToByteArray(System.Byte[])
extern void AndroidJNI_ToByteArray_mE72C1AF34FE140D36F10A0386454137D4550FBDD (void);
// 0x000000EC System.IntPtr UnityEngine.AndroidJNI::ToSByteArray(System.SByte[])
extern void AndroidJNI_ToSByteArray_m1307FD21FE087877599392D80474D56EA03AA0B8 (void);
// 0x000000ED System.IntPtr UnityEngine.AndroidJNI::ToCharArray(System.Char[])
extern void AndroidJNI_ToCharArray_mF58CDE4DA760CD4518E8F5F4DAD7A885B7166C5B (void);
// 0x000000EE System.IntPtr UnityEngine.AndroidJNI::ToShortArray(System.Int16[])
extern void AndroidJNI_ToShortArray_m28E2EB565D92A8A0396646B0E3EBFF80A7E10246 (void);
// 0x000000EF System.IntPtr UnityEngine.AndroidJNI::ToIntArray(System.Int32[])
extern void AndroidJNI_ToIntArray_mA5B1AF83EE6484437CABB1485875E86A5EAA8208 (void);
// 0x000000F0 System.IntPtr UnityEngine.AndroidJNI::ToLongArray(System.Int64[])
extern void AndroidJNI_ToLongArray_m53576F1D2526D6021B07FF19F4F3C220B13A4A92 (void);
// 0x000000F1 System.IntPtr UnityEngine.AndroidJNI::ToFloatArray(System.Single[])
extern void AndroidJNI_ToFloatArray_m61F723D6040BFC3A3622EE08E0EF9BBCE2E8E88B (void);
// 0x000000F2 System.IntPtr UnityEngine.AndroidJNI::ToDoubleArray(System.Double[])
extern void AndroidJNI_ToDoubleArray_m368EFFE8C4387F994423DFC4DA5834A4D4B1EC0E (void);
// 0x000000F3 System.IntPtr UnityEngine.AndroidJNI::ToObjectArray(System.IntPtr[],System.IntPtr)
extern void AndroidJNI_ToObjectArray_m4843C4E669DDFDA28853BB0D627A4A30DD0E9944 (void);
// 0x000000F4 System.IntPtr UnityEngine.AndroidJNI::ToObjectArray(System.IntPtr[])
extern void AndroidJNI_ToObjectArray_m86E80FA6CB35FF8AF0B5611106934EE3C9FC59D3 (void);
// 0x000000F5 System.Boolean[] UnityEngine.AndroidJNI::FromBooleanArray(System.IntPtr)
extern void AndroidJNI_FromBooleanArray_m95D7BE45F113A7576DF27BF14BBDC35DD7748A67 (void);
// 0x000000F6 System.Byte[] UnityEngine.AndroidJNI::FromByteArray(System.IntPtr)
extern void AndroidJNI_FromByteArray_m5C52B7F13653B39F42FFB7FEB1B665FAC07F0388 (void);
// 0x000000F7 System.SByte[] UnityEngine.AndroidJNI::FromSByteArray(System.IntPtr)
extern void AndroidJNI_FromSByteArray_mFED4929D339523808AE9C94F3C2AB3A317E9C5E1 (void);
// 0x000000F8 System.Char[] UnityEngine.AndroidJNI::FromCharArray(System.IntPtr)
extern void AndroidJNI_FromCharArray_m7149E127743A7D659017D1E1C3B174C3D615C638 (void);
// 0x000000F9 System.Int16[] UnityEngine.AndroidJNI::FromShortArray(System.IntPtr)
extern void AndroidJNI_FromShortArray_mA89CCCFED02DDFDA91835418DAD8211A4B7BDDC6 (void);
// 0x000000FA System.Int32[] UnityEngine.AndroidJNI::FromIntArray(System.IntPtr)
extern void AndroidJNI_FromIntArray_m5B8A47C4B6FDD607B3A67B02D4D1297B4C11CA6A (void);
// 0x000000FB System.Int64[] UnityEngine.AndroidJNI::FromLongArray(System.IntPtr)
extern void AndroidJNI_FromLongArray_mB042FE2F3D5AC91673FE72145E98D04B8775BE36 (void);
// 0x000000FC System.Single[] UnityEngine.AndroidJNI::FromFloatArray(System.IntPtr)
extern void AndroidJNI_FromFloatArray_mBA9EB0CE3EC9662D669877E2D7DA004B794C4331 (void);
// 0x000000FD System.Double[] UnityEngine.AndroidJNI::FromDoubleArray(System.IntPtr)
extern void AndroidJNI_FromDoubleArray_mF5D9E2F0D26862F10C98E8ECC8EB436EB9804692 (void);
// 0x000000FE System.IntPtr[] UnityEngine.AndroidJNI::FromObjectArray(System.IntPtr)
extern void AndroidJNI_FromObjectArray_mF29F2969BD34276ECCA7ABA7ADDD34C04694E445 (void);
// 0x000000FF System.Int32 UnityEngine.AndroidJNI::GetArrayLength(System.IntPtr)
extern void AndroidJNI_GetArrayLength_m7C02A09EAFEC667B3E8EBA9A06177E22E61028CB (void);
// 0x00000100 System.IntPtr UnityEngine.AndroidJNI::NewBooleanArray(System.Int32)
extern void AndroidJNI_NewBooleanArray_m9C7018583B95EC216E181204717267902A426029 (void);
// 0x00000101 System.IntPtr UnityEngine.AndroidJNI::NewByteArray(System.Int32)
extern void AndroidJNI_NewByteArray_mADACAA676D3E057D1C6109D8353EB704D10E7806 (void);
// 0x00000102 System.IntPtr UnityEngine.AndroidJNI::NewSByteArray(System.Int32)
extern void AndroidJNI_NewSByteArray_mFDAF396EF3C3CC7C315C20F1B7E14B2B51714F41 (void);
// 0x00000103 System.IntPtr UnityEngine.AndroidJNI::NewCharArray(System.Int32)
extern void AndroidJNI_NewCharArray_m801332FB86A2CEBF424B046128C4C0E8F7D5D80C (void);
// 0x00000104 System.IntPtr UnityEngine.AndroidJNI::NewShortArray(System.Int32)
extern void AndroidJNI_NewShortArray_m6CC9E93F24ED8BFC02A13D89DA95E6F17276BCA6 (void);
// 0x00000105 System.IntPtr UnityEngine.AndroidJNI::NewIntArray(System.Int32)
extern void AndroidJNI_NewIntArray_m46F3D6CBFA7BB4D79BDBB0971E68DE459A9F5D99 (void);
// 0x00000106 System.IntPtr UnityEngine.AndroidJNI::NewLongArray(System.Int32)
extern void AndroidJNI_NewLongArray_m707798711EAB93F83F0F2E2489C13C9AFA6886D7 (void);
// 0x00000107 System.IntPtr UnityEngine.AndroidJNI::NewFloatArray(System.Int32)
extern void AndroidJNI_NewFloatArray_mDC04BC46000F25D8D640A2DDAB36F9C81BD496F7 (void);
// 0x00000108 System.IntPtr UnityEngine.AndroidJNI::NewDoubleArray(System.Int32)
extern void AndroidJNI_NewDoubleArray_mA3DFC7AC3EC990D498539B59094FB3CEE4229E57 (void);
// 0x00000109 System.IntPtr UnityEngine.AndroidJNI::NewObjectArray(System.Int32,System.IntPtr,System.IntPtr)
extern void AndroidJNI_NewObjectArray_m4EAB5EA40119977AAD41793C78A3C19FF19A7043 (void);
// 0x0000010A System.Boolean UnityEngine.AndroidJNI::GetBooleanArrayElement(System.IntPtr,System.Int32)
extern void AndroidJNI_GetBooleanArrayElement_mCEC9DA5F142E7F4DCF70453B8B0D506720D8F4F6 (void);
// 0x0000010B System.Byte UnityEngine.AndroidJNI::GetByteArrayElement(System.IntPtr,System.Int32)
extern void AndroidJNI_GetByteArrayElement_m4E66A92347AFB54172A7483F1F224A36C927C913 (void);
// 0x0000010C System.SByte UnityEngine.AndroidJNI::GetSByteArrayElement(System.IntPtr,System.Int32)
extern void AndroidJNI_GetSByteArrayElement_mD8BC0A3483C53C6DB56EEE74274E71F7457B4DC2 (void);
// 0x0000010D System.Char UnityEngine.AndroidJNI::GetCharArrayElement(System.IntPtr,System.Int32)
extern void AndroidJNI_GetCharArrayElement_m46AE455A6DB4CE743D19B26986A7340C9EBE4EC4 (void);
// 0x0000010E System.Int16 UnityEngine.AndroidJNI::GetShortArrayElement(System.IntPtr,System.Int32)
extern void AndroidJNI_GetShortArrayElement_mEE788A8EDA8C2D81C30B5783B26983ACD908F1BD (void);
// 0x0000010F System.Int32 UnityEngine.AndroidJNI::GetIntArrayElement(System.IntPtr,System.Int32)
extern void AndroidJNI_GetIntArrayElement_m8D21128A83A398C97034293C4232487F6DEE8B52 (void);
// 0x00000110 System.Int64 UnityEngine.AndroidJNI::GetLongArrayElement(System.IntPtr,System.Int32)
extern void AndroidJNI_GetLongArrayElement_m1043DAB33A9DCA3BF5EDBA12F5D3121DFCD21BBC (void);
// 0x00000111 System.Single UnityEngine.AndroidJNI::GetFloatArrayElement(System.IntPtr,System.Int32)
extern void AndroidJNI_GetFloatArrayElement_m432455F5B77316342396460CF547335798BA7E64 (void);
// 0x00000112 System.Double UnityEngine.AndroidJNI::GetDoubleArrayElement(System.IntPtr,System.Int32)
extern void AndroidJNI_GetDoubleArrayElement_m2A6B6A5F27DC6CDC378797E525402C238CD65E02 (void);
// 0x00000113 System.IntPtr UnityEngine.AndroidJNI::GetObjectArrayElement(System.IntPtr,System.Int32)
extern void AndroidJNI_GetObjectArrayElement_mC4CAF9744617F69EFCD95B71D95492DA20A0FACE (void);
// 0x00000114 System.Void UnityEngine.AndroidJNI::SetBooleanArrayElement(System.IntPtr,System.Int32,System.Byte)
extern void AndroidJNI_SetBooleanArrayElement_mADEB936138A96C081CCE560B6F11C427C4729292 (void);
// 0x00000115 System.Void UnityEngine.AndroidJNI::SetBooleanArrayElement(System.IntPtr,System.Int32,System.Boolean)
extern void AndroidJNI_SetBooleanArrayElement_m16CF5F014FABEB28253AACFC93D4FF113D13DEC3 (void);
// 0x00000116 System.Void UnityEngine.AndroidJNI::SetByteArrayElement(System.IntPtr,System.Int32,System.SByte)
extern void AndroidJNI_SetByteArrayElement_m484197612F5E5C163F9116A0F63B0355823C375F (void);
// 0x00000117 System.Void UnityEngine.AndroidJNI::SetSByteArrayElement(System.IntPtr,System.Int32,System.SByte)
extern void AndroidJNI_SetSByteArrayElement_m470F13FC7EA450CB5B113641F99EF040E19E708C (void);
// 0x00000118 System.Void UnityEngine.AndroidJNI::SetCharArrayElement(System.IntPtr,System.Int32,System.Char)
extern void AndroidJNI_SetCharArrayElement_m56BE8F363275BF93E558F4D4BF6042DA9CDF1A39 (void);
// 0x00000119 System.Void UnityEngine.AndroidJNI::SetShortArrayElement(System.IntPtr,System.Int32,System.Int16)
extern void AndroidJNI_SetShortArrayElement_m8D2E6451D917D5452770325BE62DC667DFA26DBF (void);
// 0x0000011A System.Void UnityEngine.AndroidJNI::SetIntArrayElement(System.IntPtr,System.Int32,System.Int32)
extern void AndroidJNI_SetIntArrayElement_m66DF089843878DC016F15596A173906A2804E555 (void);
// 0x0000011B System.Void UnityEngine.AndroidJNI::SetLongArrayElement(System.IntPtr,System.Int32,System.Int64)
extern void AndroidJNI_SetLongArrayElement_m54F052B44CF922C9675C31BF32B4B3726E67AC79 (void);
// 0x0000011C System.Void UnityEngine.AndroidJNI::SetFloatArrayElement(System.IntPtr,System.Int32,System.Single)
extern void AndroidJNI_SetFloatArrayElement_mF3230F001486735FB129DD4117DD01260C998343 (void);
// 0x0000011D System.Void UnityEngine.AndroidJNI::SetDoubleArrayElement(System.IntPtr,System.Int32,System.Double)
extern void AndroidJNI_SetDoubleArrayElement_m82F4EBCB94088644F17F30C7AF48475E31BE5211 (void);
// 0x0000011E System.Void UnityEngine.AndroidJNI::SetObjectArrayElement(System.IntPtr,System.Int32,System.IntPtr)
extern void AndroidJNI_SetObjectArrayElement_mAEA12A91B1C20BF46CBFB5DC3B1D5AF95AA463B2 (void);
// 0x0000011F System.Void UnityEngine.AndroidJNISafe::CheckException()
extern void AndroidJNISafe_CheckException_m465A2955F921417ED62C035390E550889E93F8DC (void);
// 0x00000120 System.Void UnityEngine.AndroidJNISafe::DeleteGlobalRef(System.IntPtr)
extern void AndroidJNISafe_DeleteGlobalRef_mD4ECCC474E98F40B65184E138EF9D84A4F4AAAA0 (void);
// 0x00000121 System.Void UnityEngine.AndroidJNISafe::DeleteWeakGlobalRef(System.IntPtr)
extern void AndroidJNISafe_DeleteWeakGlobalRef_mBC786B6240AB03EA493A71A43D4297871FFC679A (void);
// 0x00000122 System.Void UnityEngine.AndroidJNISafe::DeleteLocalRef(System.IntPtr)
extern void AndroidJNISafe_DeleteLocalRef_m20303564C88A1B90E3D8D7A7D893392E18967094 (void);
// 0x00000123 System.IntPtr UnityEngine.AndroidJNISafe::NewString(System.String)
extern void AndroidJNISafe_NewString_m6A9EC18D8B122E7B901DB6BF469BFD38D1E8FE5A (void);
// 0x00000124 System.String UnityEngine.AndroidJNISafe::GetStringChars(System.IntPtr)
extern void AndroidJNISafe_GetStringChars_mE246814CD8FF4EDDEE6EBF107367C4A8EAF03849 (void);
// 0x00000125 System.IntPtr UnityEngine.AndroidJNISafe::GetObjectClass(System.IntPtr)
extern void AndroidJNISafe_GetObjectClass_m6FD815CB0F9760199ACD03D16FC88FED055BC9F3 (void);
// 0x00000126 System.IntPtr UnityEngine.AndroidJNISafe::GetStaticMethodID(System.IntPtr,System.String,System.String)
extern void AndroidJNISafe_GetStaticMethodID_mAD5134FF6DE446852F3F28B791C15ADBD5E9E5E8 (void);
// 0x00000127 System.IntPtr UnityEngine.AndroidJNISafe::GetMethodID(System.IntPtr,System.String,System.String)
extern void AndroidJNISafe_GetMethodID_mF095B57A77BE529D51F369D94B66D14C2BC88536 (void);
// 0x00000128 System.IntPtr UnityEngine.AndroidJNISafe::GetFieldID(System.IntPtr,System.String,System.String)
extern void AndroidJNISafe_GetFieldID_mAD9554C6DCE9389C441A9AB556001211B9B2663D (void);
// 0x00000129 System.IntPtr UnityEngine.AndroidJNISafe::GetStaticFieldID(System.IntPtr,System.String,System.String)
extern void AndroidJNISafe_GetStaticFieldID_mCCCE792F7BE47B6370951D417CCB1E2713DBF482 (void);
// 0x0000012A System.IntPtr UnityEngine.AndroidJNISafe::FromReflectedMethod(System.IntPtr)
extern void AndroidJNISafe_FromReflectedMethod_mED131988778BF0267C4CE711854D4BC26D0D960B (void);
// 0x0000012B System.IntPtr UnityEngine.AndroidJNISafe::FindClass(System.String)
extern void AndroidJNISafe_FindClass_m2E8072B600873B4D87B2197C1168967050208D1B (void);
// 0x0000012C System.IntPtr UnityEngine.AndroidJNISafe::NewObject(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_NewObject_m9DF3572307421E944B3A95DC82EF90CEF1774DA2 (void);
// 0x0000012D System.Void UnityEngine.AndroidJNISafe::SetStaticObjectField(System.IntPtr,System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_SetStaticObjectField_m7757F7E30F8122DAF89F138A8AE727CB896BC721 (void);
// 0x0000012E System.Void UnityEngine.AndroidJNISafe::SetStaticStringField(System.IntPtr,System.IntPtr,System.String)
extern void AndroidJNISafe_SetStaticStringField_m445D977B2374056C6E4607FAEDB7E99A1353E2EE (void);
// 0x0000012F System.Void UnityEngine.AndroidJNISafe::SetStaticCharField(System.IntPtr,System.IntPtr,System.Char)
extern void AndroidJNISafe_SetStaticCharField_m2B8245275C36525798C869B7B1088B25BA663613 (void);
// 0x00000130 System.Void UnityEngine.AndroidJNISafe::SetStaticDoubleField(System.IntPtr,System.IntPtr,System.Double)
extern void AndroidJNISafe_SetStaticDoubleField_mA0253927D476917D2158A9CE29F1BF535485B956 (void);
// 0x00000131 System.Void UnityEngine.AndroidJNISafe::SetStaticFloatField(System.IntPtr,System.IntPtr,System.Single)
extern void AndroidJNISafe_SetStaticFloatField_mB2EDDE632AB2088CD12F1FD12174FB86990BCBEE (void);
// 0x00000132 System.Void UnityEngine.AndroidJNISafe::SetStaticLongField(System.IntPtr,System.IntPtr,System.Int64)
extern void AndroidJNISafe_SetStaticLongField_m299AAC2DE8B6747B0B5E109BABB2F3A4FC1F486E (void);
// 0x00000133 System.Void UnityEngine.AndroidJNISafe::SetStaticShortField(System.IntPtr,System.IntPtr,System.Int16)
extern void AndroidJNISafe_SetStaticShortField_m92534AAA86D7E1055E12936C8A7BD6B865B7DB81 (void);
// 0x00000134 System.Void UnityEngine.AndroidJNISafe::SetStaticSByteField(System.IntPtr,System.IntPtr,System.SByte)
extern void AndroidJNISafe_SetStaticSByteField_m242120982A9227E1E8344FFE9F06FD74986D15E9 (void);
// 0x00000135 System.Void UnityEngine.AndroidJNISafe::SetStaticBooleanField(System.IntPtr,System.IntPtr,System.Boolean)
extern void AndroidJNISafe_SetStaticBooleanField_mBE4E40DA1B07A29D356AEEE6CB9519F2B3621AC9 (void);
// 0x00000136 System.Void UnityEngine.AndroidJNISafe::SetStaticIntField(System.IntPtr,System.IntPtr,System.Int32)
extern void AndroidJNISafe_SetStaticIntField_m1E20F6C72260CAFBF73207DCEC1816B2816EEBE1 (void);
// 0x00000137 System.IntPtr UnityEngine.AndroidJNISafe::GetStaticObjectField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticObjectField_mB6B9A9EB2619DFDF1DA56300BF9FEC19BF883867 (void);
// 0x00000138 System.String UnityEngine.AndroidJNISafe::GetStaticStringField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticStringField_mB3D1325B08A38C7DAF1FA3E6CB52F6D8E0A2CB47 (void);
// 0x00000139 System.Char UnityEngine.AndroidJNISafe::GetStaticCharField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticCharField_mF70F6D197261364AF2A9E875D84DDDA35BD0ED96 (void);
// 0x0000013A System.Double UnityEngine.AndroidJNISafe::GetStaticDoubleField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticDoubleField_mEB86F2CE1F3879AAA9DEDA4B496F882C0E1DCBC2 (void);
// 0x0000013B System.Single UnityEngine.AndroidJNISafe::GetStaticFloatField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticFloatField_mD1456B729026959309A839C2647279C0B6541356 (void);
// 0x0000013C System.Int64 UnityEngine.AndroidJNISafe::GetStaticLongField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticLongField_mABC2B933CEB757E3FAF1FD6C60AA0C4D38E9C49D (void);
// 0x0000013D System.Int16 UnityEngine.AndroidJNISafe::GetStaticShortField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticShortField_m83716D4D85B30F26803F866AC47D5C04AAB5D320 (void);
// 0x0000013E System.SByte UnityEngine.AndroidJNISafe::GetStaticSByteField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticSByteField_m77596E5B1AE58DAFF39268AC954CAD53974A688D (void);
// 0x0000013F System.Boolean UnityEngine.AndroidJNISafe::GetStaticBooleanField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticBooleanField_m172BEAA3F0AB6754EA5F1AD30C36DAA0D3D7C666 (void);
// 0x00000140 System.Int32 UnityEngine.AndroidJNISafe::GetStaticIntField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStaticIntField_m0698D50C44E490A009E8388C7321630DED5973BD (void);
// 0x00000141 System.Void UnityEngine.AndroidJNISafe::CallStaticVoidMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticVoidMethod_m7186730B5574226E150225A11B48B4B15FC74020 (void);
// 0x00000142 System.IntPtr UnityEngine.AndroidJNISafe::CallStaticObjectMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticObjectMethod_m545474765D15AC9B0144192760B45BAA963B8F5E (void);
// 0x00000143 System.String UnityEngine.AndroidJNISafe::CallStaticStringMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticStringMethod_m8BD92117111558CC00540B45437B4A90222B89BE (void);
// 0x00000144 System.Char UnityEngine.AndroidJNISafe::CallStaticCharMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticCharMethod_m12390A6D01F33879740A94195E445D7B3D4CD652 (void);
// 0x00000145 System.Double UnityEngine.AndroidJNISafe::CallStaticDoubleMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticDoubleMethod_m0510A1F18E0ED9CE8CFA82C6B2A56DCD89814247 (void);
// 0x00000146 System.Single UnityEngine.AndroidJNISafe::CallStaticFloatMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticFloatMethod_m46EE9FEEEFCF68FA1A014C2E417454BA55206945 (void);
// 0x00000147 System.Int64 UnityEngine.AndroidJNISafe::CallStaticLongMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticLongMethod_mE3487C989E9D5EFA0FB2356408B7FF40E61DBD12 (void);
// 0x00000148 System.Int16 UnityEngine.AndroidJNISafe::CallStaticShortMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticShortMethod_m47EFDF3473E6C4D6F61956725E6595A0B919589D (void);
// 0x00000149 System.SByte UnityEngine.AndroidJNISafe::CallStaticSByteMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticSByteMethod_m68A45FE3EEE495648B36902B51ECDB76385EE736 (void);
// 0x0000014A System.Boolean UnityEngine.AndroidJNISafe::CallStaticBooleanMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticBooleanMethod_mA65977CB0FC4E3759938FCBD7BA43330317E8A3D (void);
// 0x0000014B System.Int32 UnityEngine.AndroidJNISafe::CallStaticIntMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStaticIntMethod_m558318BA8E7A0A7526E48FF7218876FB987F397B (void);
// 0x0000014C System.Void UnityEngine.AndroidJNISafe::SetObjectField(System.IntPtr,System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_SetObjectField_mFE500926F9C963FF106E8AA30A16F4C671BAA8CA (void);
// 0x0000014D System.Void UnityEngine.AndroidJNISafe::SetStringField(System.IntPtr,System.IntPtr,System.String)
extern void AndroidJNISafe_SetStringField_m649363D4E87763D6A9760359EAFB29802E90B409 (void);
// 0x0000014E System.Void UnityEngine.AndroidJNISafe::SetCharField(System.IntPtr,System.IntPtr,System.Char)
extern void AndroidJNISafe_SetCharField_m69D09A6A2CEA55D84B240FE32D90300AAB1334F9 (void);
// 0x0000014F System.Void UnityEngine.AndroidJNISafe::SetDoubleField(System.IntPtr,System.IntPtr,System.Double)
extern void AndroidJNISafe_SetDoubleField_mE93D0C5EC2019A1B657BD32970FE6EFC9B005A58 (void);
// 0x00000150 System.Void UnityEngine.AndroidJNISafe::SetFloatField(System.IntPtr,System.IntPtr,System.Single)
extern void AndroidJNISafe_SetFloatField_m589CA6B8DD2BFD4515C5AEAE3772782B293F02C3 (void);
// 0x00000151 System.Void UnityEngine.AndroidJNISafe::SetLongField(System.IntPtr,System.IntPtr,System.Int64)
extern void AndroidJNISafe_SetLongField_m13905547F5CDC7E01AB0D8C787BF98DC2870EC35 (void);
// 0x00000152 System.Void UnityEngine.AndroidJNISafe::SetShortField(System.IntPtr,System.IntPtr,System.Int16)
extern void AndroidJNISafe_SetShortField_mF95E569C142DEDD604CE8BA7617328B3EDDD2F0D (void);
// 0x00000153 System.Void UnityEngine.AndroidJNISafe::SetSByteField(System.IntPtr,System.IntPtr,System.SByte)
extern void AndroidJNISafe_SetSByteField_mB021168746571E7CAA8C0EAD7AA7F02C18B5EE33 (void);
// 0x00000154 System.Void UnityEngine.AndroidJNISafe::SetBooleanField(System.IntPtr,System.IntPtr,System.Boolean)
extern void AndroidJNISafe_SetBooleanField_m5279EA41B214699E79733DC6C93259CC9DCA1D9E (void);
// 0x00000155 System.Void UnityEngine.AndroidJNISafe::SetIntField(System.IntPtr,System.IntPtr,System.Int32)
extern void AndroidJNISafe_SetIntField_mD238DA37BA1B3D7693484237951A6EFEA9C62120 (void);
// 0x00000156 System.IntPtr UnityEngine.AndroidJNISafe::GetObjectField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetObjectField_mCF3BB1C38718D6F55081126BC7F6C286B382B275 (void);
// 0x00000157 System.String UnityEngine.AndroidJNISafe::GetStringField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetStringField_mADFCA05D6DE790600B57E90B20F2E75AFC036B0F (void);
// 0x00000158 System.Char UnityEngine.AndroidJNISafe::GetCharField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetCharField_m8301FA96B40E27C032590FE3F8E84A777A4739C3 (void);
// 0x00000159 System.Double UnityEngine.AndroidJNISafe::GetDoubleField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetDoubleField_mBCBD5E80223EDECC06FA783F34149E3625219074 (void);
// 0x0000015A System.Single UnityEngine.AndroidJNISafe::GetFloatField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetFloatField_m1EAA1ED33002BBA28CA2B630521D6BF1B7D3A2E7 (void);
// 0x0000015B System.Int64 UnityEngine.AndroidJNISafe::GetLongField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetLongField_m7DD751358D10BB276D8A95D413B9DFB1E8EE81D8 (void);
// 0x0000015C System.Int16 UnityEngine.AndroidJNISafe::GetShortField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetShortField_m5D21E87061C1DAC89DF58671C53432D0361F0C6E (void);
// 0x0000015D System.SByte UnityEngine.AndroidJNISafe::GetSByteField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetSByteField_mAD3B08AA8A97F77CAE17DD25B0F389AFAC2023B1 (void);
// 0x0000015E System.Boolean UnityEngine.AndroidJNISafe::GetBooleanField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetBooleanField_m34F37B560A6AEC81B9061FB3B72698C84720435D (void);
// 0x0000015F System.Int32 UnityEngine.AndroidJNISafe::GetIntField(System.IntPtr,System.IntPtr)
extern void AndroidJNISafe_GetIntField_mBD983688B73063DE5C55D320F60F266443FAC97C (void);
// 0x00000160 System.Void UnityEngine.AndroidJNISafe::CallVoidMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallVoidMethod_m4918322816B85C931BD396C8CD37AC77C9A13110 (void);
// 0x00000161 System.IntPtr UnityEngine.AndroidJNISafe::CallObjectMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallObjectMethod_mBA06053048352614B802E9429FFF50C4A1B56057 (void);
// 0x00000162 System.String UnityEngine.AndroidJNISafe::CallStringMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallStringMethod_mF2975892926478EF6ACB9D62CCC0829A8BDD2699 (void);
// 0x00000163 System.Char UnityEngine.AndroidJNISafe::CallCharMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallCharMethod_mAA4D432267ED412D5F5D804B9038BC7D9B2DCDE3 (void);
// 0x00000164 System.Double UnityEngine.AndroidJNISafe::CallDoubleMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallDoubleMethod_mE26233E7C97901A772CAA39C02CDA51AEF0DFCFE (void);
// 0x00000165 System.Single UnityEngine.AndroidJNISafe::CallFloatMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallFloatMethod_m1DFC594E3141D409B0F7341455FEE45E5C141504 (void);
// 0x00000166 System.Int64 UnityEngine.AndroidJNISafe::CallLongMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallLongMethod_mE101DBF4C4E8832E2BB28F0471EA9A824CC10525 (void);
// 0x00000167 System.Int16 UnityEngine.AndroidJNISafe::CallShortMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallShortMethod_mB007DECD38E377B824B170DE90CDD9F65FDF8DFC (void);
// 0x00000168 System.SByte UnityEngine.AndroidJNISafe::CallSByteMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallSByteMethod_m15A10302255F3A4207B663C0A97878C433233DDA (void);
// 0x00000169 System.Boolean UnityEngine.AndroidJNISafe::CallBooleanMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallBooleanMethod_m0C8E5B12826ACB3AA868FF52B22C57BA107A1DF5 (void);
// 0x0000016A System.Int32 UnityEngine.AndroidJNISafe::CallIntMethod(System.IntPtr,System.IntPtr,UnityEngine.jvalue[])
extern void AndroidJNISafe_CallIntMethod_m88ECF62449F26B13AE4C2B7E8B3C0E503970782B (void);
// 0x0000016B System.Char[] UnityEngine.AndroidJNISafe::FromCharArray(System.IntPtr)
extern void AndroidJNISafe_FromCharArray_m54EDC9D2BE92F9973F4E00EE953EE242B231EA96 (void);
// 0x0000016C System.Double[] UnityEngine.AndroidJNISafe::FromDoubleArray(System.IntPtr)
extern void AndroidJNISafe_FromDoubleArray_mDEA8F2C7854101272F3A2733F351B570AAD5D9D9 (void);
// 0x0000016D System.Single[] UnityEngine.AndroidJNISafe::FromFloatArray(System.IntPtr)
extern void AndroidJNISafe_FromFloatArray_mDE02985159EEFD2CB28611C797AC21DE8B6300B8 (void);
// 0x0000016E System.Int64[] UnityEngine.AndroidJNISafe::FromLongArray(System.IntPtr)
extern void AndroidJNISafe_FromLongArray_mC4D73C0DA27F212947AB85AA2030A35BECDF8288 (void);
// 0x0000016F System.Int16[] UnityEngine.AndroidJNISafe::FromShortArray(System.IntPtr)
extern void AndroidJNISafe_FromShortArray_m62C0CB2D0BAE96D4B8CE365630361150EBE884FC (void);
// 0x00000170 System.Byte[] UnityEngine.AndroidJNISafe::FromByteArray(System.IntPtr)
extern void AndroidJNISafe_FromByteArray_mB06EF0FDBF6C738231E8F9D4998C38551131C4C5 (void);
// 0x00000171 System.SByte[] UnityEngine.AndroidJNISafe::FromSByteArray(System.IntPtr)
extern void AndroidJNISafe_FromSByteArray_m261D638D8B059AB777BEF0BEFDD0822717DFF2B1 (void);
// 0x00000172 System.Boolean[] UnityEngine.AndroidJNISafe::FromBooleanArray(System.IntPtr)
extern void AndroidJNISafe_FromBooleanArray_m36ED740401185EC0A959CA0F96A324A69E668646 (void);
// 0x00000173 System.Int32[] UnityEngine.AndroidJNISafe::FromIntArray(System.IntPtr)
extern void AndroidJNISafe_FromIntArray_mC4C4DC70FFA39CD6E3E02FDAC7192324E6D4614E (void);
// 0x00000174 System.IntPtr UnityEngine.AndroidJNISafe::ToObjectArray(System.IntPtr[],System.IntPtr)
extern void AndroidJNISafe_ToObjectArray_m4C95D999242E900D9C70891E44100A5EB5020C5F (void);
// 0x00000175 System.IntPtr UnityEngine.AndroidJNISafe::ToCharArray(System.Char[])
extern void AndroidJNISafe_ToCharArray_mFBF42A984F1C5D618CD0366B3B344E2BF8856B12 (void);
// 0x00000176 System.IntPtr UnityEngine.AndroidJNISafe::ToDoubleArray(System.Double[])
extern void AndroidJNISafe_ToDoubleArray_m6C1716EFF6DCA1AE3E04D292EB38A31E4132C1C1 (void);
// 0x00000177 System.IntPtr UnityEngine.AndroidJNISafe::ToFloatArray(System.Single[])
extern void AndroidJNISafe_ToFloatArray_m18207119C3AC0C5D71DA537B2CEB21D11301B732 (void);
// 0x00000178 System.IntPtr UnityEngine.AndroidJNISafe::ToLongArray(System.Int64[])
extern void AndroidJNISafe_ToLongArray_m67486F6D1F467D2354EEB74DACFDA79A1F3F7E0C (void);
// 0x00000179 System.IntPtr UnityEngine.AndroidJNISafe::ToShortArray(System.Int16[])
extern void AndroidJNISafe_ToShortArray_m170C4D2D7D1ED3A02B4C707FB666BF4F2A9D02ED (void);
// 0x0000017A System.IntPtr UnityEngine.AndroidJNISafe::ToByteArray(System.Byte[])
extern void AndroidJNISafe_ToByteArray_mB36D6ABE2FF31844554A353E136B2153BFDF0F65 (void);
// 0x0000017B System.IntPtr UnityEngine.AndroidJNISafe::ToSByteArray(System.SByte[])
extern void AndroidJNISafe_ToSByteArray_m10BD1D36C8D2D7F764FD8C87742FD700DB48665C (void);
// 0x0000017C System.IntPtr UnityEngine.AndroidJNISafe::ToBooleanArray(System.Boolean[])
extern void AndroidJNISafe_ToBooleanArray_m94630C7B69D819D7BE683691B46879C6154B5F3A (void);
// 0x0000017D System.IntPtr UnityEngine.AndroidJNISafe::ToIntArray(System.Int32[])
extern void AndroidJNISafe_ToIntArray_mE4647AC18D85206D98121752C3B8CD7D52A321EB (void);
// 0x0000017E System.IntPtr UnityEngine.AndroidJNISafe::GetObjectArrayElement(System.IntPtr,System.Int32)
extern void AndroidJNISafe_GetObjectArrayElement_m02B6993F13670DD2D1557D75EC31D8D860F10FD0 (void);
// 0x0000017F System.Int32 UnityEngine.AndroidJNISafe::GetArrayLength(System.IntPtr)
extern void AndroidJNISafe_GetArrayLength_m809419F04C9BB93FED5B4A89F0539231C9B90E6F (void);
// 0x00000180 System.Void UnityEngine.Android.AndroidAssetPackInfo::.ctor(System.String,UnityEngine.Android.AndroidAssetPackStatus,System.UInt64,System.UInt64,System.Single,UnityEngine.Android.AndroidAssetPackError)
extern void AndroidAssetPackInfo__ctor_m747C0EAD6448BA479BAE3C5EAB67F205E0307972 (void);
// 0x00000181 System.Void UnityEngine.Android.AndroidAssetPackState::.ctor(System.String,UnityEngine.Android.AndroidAssetPackStatus,UnityEngine.Android.AndroidAssetPackError)
extern void AndroidAssetPackState__ctor_m5CB1F078A45558A0966BA32FCFE18CFC46CA941B (void);
// 0x00000182 System.Void UnityEngine.Android.AndroidAssetPackUseMobileDataRequestResult::.ctor(System.Boolean)
extern void AndroidAssetPackUseMobileDataRequestResult__ctor_mB46211F6D3B3A421B1C1D9E05F3FC62858383E8F (void);
// 0x00000183 UnityEngine.AndroidJavaObject UnityEngine.Android.AndroidAssetPacks::GetAssetPackManager()
extern void AndroidAssetPacks_GetAssetPackManager_m91B486E7EEF9F019B820E11F10E1D877609778A9 (void);
// 0x00000184 System.Void UnityEngine.Android.AndroidAssetPacks/AssetPackManagerDownloadStatusCallback::.ctor(System.Action`1<UnityEngine.Android.AndroidAssetPackInfo>,System.String[])
extern void AssetPackManagerDownloadStatusCallback__ctor_m03D0B212EFAEACF611D60596E978DD0468C6D936 (void);
// 0x00000185 System.Void UnityEngine.Android.AndroidAssetPacks/AssetPackManagerDownloadStatusCallback::onStatusUpdate(System.String,System.Int32,System.Int64,System.Int64,System.Int32,System.Int32)
extern void AssetPackManagerDownloadStatusCallback_onStatusUpdate_m4042BE15C2B4A64CE1BAF4734E6A0BF8DD4FFC7A (void);
// 0x00000186 System.Void UnityEngine.Android.AndroidAssetPacks/AssetPackManagerMobileDataConfirmationCallback::.ctor(System.Action`1<UnityEngine.Android.AndroidAssetPackUseMobileDataRequestResult>)
extern void AssetPackManagerMobileDataConfirmationCallback__ctor_m927FCB784F7D90524C6725B9E64EF8799538999B (void);
// 0x00000187 System.Void UnityEngine.Android.AndroidAssetPacks/AssetPackManagerMobileDataConfirmationCallback::onMobileDataConfirmationResult(System.Boolean)
extern void AssetPackManagerMobileDataConfirmationCallback_onMobileDataConfirmationResult_m61FEFDED8787D798CF30CB78DC133505B7D3614A (void);
// 0x00000188 System.Void UnityEngine.Android.AndroidAssetPacks/AssetPackManagerStatusQueryCallback::.ctor(System.Action`2<System.UInt64,UnityEngine.Android.AndroidAssetPackState[]>,System.String[])
extern void AssetPackManagerStatusQueryCallback__ctor_m66F3B4A8BD8911F07121E2F8EF871741CE3A2CDB (void);
// 0x00000189 System.Void UnityEngine.Android.AndroidAssetPacks/AssetPackManagerStatusQueryCallback::onStatusResult(System.Int64,System.String[],System.Int32[],System.Int32[])
extern void AssetPackManagerStatusQueryCallback_onStatusResult_m8C41C5CC2F37808E5C5C12B023DF31E1DF96C314 (void);
// 0x0000018A UnityEngine.AndroidJavaObject UnityEngine.Android.Common::GetActivity()
extern void Common_GetActivity_m33E672D97C605F07C4EE5F927F4C8D21F8D4F2B7 (void);
// 0x0000018B System.Void UnityEngine.Android.PermissionCallbacks::add_PermissionGranted(System.Action`1<System.String>)
extern void PermissionCallbacks_add_PermissionGranted_m74335D4200D9B1A7C80AB9C133F95C61FCDCDF89 (void);
// 0x0000018C System.Void UnityEngine.Android.PermissionCallbacks::remove_PermissionGranted(System.Action`1<System.String>)
extern void PermissionCallbacks_remove_PermissionGranted_m4A3F9873FC159F89A2AD35F2FCAFF66A19813AF4 (void);
// 0x0000018D System.Void UnityEngine.Android.PermissionCallbacks::add_PermissionDenied(System.Action`1<System.String>)
extern void PermissionCallbacks_add_PermissionDenied_mE0B2826463785B050C999C70F443FCC3822563D0 (void);
// 0x0000018E System.Void UnityEngine.Android.PermissionCallbacks::remove_PermissionDenied(System.Action`1<System.String>)
extern void PermissionCallbacks_remove_PermissionDenied_mF1A606ADE21F9520909126D3642B0BC2D6E994A1 (void);
// 0x0000018F System.Void UnityEngine.Android.PermissionCallbacks::add_PermissionDeniedAndDontAskAgain(System.Action`1<System.String>)
extern void PermissionCallbacks_add_PermissionDeniedAndDontAskAgain_mEDE8C00FEF2F649F10A47F30AC4ECB09E52DB9AA (void);
// 0x00000190 System.Void UnityEngine.Android.PermissionCallbacks::remove_PermissionDeniedAndDontAskAgain(System.Action`1<System.String>)
extern void PermissionCallbacks_remove_PermissionDeniedAndDontAskAgain_m55B04AE58C687946BDFA2094ED851518B2A1D68D (void);
// 0x00000191 System.Void UnityEngine.Android.PermissionCallbacks::.ctor()
extern void PermissionCallbacks__ctor_m91B14BBBC8913C131E400BA0D13576822AAE7A75 (void);
// 0x00000192 System.Void UnityEngine.Android.PermissionCallbacks::onPermissionGranted(System.String)
extern void PermissionCallbacks_onPermissionGranted_m723440705B5B21B97AF5206716275BAE2A122E3C (void);
// 0x00000193 System.Void UnityEngine.Android.PermissionCallbacks::onPermissionDenied(System.String)
extern void PermissionCallbacks_onPermissionDenied_m0E05122B560DD62BB38178EE601E65854017980A (void);
// 0x00000194 System.Void UnityEngine.Android.PermissionCallbacks::onPermissionDeniedAndDontAskAgain(System.String)
extern void PermissionCallbacks_onPermissionDeniedAndDontAskAgain_m1191CF6422AFD8E8FE7BDBDBC04721D63718A5D9 (void);
// 0x00000195 UnityEngine.AndroidJavaObject UnityEngine.Android.Permission::GetUnityPermissions()
extern void Permission_GetUnityPermissions_m6A22A923A8B036209E6218E24F7AA8CA79AA15BD (void);
// 0x00000196 System.Boolean UnityEngine.Android.Permission::HasUserAuthorizedPermission(System.String)
extern void Permission_HasUserAuthorizedPermission_mF4C90E13124E28F6F672200E489CC25A9B645B8B (void);
// 0x00000197 System.Void UnityEngine.Android.Permission::RequestUserPermission(System.String)
extern void Permission_RequestUserPermission_mF9CF3A21AAF34B311137C4D00B3AD6A6C2694242 (void);
// 0x00000198 System.Void UnityEngine.Android.Permission::RequestUserPermission(System.String,UnityEngine.Android.PermissionCallbacks)
extern void Permission_RequestUserPermission_m7B8E817C03FDB5C99F22002C7181F27BF031F117 (void);
// 0x00000199 System.Void UnityEngine.Android.Permission::RequestUserPermissions(System.String[],UnityEngine.Android.PermissionCallbacks)
extern void Permission_RequestUserPermissions_m09FE74BF5829C8B48A0E6C9B7F7846FEE763D2DA (void);
static Il2CppMethodPointer s_methodPointers[409] = 
{
	AndroidJavaRunnable__ctor_m000E4FEB2DE8031A1CD733610D76E2BF60490334,
	AndroidJavaRunnable_Invoke_m98CFB1479B942F71BF29F53CFDAC1CB9DAFAEBE1,
	AndroidJavaException__ctor_mD4B5992BB074504F8E86D79EA98752D3CB154541,
	AndroidJavaException_get_StackTrace_m28AC922BCC16051CCBA4C7E5F69698264AA7CC27,
	GlobalJavaObjectRef__ctor_mFE5679D1B51F51CBF11721773C0D767286AC22E8,
	GlobalJavaObjectRef_Finalize_m2EE89F98A391773F885A4A312FD4BD134E0D46D8,
	GlobalJavaObjectRef_op_Implicit_m444B263750F9B778C87C30EA918CDC0B62271F24,
	GlobalJavaObjectRef_Dispose_m45E67345587866D5A50D250D1C17425110703520,
	AndroidJavaRunnableProxy__ctor_mB173256AF7629962B226343C4F6F94FFFF7317C3,
	AndroidJavaRunnableProxy_run_m014F4E0A8ED56A054096F2BAC90653716D2A0D46,
	AndroidJavaProxy__ctor_m2832886A0E1BBF6702653A7C6A4609F11FB712C7,
	AndroidJavaProxy__ctor_mFA05DF6B31FC284C65D378C02A2A34F277DFE6E5,
	AndroidJavaProxy_Finalize_m6E4C294F2117D7A07E82A315081C9239AFA217E8,
	AndroidJavaProxy_Invoke_m9D765F3E7DC37C5CB14C4884F2873B48D2F96BFB,
	AndroidJavaProxy_Invoke_mCAE9C5E669AD50DE372494E12224FF1F31A43F1D,
	AndroidJavaProxy_equals_mC390139E035408E858940EB523D45ED3C8377110,
	AndroidJavaProxy_hashCode_m7991233D3D6D5F994E7BC59C3CB65DBBEDF8CA93,
	AndroidJavaProxy_toString_mF77EEDD3BB413F1273D9970BFB0D7C388366B256,
	AndroidJavaProxy_GetProxyObject_mBFD2FBEF9ED9D4AE23DECF5836E5C73A886E2109,
	AndroidJavaProxy_GetRawProxy_m685E066A4D378B596CD88385B954AE90CBF328A9,
	AndroidJavaProxy__cctor_m44B4074B6A6D8193D86E65DB4C165C2371615D63,
	AndroidJavaObject__ctor_mB47CA3FC88F645DAB31FB0FAAA32E9159B1DB19E,
	AndroidJavaObject__ctor_m1F1F88504475490860A246714F36205FB7D53362,
	AndroidJavaObject__ctor_m262439771D3A3EFBD18E5D06188D11989D562635,
	AndroidJavaObject__ctor_m0F50ADD04B4BEA5ACB6B614BB206EBFA9353CF6B,
	AndroidJavaObject__ctor_mA61E481C9C0F990FF9BEBFE9E1299612BC174E0E,
	AndroidJavaObject__ctor_m5A65B5D325C2CEFAC4097A0D3813F8E158178DD7,
	AndroidJavaObject_Dispose_m2B1593C20B3CE1C8FF95982F638F50985F9DD9E6,
	NULL,
	AndroidJavaObject_Call_mDEF7846E2AB1C5379069BB21049ED55A9D837B1C,
	NULL,
	AndroidJavaObject_CallStatic_mB677DE04369EDD8E6DECAF2F233116EE1F06555C,
	NULL,
	NULL,
	NULL,
	NULL,
	AndroidJavaObject_GetRawObject_m536F043B5CE2C21369FF6173C9D2A9A62136BC48,
	AndroidJavaObject_GetRawClass_mE4FB4DC4F856A52E10C6AAD0B65BEBF47B5071F5,
	AndroidJavaObject_CloneReference_m6DF6E2BF8D91804B303C93C2026E4A39977E8428,
	NULL,
	NULL,
	NULL,
	NULL,
	AndroidJavaObject_DebugPrint_m047934BF3D1E6676FDDBDA038E1AF387C5413533,
	AndroidJavaObject_DebugPrint_m41CA713464E773016D31C1B6C1489AC34A542CE6,
	AndroidJavaObject__AndroidJavaObject_m1284CB7198514B8C06A2BF794ACDC909DC26443F,
	AndroidJavaObject__ctor_m0CEE7D570807333CE2C193A82AB3AB8D4F873A6B,
	AndroidJavaObject__ctor_m67B4EEAB015B123D5A3EDCAD914B4795A3B67F04,
	AndroidJavaObject_Finalize_m87374EE46B27BE3559CACED8A1B62475200AB5AA,
	AndroidJavaObject_Dispose_m87886676A84FA079C0FE45E6C31D790D764652BE,
	AndroidJavaObject__Call_m4C4D7D7287030773175BDF47681EA018DFA4DF1A,
	NULL,
	NULL,
	NULL,
	AndroidJavaObject__CallStatic_mD63902D30CD5626DAEAD1D6484AF7A9ACA85590E,
	NULL,
	NULL,
	NULL,
	AndroidJavaObject_AndroidJavaObjectDeleteLocalRef_m2ECEEAF6389ABB9D6B963B8A98568ECD9413DF3C,
	AndroidJavaObject_AndroidJavaClassDeleteLocalRef_m56C84D7516BCB51A84E8AFDB3FCA46BAF494548F,
	NULL,
	AndroidJavaObject__GetRawObject_mC5B8B60BEF515F5EE2A113D60991A433DA740C69,
	AndroidJavaObject__GetRawClass_m470EAEBF8B0BD365FD13F1C6F55119836452FDFA,
	AndroidJavaClass__ctor_mB5466169E1151B8CC44C8FED234D79984B431389,
	AndroidJavaClass__AndroidJavaClass_mF481A9584D78F32C64219FDA49CB84B6F0A017DD,
	AndroidJavaClass__ctor_mB206D3CB990755BD56E308F61CD43BB9EA4421D0,
	AndroidReflection_IsPrimitive_mA41A9ECECE3D73679C79DC8B0FDD32B59570DF25,
	AndroidReflection_IsAssignableFrom_mBAE0D5121AD208959B89E9BDAF62F0E0BAB917C6,
	AndroidReflection_GetStaticMethodID_mF8378F28D71354360CA9F8AAF8EAF24A3B9594CF,
	AndroidReflection_GetMethodID_m4318CE3C372444B966909092BF046833A9733EE7,
	AndroidReflection_GetConstructorMember_m6380904C6B5AF39F973E79B13B3C2B53DFD759B3,
	AndroidReflection_GetMethodMember_m2871C6DC2BA1AE5FF3FA448AC36022CC8B19C9EA,
	AndroidReflection_GetFieldMember_m6E589FB4DEDCFEE84B2CFD9C416D0C21EDB09D2F,
	AndroidReflection_GetFieldClass_m88D4993FF662A0EF387AADD915A4FD8054F0DF27,
	AndroidReflection_GetFieldSignature_mF36DDDDB3E57742AD66487E2D7CECBE81736B259,
	AndroidReflection_NewProxyInstance_m052E9828C670A85F3B9DD9D4632A9A6B52FB5EDF,
	AndroidReflection_CreateInvocationError_m20254A88DCE4AEE011AAC3D42B94DC7B588AB20F,
	AndroidReflection__cctor_m59365F63F057115EA8FBCB0AFBAF94012DAAC622,
	_AndroidJNIHelper_CreateJavaProxy_mAC059F7C2716D4E9099EA7268F5378260E6DDF36,
	_AndroidJNIHelper_CreateJavaRunnable_m25981D4249F420FA78240CDC2F7E53B9EAD5F31A,
	_AndroidJNIHelper_InvokeJavaProxyMethod_mCA9BD35B5AE99B6DFABBFE9968DD1EBE931F70C2,
	_AndroidJNIHelper_CreateJNIArgArray_mFB22FA0559B96CAA29D2A63EA56EF62F0280AA39,
	_AndroidJNIHelper_UnboxArray_m5A46896F07017C9B0908AE3D72DA1285071DEA4A,
	_AndroidJNIHelper_Unbox_m7D786B14E4A90E5411FC9AA7F985451DEBC95731,
	_AndroidJNIHelper_Box_mA0B7B8444C6AC2BABBDC7B1A7FACDA3EA6F816B0,
	_AndroidJNIHelper_DeleteJNIArgArray_m3920D9869CEC7D9898B83ADC4457509D94897473,
	_AndroidJNIHelper_ConvertToJNIArray_m6E451CD0C15DE2812F103C604CB1EF1D358D1213,
	NULL,
	_AndroidJNIHelper_GetConstructorID_mD9CF17C7FA280BED12A8BB5D6F12E0D2C6778C3F,
	_AndroidJNIHelper_GetMethodID_m185ABE7B65325FA580775AF81FE17687994C98DD,
	NULL,
	NULL,
	_AndroidJNIHelper_GetConstructorID_m89A45100B1A05DEBF6586AB234A7DBE2044B2490,
	_AndroidJNIHelper_GetMethodID_mECADE8D5FC2712DED913CDECBB9D7E8C46812184,
	_AndroidJNIHelper_GetMethodIDFallback_m7155B2DE7C4F4E71C6AE29F856A1A9618ADAAD4F,
	_AndroidJNIHelper_GetFieldID_m698CE89D968CF7A131861E95FF72272A6A14FB95,
	_AndroidJNIHelper_GetSignature_m414A7A6B98FB5565075E4C51F22C482F2BCEEF5F,
	_AndroidJNIHelper_GetSignature_m309D35CC998B59CFEDA2D6EB5D0E221B3F1374F7,
	NULL,
	_AndroidJNIHelper__ctor_m0535562F59B589E117E57B8EA07ECE900848F509,
	AndroidJNIHelper_get_debug_m0F67D4E412EBE1D511C42F5F12294C63E669C907,
	AndroidJNIHelper_set_debug_m66E40D20DC9DC69BD7B139DA5C367BA1185FCA3F,
	AndroidJNIHelper_GetConstructorID_mCF5EAC779FFBD1129C2E28FE1C2171E6FF8AAE95,
	AndroidJNIHelper_GetConstructorID_m2D883140A087C1CDB74FE9195D14643CB9A854F0,
	AndroidJNIHelper_GetMethodID_m5D0526B2FE20191F966D72521647D686980EE06B,
	AndroidJNIHelper_GetMethodID_m58B68CA9B567BF23064BD8891CEA509FF8FA7C76,
	AndroidJNIHelper_GetMethodID_mDB705DC228B1BB30E6595068797FB3F2A1817BB8,
	AndroidJNIHelper_GetFieldID_mB209E2F32D04CA5BCD7D6EC10A0C8DC0ED5D0D3B,
	AndroidJNIHelper_GetFieldID_m736A1E39AEE90F1DD3C352FA3F1988D4CFD9778F,
	AndroidJNIHelper_GetFieldID_mDA4775DFA37126A7EFEBCA8E68EF9640F3D6EF01,
	AndroidJNIHelper_CreateJavaRunnable_mBEE8C5060C69EE5C6922D4BA06C261055A3FF99F,
	AndroidJNIHelper_CreateJavaProxy_m75CA3C0BF15517CD52658E08F9FCBA1022822E6F,
	AndroidJNIHelper_ConvertToJNIArray_mBEAE4605FF297D19AFB8CE4E8443C9C0F87E9A13,
	AndroidJNIHelper_CreateJNIArgArray_mC12C279EEB43D740F42C5E90301DC54F6E04B876,
	AndroidJNIHelper_DeleteJNIArgArray_mBDC874B32FF09E6B48E18B2A58794C0A4DE2FA23,
	AndroidJNIHelper_GetConstructorID_m0FDAC24E463246206BA8FBDE44B4A73D6CBF3D40,
	AndroidJNIHelper_GetMethodID_m283E294AA8DD3F6721A4173CE4C31038B98AA7E5,
	AndroidJNIHelper_GetSignature_mE8DBE8ABB6506BBF2806880DA2FBB876DD88A047,
	AndroidJNIHelper_GetSignature_m1B2D7B48C9E9D7BB1F10AD60D541EA7F9BE18F45,
	NULL,
	NULL,
	NULL,
	NULL,
	AndroidJNI_AttachCurrentThread_m412647220ED1F7D42F9D41F692D1D7DC4696B6D4,
	AndroidJNI_DetachCurrentThread_mC0F3D8974C0681528B801EF0AC2A54E5C7A2E933,
	AndroidJNI_GetVersion_m8609243FCDE009D1C5DEC7C7E40664386D5C4608,
	AndroidJNI_FindClass_m6E9908108F8D828563AD510C5A355E71A9A493E9,
	AndroidJNI_FromReflectedMethod_m1613634C8A528B0A2A1F6828B609F31792B7DF43,
	AndroidJNI_FromReflectedField_m88709F5C3EDD6CB503B1C6EE32CFF6BC328171E8,
	AndroidJNI_ToReflectedMethod_m50B8B87366FDAB0506EF2023352AB602E14CA7E2,
	AndroidJNI_ToReflectedField_mEE3F5294258CD32FB6066A2EB256B75B04B19D3B,
	AndroidJNI_GetSuperclass_m755BF9AA2AADBE96A2C64F15AE8114BD1807AD6A,
	AndroidJNI_IsAssignableFrom_m28DB6B523875A7837F7CC60807F32D58E90F9C25,
	AndroidJNI_Throw_m7DE4EC0AF3864EDC05EE828A57B20A1EB0C71F5E,
	AndroidJNI_ThrowNew_mDEAF3A3716A9F8D6A9DF48B6928D17740C0112AE,
	AndroidJNI_ExceptionOccurred_mAE2AE7C57E06059383EDCAB956A01BDF7D03F54C,
	AndroidJNI_ExceptionDescribe_m20B24A293E6A8467E312E028E6AA250934684508,
	AndroidJNI_ExceptionClear_m1205CD178ADB8E0E7EBDBE349AFA767D5D7758B8,
	AndroidJNI_FatalError_m6F93C97D6802B5FFB6A6A99D16C74234E79AF5D6,
	AndroidJNI_PushLocalFrame_m2D8050A3799AEBB4A7E506E6790839EB66932E10,
	AndroidJNI_PopLocalFrame_m32AF6F9065F09D80BFDD3F573B62C782F392E609,
	AndroidJNI_NewGlobalRef_m9A06F23234FB1ECF3F482AF3A6A6148A5916E9A7,
	AndroidJNI_DeleteGlobalRef_mC50B6C056F32BB9F44B800949FA169C728D4C41D,
	AndroidJNI_NewWeakGlobalRef_m27D74DAC1D1F0A11796E4FA669D8CB8DBB1AF2BA,
	AndroidJNI_DeleteWeakGlobalRef_mA1F19C1656B86A22A339497C335C156648736E6D,
	AndroidJNI_NewLocalRef_m286E59F912B94D07D1CE54DFE93A631B2162CD65,
	AndroidJNI_DeleteLocalRef_mD2A2B4F1C17A4F5863BB94F88F268E72FD120DBB,
	AndroidJNI_IsSameObject_mA37D2BE7C0E40F30E6D438A937B038E7703DFDAB,
	AndroidJNI_EnsureLocalCapacity_mD27645E03F7F82811D5AFFF6B068E226C9C93227,
	AndroidJNI_AllocObject_m5E81D0A72F3DCDFC5D0AD62C2B93816083EE3926,
	AndroidJNI_NewObject_mF026198FBA1D5E69719DEB52F41E9FDB8B7F93A4,
	AndroidJNI_GetObjectClass_m418C2D7DAE432AD104209833A0DF06B16E1E74B5,
	AndroidJNI_IsInstanceOf_m09B386C15D3FBD4A6589D9F6DD3E5F8D259F51B1,
	AndroidJNI_GetMethodID_mA7FF961764CA4D68C4789E5A17926CE5FF9B3549,
	AndroidJNI_GetFieldID_mC6BFB1F17EF5FC82D45F08D25C11D346E51673F2,
	AndroidJNI_GetStaticMethodID_mA13B58796C4E210B46956723FE664B6D0130C5A3,
	AndroidJNI_GetStaticFieldID_m89476A442BF57C00C1CBB0DA588077C2B4171654,
	AndroidJNI_NewString_m6F3143989EFE907B5D0091850D1754421795A39B,
	AndroidJNI_NewStringFromStr_mF56D4A6456A326916DEF2E148E826F6EBC93B3CC,
	AndroidJNI_NewString_m74F9DCF2F56735C9BE6DC3831134A96C7BCA9F9F,
	AndroidJNI_NewStringUTF_mB6A0A0A1C3F931503A6329D4E232DB501B95B5B0,
	AndroidJNI_GetStringChars_mB61E4F713A2457F7DF053DBFE4455A559DA9623C,
	AndroidJNI_GetStringLength_mD2A44213EB9B131E1DECEF34A315F2B817384760,
	AndroidJNI_GetStringUTFLength_m5BE0CC7EE4108BA654A9F7647E66871D831B812A,
	AndroidJNI_GetStringUTFChars_m61CFD69CF31D17C2728F23656079D3E26D9D2BBB,
	AndroidJNI_CallStringMethod_m52FAF2826B75AF2AEA8F848AEC973A682216EC4C,
	AndroidJNI_CallObjectMethod_m21944B19534CA3EB8885BE3F1057E6A72AAC4355,
	AndroidJNI_CallIntMethod_mD1299CB9F99E26D2734A0F02D376DE6EF49F1741,
	AndroidJNI_CallBooleanMethod_m78FC31B29854285F1164C75974AB463FE5716F84,
	AndroidJNI_CallShortMethod_mC3C11BD9E32604C41710756104D5A8AFCFA792E4,
	AndroidJNI_CallByteMethod_m7E2B355303C36B0992364D036BFCCF1CB4DD8154,
	AndroidJNI_CallSByteMethod_mA98E61BB8186A06CBF4A175E29E2F0F194FB8507,
	AndroidJNI_CallCharMethod_m560F0E113CA6E4F4A73BDAF93D53DADDA8D2047B,
	AndroidJNI_CallFloatMethod_mF94056CFCC7E045F7B350D2D3285335482A2AE8E,
	AndroidJNI_CallDoubleMethod_m9001B2EF56623D6F17B4E9E87788CDD3E760A897,
	AndroidJNI_CallLongMethod_mDE82FA5F26CBE0E0F8251D3A7698D376524E1A4B,
	AndroidJNI_CallVoidMethod_mFCFF6A5FF4A51305C3D2DBFB3A0699617EF40D48,
	AndroidJNI_GetStringField_m6A2FA57794ADA8735B103FF5D4819F3C4813992F,
	AndroidJNI_GetObjectField_m5E3C5FF1582F4A62155220FDCB849574C0E36AD6,
	AndroidJNI_GetBooleanField_mC900428E2FD1E55BA21808A31B760FB0F10BC047,
	AndroidJNI_GetByteField_m675BEFB1024363DE587C000406B8A09C5762B847,
	AndroidJNI_GetSByteField_mB81412A4748ABB311535E2B73092569AAE6CB735,
	AndroidJNI_GetCharField_m7DA94A98ED33A97EE7874872C9F51515F151F019,
	AndroidJNI_GetShortField_m5EEACBB3920623AD54D9DF77499E8BA92B35E3D8,
	AndroidJNI_GetIntField_m6B78A3F6F8EE6D1ADEDECF1EC145BC9C5AE37E88,
	AndroidJNI_GetLongField_mC2DC315C44320CE9A406B95A4CAA1117A0FF56A8,
	AndroidJNI_GetFloatField_m5C92103D7307A19F72F28DD40118F84D91C19A39,
	AndroidJNI_GetDoubleField_mE2B23D9F1363B48811B6207BEF8A18B39CB3B22B,
	AndroidJNI_SetStringField_mE9B2983BC7C61C0EEEA3FA31484B570B48E210DC,
	AndroidJNI_SetObjectField_m6BA777B66D76ECD1E34D69D800A8F2F51C51026C,
	AndroidJNI_SetBooleanField_m9A22242BD25A8B3802C05F70C2EB5ACE7E8BF2A0,
	AndroidJNI_SetByteField_mA1EBC3D4A3734B8064330782DC240DD1775C7C4B,
	AndroidJNI_SetSByteField_mA360303CC36670BDC442E1366D64333A5363D09F,
	AndroidJNI_SetCharField_m86543FBDB219D090EFEA141F679CCD22E195B680,
	AndroidJNI_SetShortField_m8811FC677647B47F855FC9533D51437517B53066,
	AndroidJNI_SetIntField_mCAB8E0B5C4F1773F6CAF81731DFB224FB78F0138,
	AndroidJNI_SetLongField_m763D39D8B341907F54921AF5EFE4E0C37EB44B00,
	AndroidJNI_SetFloatField_m953B7DC631E56432E2AB59154CAC15EE10B28E02,
	AndroidJNI_SetDoubleField_m30FB81E1DEE48DB3C22117F3D017813A204B474D,
	AndroidJNI_CallStaticStringMethod_m7E8E3AFF8296764C324060E65B052B23500C18AB,
	AndroidJNI_CallStaticObjectMethod_m5D0C02761602E6ED1AE4FAD90B7762A6376A35D5,
	AndroidJNI_CallStaticIntMethod_m7AA48D4603F398E99E45DF3E057BB58FB6D374FC,
	AndroidJNI_CallStaticBooleanMethod_m6D035B0525AF900D6BF3F91C174C093FE2531453,
	AndroidJNI_CallStaticShortMethod_m10E61636B448E36034CD6DE902A286B0257C7586,
	AndroidJNI_CallStaticByteMethod_mFC6A3C4731086DD6CD298C5EDC86C0211B90C69C,
	AndroidJNI_CallStaticSByteMethod_mA2BA2E72F9CF73A04E15420CDCCFD7CFD809E0DA,
	AndroidJNI_CallStaticCharMethod_mCC7C287BA2CD34A0526577D194237277F9708353,
	AndroidJNI_CallStaticFloatMethod_m19DBC375D9E707CA40BE8D997C84F553EF71040A,
	AndroidJNI_CallStaticDoubleMethod_m052484ED56097C439FA22A89CA0FE393BBFFD305,
	AndroidJNI_CallStaticLongMethod_mC103D7C5C92E7DC15B7AC043BD5D7FE398F559AC,
	AndroidJNI_CallStaticVoidMethod_m2DB4A797A541A547320D853111F8D1E5D27D9C5E,
	AndroidJNI_GetStaticStringField_mFE7F821C85A677C32C199BB9B23CEB66A523A977,
	AndroidJNI_GetStaticObjectField_m52268140CD4BD65B9FAC976669DBBD65D763731C,
	AndroidJNI_GetStaticBooleanField_m6BC154F7001DA04748F5F96F61878A3D6205ECA4,
	AndroidJNI_GetStaticByteField_m1817BBECBE096B84C719026A308F3F0961025070,
	AndroidJNI_GetStaticSByteField_m0A5D05E28F47C16783818258361281644C5C6585,
	AndroidJNI_GetStaticCharField_m3B1D9B99424A25FB6F665DA504125C0F20CEC0BF,
	AndroidJNI_GetStaticShortField_m66353DB84BAFDD75B35914D8AA5056AC6B3C0BDB,
	AndroidJNI_GetStaticIntField_m039F7CB6BD326410250D18A49836F55CD1DD87F9,
	AndroidJNI_GetStaticLongField_mD403EAC792740D06B021D1E9D34D25CAFEE59194,
	AndroidJNI_GetStaticFloatField_m68704C9BF92DF84E6982FCB03EAC0935F3934399,
	AndroidJNI_GetStaticDoubleField_mF882F4F690FE87E2A81D8779BB62C905DC217700,
	AndroidJNI_SetStaticStringField_m42D87A914D4AD4DAE0B66661BAE6B708F4ED3AF8,
	AndroidJNI_SetStaticObjectField_mFA62D317DCE3F48E5D63FBA0AC3464977C0459A3,
	AndroidJNI_SetStaticBooleanField_m18BC2337480DF6ED4F40F9B674D5DAA19225F3D2,
	AndroidJNI_SetStaticByteField_m22249B7319EA4C4751995AFAE6CB41317EAF7190,
	AndroidJNI_SetStaticSByteField_mCA2EA01B1AD261349CD5BE1E3F7AD43A8596837C,
	AndroidJNI_SetStaticCharField_m73B891760479DB05E0C3EC3D60F90D4503AA67A0,
	AndroidJNI_SetStaticShortField_mBC6DC87D8235408532C23E5CDB19C178C5F2D77E,
	AndroidJNI_SetStaticIntField_mCBEADFA609B9541779AC51B1FE200B50D51C10F7,
	AndroidJNI_SetStaticLongField_mEEDC663D7A85F4DAEE0CA7935EF5E27CD377E5FA,
	AndroidJNI_SetStaticFloatField_m836FCB6A7FB278526013F0C7BCAFCE33F2D7C016,
	AndroidJNI_SetStaticDoubleField_mF7F4869A92C98895DC79F0EEBEB0DA0C576CBDF7,
	AndroidJNI_ToBooleanArray_m06017AECA409DC3207C993113FA1F88277F1D71B,
	AndroidJNI_ToByteArray_mE72C1AF34FE140D36F10A0386454137D4550FBDD,
	AndroidJNI_ToSByteArray_m1307FD21FE087877599392D80474D56EA03AA0B8,
	AndroidJNI_ToCharArray_mF58CDE4DA760CD4518E8F5F4DAD7A885B7166C5B,
	AndroidJNI_ToShortArray_m28E2EB565D92A8A0396646B0E3EBFF80A7E10246,
	AndroidJNI_ToIntArray_mA5B1AF83EE6484437CABB1485875E86A5EAA8208,
	AndroidJNI_ToLongArray_m53576F1D2526D6021B07FF19F4F3C220B13A4A92,
	AndroidJNI_ToFloatArray_m61F723D6040BFC3A3622EE08E0EF9BBCE2E8E88B,
	AndroidJNI_ToDoubleArray_m368EFFE8C4387F994423DFC4DA5834A4D4B1EC0E,
	AndroidJNI_ToObjectArray_m4843C4E669DDFDA28853BB0D627A4A30DD0E9944,
	AndroidJNI_ToObjectArray_m86E80FA6CB35FF8AF0B5611106934EE3C9FC59D3,
	AndroidJNI_FromBooleanArray_m95D7BE45F113A7576DF27BF14BBDC35DD7748A67,
	AndroidJNI_FromByteArray_m5C52B7F13653B39F42FFB7FEB1B665FAC07F0388,
	AndroidJNI_FromSByteArray_mFED4929D339523808AE9C94F3C2AB3A317E9C5E1,
	AndroidJNI_FromCharArray_m7149E127743A7D659017D1E1C3B174C3D615C638,
	AndroidJNI_FromShortArray_mA89CCCFED02DDFDA91835418DAD8211A4B7BDDC6,
	AndroidJNI_FromIntArray_m5B8A47C4B6FDD607B3A67B02D4D1297B4C11CA6A,
	AndroidJNI_FromLongArray_mB042FE2F3D5AC91673FE72145E98D04B8775BE36,
	AndroidJNI_FromFloatArray_mBA9EB0CE3EC9662D669877E2D7DA004B794C4331,
	AndroidJNI_FromDoubleArray_mF5D9E2F0D26862F10C98E8ECC8EB436EB9804692,
	AndroidJNI_FromObjectArray_mF29F2969BD34276ECCA7ABA7ADDD34C04694E445,
	AndroidJNI_GetArrayLength_m7C02A09EAFEC667B3E8EBA9A06177E22E61028CB,
	AndroidJNI_NewBooleanArray_m9C7018583B95EC216E181204717267902A426029,
	AndroidJNI_NewByteArray_mADACAA676D3E057D1C6109D8353EB704D10E7806,
	AndroidJNI_NewSByteArray_mFDAF396EF3C3CC7C315C20F1B7E14B2B51714F41,
	AndroidJNI_NewCharArray_m801332FB86A2CEBF424B046128C4C0E8F7D5D80C,
	AndroidJNI_NewShortArray_m6CC9E93F24ED8BFC02A13D89DA95E6F17276BCA6,
	AndroidJNI_NewIntArray_m46F3D6CBFA7BB4D79BDBB0971E68DE459A9F5D99,
	AndroidJNI_NewLongArray_m707798711EAB93F83F0F2E2489C13C9AFA6886D7,
	AndroidJNI_NewFloatArray_mDC04BC46000F25D8D640A2DDAB36F9C81BD496F7,
	AndroidJNI_NewDoubleArray_mA3DFC7AC3EC990D498539B59094FB3CEE4229E57,
	AndroidJNI_NewObjectArray_m4EAB5EA40119977AAD41793C78A3C19FF19A7043,
	AndroidJNI_GetBooleanArrayElement_mCEC9DA5F142E7F4DCF70453B8B0D506720D8F4F6,
	AndroidJNI_GetByteArrayElement_m4E66A92347AFB54172A7483F1F224A36C927C913,
	AndroidJNI_GetSByteArrayElement_mD8BC0A3483C53C6DB56EEE74274E71F7457B4DC2,
	AndroidJNI_GetCharArrayElement_m46AE455A6DB4CE743D19B26986A7340C9EBE4EC4,
	AndroidJNI_GetShortArrayElement_mEE788A8EDA8C2D81C30B5783B26983ACD908F1BD,
	AndroidJNI_GetIntArrayElement_m8D21128A83A398C97034293C4232487F6DEE8B52,
	AndroidJNI_GetLongArrayElement_m1043DAB33A9DCA3BF5EDBA12F5D3121DFCD21BBC,
	AndroidJNI_GetFloatArrayElement_m432455F5B77316342396460CF547335798BA7E64,
	AndroidJNI_GetDoubleArrayElement_m2A6B6A5F27DC6CDC378797E525402C238CD65E02,
	AndroidJNI_GetObjectArrayElement_mC4CAF9744617F69EFCD95B71D95492DA20A0FACE,
	AndroidJNI_SetBooleanArrayElement_mADEB936138A96C081CCE560B6F11C427C4729292,
	AndroidJNI_SetBooleanArrayElement_m16CF5F014FABEB28253AACFC93D4FF113D13DEC3,
	AndroidJNI_SetByteArrayElement_m484197612F5E5C163F9116A0F63B0355823C375F,
	AndroidJNI_SetSByteArrayElement_m470F13FC7EA450CB5B113641F99EF040E19E708C,
	AndroidJNI_SetCharArrayElement_m56BE8F363275BF93E558F4D4BF6042DA9CDF1A39,
	AndroidJNI_SetShortArrayElement_m8D2E6451D917D5452770325BE62DC667DFA26DBF,
	AndroidJNI_SetIntArrayElement_m66DF089843878DC016F15596A173906A2804E555,
	AndroidJNI_SetLongArrayElement_m54F052B44CF922C9675C31BF32B4B3726E67AC79,
	AndroidJNI_SetFloatArrayElement_mF3230F001486735FB129DD4117DD01260C998343,
	AndroidJNI_SetDoubleArrayElement_m82F4EBCB94088644F17F30C7AF48475E31BE5211,
	AndroidJNI_SetObjectArrayElement_mAEA12A91B1C20BF46CBFB5DC3B1D5AF95AA463B2,
	AndroidJNISafe_CheckException_m465A2955F921417ED62C035390E550889E93F8DC,
	AndroidJNISafe_DeleteGlobalRef_mD4ECCC474E98F40B65184E138EF9D84A4F4AAAA0,
	AndroidJNISafe_DeleteWeakGlobalRef_mBC786B6240AB03EA493A71A43D4297871FFC679A,
	AndroidJNISafe_DeleteLocalRef_m20303564C88A1B90E3D8D7A7D893392E18967094,
	AndroidJNISafe_NewString_m6A9EC18D8B122E7B901DB6BF469BFD38D1E8FE5A,
	AndroidJNISafe_GetStringChars_mE246814CD8FF4EDDEE6EBF107367C4A8EAF03849,
	AndroidJNISafe_GetObjectClass_m6FD815CB0F9760199ACD03D16FC88FED055BC9F3,
	AndroidJNISafe_GetStaticMethodID_mAD5134FF6DE446852F3F28B791C15ADBD5E9E5E8,
	AndroidJNISafe_GetMethodID_mF095B57A77BE529D51F369D94B66D14C2BC88536,
	AndroidJNISafe_GetFieldID_mAD9554C6DCE9389C441A9AB556001211B9B2663D,
	AndroidJNISafe_GetStaticFieldID_mCCCE792F7BE47B6370951D417CCB1E2713DBF482,
	AndroidJNISafe_FromReflectedMethod_mED131988778BF0267C4CE711854D4BC26D0D960B,
	AndroidJNISafe_FindClass_m2E8072B600873B4D87B2197C1168967050208D1B,
	AndroidJNISafe_NewObject_m9DF3572307421E944B3A95DC82EF90CEF1774DA2,
	AndroidJNISafe_SetStaticObjectField_m7757F7E30F8122DAF89F138A8AE727CB896BC721,
	AndroidJNISafe_SetStaticStringField_m445D977B2374056C6E4607FAEDB7E99A1353E2EE,
	AndroidJNISafe_SetStaticCharField_m2B8245275C36525798C869B7B1088B25BA663613,
	AndroidJNISafe_SetStaticDoubleField_mA0253927D476917D2158A9CE29F1BF535485B956,
	AndroidJNISafe_SetStaticFloatField_mB2EDDE632AB2088CD12F1FD12174FB86990BCBEE,
	AndroidJNISafe_SetStaticLongField_m299AAC2DE8B6747B0B5E109BABB2F3A4FC1F486E,
	AndroidJNISafe_SetStaticShortField_m92534AAA86D7E1055E12936C8A7BD6B865B7DB81,
	AndroidJNISafe_SetStaticSByteField_m242120982A9227E1E8344FFE9F06FD74986D15E9,
	AndroidJNISafe_SetStaticBooleanField_mBE4E40DA1B07A29D356AEEE6CB9519F2B3621AC9,
	AndroidJNISafe_SetStaticIntField_m1E20F6C72260CAFBF73207DCEC1816B2816EEBE1,
	AndroidJNISafe_GetStaticObjectField_mB6B9A9EB2619DFDF1DA56300BF9FEC19BF883867,
	AndroidJNISafe_GetStaticStringField_mB3D1325B08A38C7DAF1FA3E6CB52F6D8E0A2CB47,
	AndroidJNISafe_GetStaticCharField_mF70F6D197261364AF2A9E875D84DDDA35BD0ED96,
	AndroidJNISafe_GetStaticDoubleField_mEB86F2CE1F3879AAA9DEDA4B496F882C0E1DCBC2,
	AndroidJNISafe_GetStaticFloatField_mD1456B729026959309A839C2647279C0B6541356,
	AndroidJNISafe_GetStaticLongField_mABC2B933CEB757E3FAF1FD6C60AA0C4D38E9C49D,
	AndroidJNISafe_GetStaticShortField_m83716D4D85B30F26803F866AC47D5C04AAB5D320,
	AndroidJNISafe_GetStaticSByteField_m77596E5B1AE58DAFF39268AC954CAD53974A688D,
	AndroidJNISafe_GetStaticBooleanField_m172BEAA3F0AB6754EA5F1AD30C36DAA0D3D7C666,
	AndroidJNISafe_GetStaticIntField_m0698D50C44E490A009E8388C7321630DED5973BD,
	AndroidJNISafe_CallStaticVoidMethod_m7186730B5574226E150225A11B48B4B15FC74020,
	AndroidJNISafe_CallStaticObjectMethod_m545474765D15AC9B0144192760B45BAA963B8F5E,
	AndroidJNISafe_CallStaticStringMethod_m8BD92117111558CC00540B45437B4A90222B89BE,
	AndroidJNISafe_CallStaticCharMethod_m12390A6D01F33879740A94195E445D7B3D4CD652,
	AndroidJNISafe_CallStaticDoubleMethod_m0510A1F18E0ED9CE8CFA82C6B2A56DCD89814247,
	AndroidJNISafe_CallStaticFloatMethod_m46EE9FEEEFCF68FA1A014C2E417454BA55206945,
	AndroidJNISafe_CallStaticLongMethod_mE3487C989E9D5EFA0FB2356408B7FF40E61DBD12,
	AndroidJNISafe_CallStaticShortMethod_m47EFDF3473E6C4D6F61956725E6595A0B919589D,
	AndroidJNISafe_CallStaticSByteMethod_m68A45FE3EEE495648B36902B51ECDB76385EE736,
	AndroidJNISafe_CallStaticBooleanMethod_mA65977CB0FC4E3759938FCBD7BA43330317E8A3D,
	AndroidJNISafe_CallStaticIntMethod_m558318BA8E7A0A7526E48FF7218876FB987F397B,
	AndroidJNISafe_SetObjectField_mFE500926F9C963FF106E8AA30A16F4C671BAA8CA,
	AndroidJNISafe_SetStringField_m649363D4E87763D6A9760359EAFB29802E90B409,
	AndroidJNISafe_SetCharField_m69D09A6A2CEA55D84B240FE32D90300AAB1334F9,
	AndroidJNISafe_SetDoubleField_mE93D0C5EC2019A1B657BD32970FE6EFC9B005A58,
	AndroidJNISafe_SetFloatField_m589CA6B8DD2BFD4515C5AEAE3772782B293F02C3,
	AndroidJNISafe_SetLongField_m13905547F5CDC7E01AB0D8C787BF98DC2870EC35,
	AndroidJNISafe_SetShortField_mF95E569C142DEDD604CE8BA7617328B3EDDD2F0D,
	AndroidJNISafe_SetSByteField_mB021168746571E7CAA8C0EAD7AA7F02C18B5EE33,
	AndroidJNISafe_SetBooleanField_m5279EA41B214699E79733DC6C93259CC9DCA1D9E,
	AndroidJNISafe_SetIntField_mD238DA37BA1B3D7693484237951A6EFEA9C62120,
	AndroidJNISafe_GetObjectField_mCF3BB1C38718D6F55081126BC7F6C286B382B275,
	AndroidJNISafe_GetStringField_mADFCA05D6DE790600B57E90B20F2E75AFC036B0F,
	AndroidJNISafe_GetCharField_m8301FA96B40E27C032590FE3F8E84A777A4739C3,
	AndroidJNISafe_GetDoubleField_mBCBD5E80223EDECC06FA783F34149E3625219074,
	AndroidJNISafe_GetFloatField_m1EAA1ED33002BBA28CA2B630521D6BF1B7D3A2E7,
	AndroidJNISafe_GetLongField_m7DD751358D10BB276D8A95D413B9DFB1E8EE81D8,
	AndroidJNISafe_GetShortField_m5D21E87061C1DAC89DF58671C53432D0361F0C6E,
	AndroidJNISafe_GetSByteField_mAD3B08AA8A97F77CAE17DD25B0F389AFAC2023B1,
	AndroidJNISafe_GetBooleanField_m34F37B560A6AEC81B9061FB3B72698C84720435D,
	AndroidJNISafe_GetIntField_mBD983688B73063DE5C55D320F60F266443FAC97C,
	AndroidJNISafe_CallVoidMethod_m4918322816B85C931BD396C8CD37AC77C9A13110,
	AndroidJNISafe_CallObjectMethod_mBA06053048352614B802E9429FFF50C4A1B56057,
	AndroidJNISafe_CallStringMethod_mF2975892926478EF6ACB9D62CCC0829A8BDD2699,
	AndroidJNISafe_CallCharMethod_mAA4D432267ED412D5F5D804B9038BC7D9B2DCDE3,
	AndroidJNISafe_CallDoubleMethod_mE26233E7C97901A772CAA39C02CDA51AEF0DFCFE,
	AndroidJNISafe_CallFloatMethod_m1DFC594E3141D409B0F7341455FEE45E5C141504,
	AndroidJNISafe_CallLongMethod_mE101DBF4C4E8832E2BB28F0471EA9A824CC10525,
	AndroidJNISafe_CallShortMethod_mB007DECD38E377B824B170DE90CDD9F65FDF8DFC,
	AndroidJNISafe_CallSByteMethod_m15A10302255F3A4207B663C0A97878C433233DDA,
	AndroidJNISafe_CallBooleanMethod_m0C8E5B12826ACB3AA868FF52B22C57BA107A1DF5,
	AndroidJNISafe_CallIntMethod_m88ECF62449F26B13AE4C2B7E8B3C0E503970782B,
	AndroidJNISafe_FromCharArray_m54EDC9D2BE92F9973F4E00EE953EE242B231EA96,
	AndroidJNISafe_FromDoubleArray_mDEA8F2C7854101272F3A2733F351B570AAD5D9D9,
	AndroidJNISafe_FromFloatArray_mDE02985159EEFD2CB28611C797AC21DE8B6300B8,
	AndroidJNISafe_FromLongArray_mC4D73C0DA27F212947AB85AA2030A35BECDF8288,
	AndroidJNISafe_FromShortArray_m62C0CB2D0BAE96D4B8CE365630361150EBE884FC,
	AndroidJNISafe_FromByteArray_mB06EF0FDBF6C738231E8F9D4998C38551131C4C5,
	AndroidJNISafe_FromSByteArray_m261D638D8B059AB777BEF0BEFDD0822717DFF2B1,
	AndroidJNISafe_FromBooleanArray_m36ED740401185EC0A959CA0F96A324A69E668646,
	AndroidJNISafe_FromIntArray_mC4C4DC70FFA39CD6E3E02FDAC7192324E6D4614E,
	AndroidJNISafe_ToObjectArray_m4C95D999242E900D9C70891E44100A5EB5020C5F,
	AndroidJNISafe_ToCharArray_mFBF42A984F1C5D618CD0366B3B344E2BF8856B12,
	AndroidJNISafe_ToDoubleArray_m6C1716EFF6DCA1AE3E04D292EB38A31E4132C1C1,
	AndroidJNISafe_ToFloatArray_m18207119C3AC0C5D71DA537B2CEB21D11301B732,
	AndroidJNISafe_ToLongArray_m67486F6D1F467D2354EEB74DACFDA79A1F3F7E0C,
	AndroidJNISafe_ToShortArray_m170C4D2D7D1ED3A02B4C707FB666BF4F2A9D02ED,
	AndroidJNISafe_ToByteArray_mB36D6ABE2FF31844554A353E136B2153BFDF0F65,
	AndroidJNISafe_ToSByteArray_m10BD1D36C8D2D7F764FD8C87742FD700DB48665C,
	AndroidJNISafe_ToBooleanArray_m94630C7B69D819D7BE683691B46879C6154B5F3A,
	AndroidJNISafe_ToIntArray_mE4647AC18D85206D98121752C3B8CD7D52A321EB,
	AndroidJNISafe_GetObjectArrayElement_m02B6993F13670DD2D1557D75EC31D8D860F10FD0,
	AndroidJNISafe_GetArrayLength_m809419F04C9BB93FED5B4A89F0539231C9B90E6F,
	AndroidAssetPackInfo__ctor_m747C0EAD6448BA479BAE3C5EAB67F205E0307972,
	AndroidAssetPackState__ctor_m5CB1F078A45558A0966BA32FCFE18CFC46CA941B,
	AndroidAssetPackUseMobileDataRequestResult__ctor_mB46211F6D3B3A421B1C1D9E05F3FC62858383E8F,
	AndroidAssetPacks_GetAssetPackManager_m91B486E7EEF9F019B820E11F10E1D877609778A9,
	AssetPackManagerDownloadStatusCallback__ctor_m03D0B212EFAEACF611D60596E978DD0468C6D936,
	AssetPackManagerDownloadStatusCallback_onStatusUpdate_m4042BE15C2B4A64CE1BAF4734E6A0BF8DD4FFC7A,
	AssetPackManagerMobileDataConfirmationCallback__ctor_m927FCB784F7D90524C6725B9E64EF8799538999B,
	AssetPackManagerMobileDataConfirmationCallback_onMobileDataConfirmationResult_m61FEFDED8787D798CF30CB78DC133505B7D3614A,
	AssetPackManagerStatusQueryCallback__ctor_m66F3B4A8BD8911F07121E2F8EF871741CE3A2CDB,
	AssetPackManagerStatusQueryCallback_onStatusResult_m8C41C5CC2F37808E5C5C12B023DF31E1DF96C314,
	Common_GetActivity_m33E672D97C605F07C4EE5F927F4C8D21F8D4F2B7,
	PermissionCallbacks_add_PermissionGranted_m74335D4200D9B1A7C80AB9C133F95C61FCDCDF89,
	PermissionCallbacks_remove_PermissionGranted_m4A3F9873FC159F89A2AD35F2FCAFF66A19813AF4,
	PermissionCallbacks_add_PermissionDenied_mE0B2826463785B050C999C70F443FCC3822563D0,
	PermissionCallbacks_remove_PermissionDenied_mF1A606ADE21F9520909126D3642B0BC2D6E994A1,
	PermissionCallbacks_add_PermissionDeniedAndDontAskAgain_mEDE8C00FEF2F649F10A47F30AC4ECB09E52DB9AA,
	PermissionCallbacks_remove_PermissionDeniedAndDontAskAgain_m55B04AE58C687946BDFA2094ED851518B2A1D68D,
	PermissionCallbacks__ctor_m91B14BBBC8913C131E400BA0D13576822AAE7A75,
	PermissionCallbacks_onPermissionGranted_m723440705B5B21B97AF5206716275BAE2A122E3C,
	PermissionCallbacks_onPermissionDenied_m0E05122B560DD62BB38178EE601E65854017980A,
	PermissionCallbacks_onPermissionDeniedAndDontAskAgain_m1191CF6422AFD8E8FE7BDBDBC04721D63718A5D9,
	Permission_GetUnityPermissions_m6A22A923A8B036209E6218E24F7AA8CA79AA15BD,
	Permission_HasUserAuthorizedPermission_mF4C90E13124E28F6F672200E489CC25A9B645B8B,
	Permission_RequestUserPermission_mF9CF3A21AAF34B311137C4D00B3AD6A6C2694242,
	Permission_RequestUserPermission_m7B8E817C03FDB5C99F22002C7181F27BF031F117,
	Permission_RequestUserPermissions_m09FE74BF5829C8B48A0E6C9B7F7846FEE763D2DA,
};
static const int32_t s_InvokerIndices[409] = 
{
	2644,
	6215,
	2647,
	6088,
	4855,
	6215,
	9734,
	6215,
	4880,
	6215,
	4880,
	4880,
	6215,
	2044,
	2044,
	3416,
	6059,
	6088,
	6088,
	6061,
	10922,
	2647,
	2647,
	2647,
	2647,
	2647,
	2647,
	6215,
	0,
	2647,
	0,
	2647,
	0,
	0,
	0,
	0,
	6061,
	6061,
	6088,
	0,
	0,
	0,
	0,
	4880,
	944,
	2647,
	4855,
	6215,
	6215,
	4782,
	2647,
	0,
	0,
	0,
	2647,
	0,
	0,
	0,
	9802,
	9802,
	0,
	6061,
	6061,
	4880,
	4880,
	4855,
	9570,
	8279,
	7732,
	7732,
	8468,
	7272,
	7272,
	9733,
	9802,
	7725,
	8470,
	10922,
	7726,
	9734,
	7731,
	9804,
	9804,
	9804,
	9804,
	8805,
	9734,
	0,
	8468,
	7272,
	0,
	0,
	8468,
	7272,
	7272,
	7272,
	9804,
	9804,
	0,
	6215,
	10850,
	10078,
	9733,
	8468,
	8468,
	7728,
	7272,
	8468,
	7728,
	7272,
	9734,
	9734,
	9734,
	9804,
	8805,
	8468,
	7272,
	9804,
	9804,
	0,
	0,
	0,
	0,
	10872,
	10872,
	10872,
	9734,
	9733,
	9733,
	7724,
	7724,
	9733,
	8264,
	9689,
	8432,
	10874,
	10922,
	10922,
	10090,
	9687,
	9733,
	9733,
	10088,
	9733,
	10088,
	9733,
	10088,
	8264,
	9687,
	9733,
	7726,
	9733,
	8264,
	7728,
	7728,
	7728,
	7728,
	9734,
	9734,
	9734,
	9734,
	9802,
	9689,
	9689,
	9802,
	7759,
	7726,
	7667,
	7558,
	7632,
	7558,
	7807,
	7843,
	7816,
	7619,
	7714,
	7976,
	8511,
	8467,
	8264,
	8264,
	8588,
	8627,
	8389,
	8431,
	8460,
	8593,
	8372,
	7976,
	7975,
	7970,
	7970,
	7977,
	7979,
	7972,
	7973,
	7974,
	7978,
	7971,
	7759,
	7726,
	7667,
	7558,
	7632,
	7558,
	7807,
	7843,
	7816,
	7619,
	7714,
	7976,
	8511,
	8467,
	8264,
	8264,
	8588,
	8627,
	8389,
	8431,
	8460,
	8593,
	8372,
	7976,
	7975,
	7970,
	7970,
	7977,
	7979,
	7972,
	7973,
	7974,
	7978,
	7971,
	9734,
	9734,
	9734,
	9734,
	9734,
	9734,
	9734,
	9734,
	9734,
	8471,
	9734,
	9802,
	9802,
	9802,
	9802,
	9802,
	9802,
	9802,
	9802,
	9802,
	9802,
	9689,
	9731,
	9731,
	9731,
	9731,
	9731,
	9731,
	9731,
	9731,
	9731,
	7723,
	8263,
	8263,
	8587,
	8626,
	8388,
	8430,
	8459,
	8592,
	8371,
	8466,
	7961,
	7961,
	7967,
	7967,
	7969,
	7963,
	7964,
	7965,
	7968,
	7962,
	7966,
	10922,
	10088,
	10088,
	10088,
	9734,
	9802,
	9733,
	7728,
	7728,
	7728,
	7728,
	9733,
	9734,
	7726,
	7975,
	7976,
	7979,
	7971,
	7978,
	7974,
	7972,
	7977,
	7970,
	7973,
	8467,
	8511,
	8627,
	8372,
	8593,
	8460,
	8389,
	8588,
	8264,
	8431,
	7976,
	7726,
	7759,
	7843,
	7619,
	7816,
	7714,
	7632,
	7807,
	7558,
	7667,
	7975,
	7976,
	7979,
	7971,
	7978,
	7974,
	7972,
	7977,
	7970,
	7973,
	8467,
	8511,
	8627,
	8372,
	8593,
	8460,
	8389,
	8588,
	8264,
	8431,
	7976,
	7726,
	7759,
	7843,
	7619,
	7816,
	7714,
	7632,
	7807,
	7558,
	7667,
	9802,
	9802,
	9802,
	9802,
	9802,
	9802,
	9802,
	9802,
	9802,
	8471,
	9734,
	9734,
	9734,
	9734,
	9734,
	9734,
	9734,
	9734,
	9734,
	8466,
	9689,
	205,
	1333,
	4782,
	10880,
	2647,
	201,
	4880,
	4782,
	2647,
	882,
	10880,
	4880,
	4880,
	4880,
	4880,
	4880,
	4880,
	6215,
	4880,
	4880,
	4880,
	10880,
	9570,
	10090,
	8805,
	8805,
};
static const Il2CppTokenRangePair s_rgctxIndices[23] = 
{
	{ 0x06000021, { 0, 1 } },
	{ 0x06000022, { 1, 1 } },
	{ 0x06000023, { 2, 1 } },
	{ 0x06000024, { 3, 1 } },
	{ 0x06000028, { 4, 1 } },
	{ 0x06000029, { 5, 1 } },
	{ 0x0600002A, { 6, 1 } },
	{ 0x0600002B, { 7, 1 } },
	{ 0x06000034, { 8, 4 } },
	{ 0x06000035, { 12, 4 } },
	{ 0x06000036, { 16, 3 } },
	{ 0x06000038, { 19, 4 } },
	{ 0x06000039, { 23, 4 } },
	{ 0x0600003A, { 27, 3 } },
	{ 0x0600003D, { 30, 2 } },
	{ 0x06000058, { 32, 2 } },
	{ 0x0600005B, { 34, 1 } },
	{ 0x0600005C, { 35, 1 } },
	{ 0x06000063, { 36, 1 } },
	{ 0x06000078, { 37, 1 } },
	{ 0x06000079, { 38, 1 } },
	{ 0x0600007A, { 39, 1 } },
	{ 0x0600007B, { 40, 1 } },
};
extern const uint32_t g_rgctx_AndroidJavaObject__Get_TisFieldType_tE541E61DC1EE486A3DDC10DCA2A2DD9A2A3BADE8_m06134DE4CC6EA8E3189542B792E94123E91E03C6;
extern const uint32_t g_rgctx_AndroidJavaObject__Set_TisFieldType_t5C6F84F3CFFB0874A4DA0D1C58053A25B835551E_m462146FCC7D2B3F83FF6E8999553D33E4959F5BE;
extern const uint32_t g_rgctx_AndroidJavaObject__GetStatic_TisFieldType_tBB418E296327456981AAF34C2BEB510AEC3C4E4D_m287CA0417FCCBDC7C54BA521F00661EE781AE83E;
extern const uint32_t g_rgctx_AndroidJavaObject__SetStatic_TisFieldType_t6076CC06F19BCC0301AE8C734F26CA429D2DB469_m63581F10344537651B9090C50B7BAFA8B9CDB1A3;
extern const uint32_t g_rgctx_AndroidJavaObject__Call_TisReturnType_t6A981EFC55AACA8DB2914A6B9C24AF2C1F0D86E3_mBA92180AE68E07DA29AB2ED05D52AA22E1ECEC52;
extern const uint32_t g_rgctx_AndroidJavaObject__Call_TisReturnType_t7C9CEFF53F7F785E3B0A2AA52BF0599DB9E4C7A7_m53C8581E9FA8AB194F9919FD0B5F910D4D0EDEDF;
extern const uint32_t g_rgctx_AndroidJavaObject__CallStatic_TisReturnType_t41223B870DEBD9DC66C7F3F6FDDF2CF6D061E4EB_mA272E4A1258A5DB68113DF8B90DF5B5B3A724832;
extern const uint32_t g_rgctx_AndroidJavaObject__CallStatic_TisReturnType_t94E13999E45FF70AA5DA5E427955FC4E439412B2_m7F6D9361C675DD63CF53A9BE7588D79625F85B4D;
extern const uint32_t g_rgctx_AndroidJNIHelper_GetMethodID_TisReturnType_t0FD1385ACD92B5652F803E183304929EDB7632D9_mAB39D5C2ACF4BF7098D1D76F31302A29A45D3825;
extern const uint32_t g_rgctx_ReturnType_t0FD1385ACD92B5652F803E183304929EDB7632D9;
extern const uint32_t g_rgctx_ReturnType_t0FD1385ACD92B5652F803E183304929EDB7632D9;
extern const uint32_t g_rgctx_AndroidJavaObject_FromJavaArrayDeleteLocalRef_TisReturnType_t0FD1385ACD92B5652F803E183304929EDB7632D9_m6244F277416780B7BE2BF89D0D01E635E59EF5DB;
extern const uint32_t g_rgctx_AndroidJNIHelper_GetFieldID_TisFieldType_tBD81DD51AB3076BC0134BD255EBBAC34E341C535_m715A2F9AB6249CE2205B84D8BBABD111EB27AFA6;
extern const uint32_t g_rgctx_FieldType_tBD81DD51AB3076BC0134BD255EBBAC34E341C535;
extern const uint32_t g_rgctx_FieldType_tBD81DD51AB3076BC0134BD255EBBAC34E341C535;
extern const uint32_t g_rgctx_AndroidJavaObject_FromJavaArrayDeleteLocalRef_TisFieldType_tBD81DD51AB3076BC0134BD255EBBAC34E341C535_mE7A16B6F1C9E330EE15FEE6608807ED74D02E823;
extern const uint32_t g_rgctx_AndroidJNIHelper_GetFieldID_TisFieldType_t2F905C7C598CE04C35E1297452F5AE8BFD208DAE_mFB00C8B4BED9EBCFA629A59166C7EFC55D5D4085;
extern const uint32_t g_rgctx_FieldType_t2F905C7C598CE04C35E1297452F5AE8BFD208DAE;
extern const uint32_t g_rgctx_FieldType_t2F905C7C598CE04C35E1297452F5AE8BFD208DAE;
extern const uint32_t g_rgctx_AndroidJNIHelper_GetMethodID_TisReturnType_t2DAB0875DF34A21B532F695F9B7329A0B5BBCB27_m872C06BDFAA7CC5369EABC3850D99E950C4E9323;
extern const uint32_t g_rgctx_ReturnType_t2DAB0875DF34A21B532F695F9B7329A0B5BBCB27;
extern const uint32_t g_rgctx_ReturnType_t2DAB0875DF34A21B532F695F9B7329A0B5BBCB27;
extern const uint32_t g_rgctx_AndroidJavaObject_FromJavaArrayDeleteLocalRef_TisReturnType_t2DAB0875DF34A21B532F695F9B7329A0B5BBCB27_m4D0458550132E2BEB3886928F02628AB8F67175E;
extern const uint32_t g_rgctx_AndroidJNIHelper_GetFieldID_TisFieldType_t31FF3BC94504224433BABB9DAD6CE4032EA80634_m323B9856DC209DF10953B764C89892283C6AE581;
extern const uint32_t g_rgctx_FieldType_t31FF3BC94504224433BABB9DAD6CE4032EA80634;
extern const uint32_t g_rgctx_FieldType_t31FF3BC94504224433BABB9DAD6CE4032EA80634;
extern const uint32_t g_rgctx_AndroidJavaObject_FromJavaArrayDeleteLocalRef_TisFieldType_t31FF3BC94504224433BABB9DAD6CE4032EA80634_m60011E2A0F4B6E687443E3F085869DC455438124;
extern const uint32_t g_rgctx_AndroidJNIHelper_GetFieldID_TisFieldType_t0374C27CAE86F7DA1E32463BA4A51CE6B68D02E1_mB578307426A086429411417C9DC1C11863EE3FEC;
extern const uint32_t g_rgctx_FieldType_t0374C27CAE86F7DA1E32463BA4A51CE6B68D02E1;
extern const uint32_t g_rgctx_FieldType_t0374C27CAE86F7DA1E32463BA4A51CE6B68D02E1;
extern const uint32_t g_rgctx_AndroidJNIHelper_ConvertFromJNIArray_TisReturnType_t7F4379E8D6E3B3545F3D77660B8E3F6DA1DC4DB9_mEE171E15AE5E0AD6E114212C9E6F2EF42DEB024C;
extern const uint32_t g_rgctx_ReturnType_t7F4379E8D6E3B3545F3D77660B8E3F6DA1DC4DB9;
extern const uint32_t g_rgctx_ArrayType_t15B09198CBBCA055D6DA2DC75D66B572B87D0FBF;
extern const uint32_t g_rgctx_ArrayType_t15B09198CBBCA055D6DA2DC75D66B572B87D0FBF;
extern const uint32_t g_rgctx__AndroidJNIHelper_GetSignature_TisReturnType_tA157ECD42EBFB71C50CF7B157BCCB1CDBF4A0B6A_m1DE78CA1017A9BF3BEE85712A53DFEBE683DC87F;
extern const uint32_t g_rgctx_ReturnType_tA4A9F806902AD20F830C4E904C1531989B535F95;
extern const uint32_t g_rgctx_ReturnType_t26D5E94BFCB8FB3CF5FF8C82163A3BE8EACEA9A4;
extern const uint32_t g_rgctx__AndroidJNIHelper_ConvertFromJNIArray_TisArrayType_t33FA9A7F66F041B4E2878FF619DA2A8FCDD39085_m2DE197CE4F8061C7658B6308D28812D6AE662FE5;
extern const uint32_t g_rgctx__AndroidJNIHelper_GetMethodID_TisReturnType_tD2A6EF0F4156D493768CADDC35C177D5FF55C4DE_m7AC36AC0446E8D34546FE4D002F9C8B56D25A8B6;
extern const uint32_t g_rgctx__AndroidJNIHelper_GetFieldID_TisFieldType_tC112CF857F2F9A16A01117BD8DE4B420B806AB6D_mFA7B428C4344302ADCD1670CEB890B397DAD9227;
extern const uint32_t g_rgctx__AndroidJNIHelper_GetSignature_TisReturnType_t9A95B9FC093A340C4D5AEABCB66770F89AE0048D_mE373542209BEBEDCBECCAB5C5B944D0778370DEC;
static const Il2CppRGCTXDefinition s_rgctxValues[41] = 
{
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__Get_TisFieldType_tE541E61DC1EE486A3DDC10DCA2A2DD9A2A3BADE8_m06134DE4CC6EA8E3189542B792E94123E91E03C6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__Set_TisFieldType_t5C6F84F3CFFB0874A4DA0D1C58053A25B835551E_m462146FCC7D2B3F83FF6E8999553D33E4959F5BE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__GetStatic_TisFieldType_tBB418E296327456981AAF34C2BEB510AEC3C4E4D_m287CA0417FCCBDC7C54BA521F00661EE781AE83E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__SetStatic_TisFieldType_t6076CC06F19BCC0301AE8C734F26CA429D2DB469_m63581F10344537651B9090C50B7BAFA8B9CDB1A3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__Call_TisReturnType_t6A981EFC55AACA8DB2914A6B9C24AF2C1F0D86E3_mBA92180AE68E07DA29AB2ED05D52AA22E1ECEC52 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__Call_TisReturnType_t7C9CEFF53F7F785E3B0A2AA52BF0599DB9E4C7A7_m53C8581E9FA8AB194F9919FD0B5F910D4D0EDEDF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__CallStatic_TisReturnType_t41223B870DEBD9DC66C7F3F6FDDF2CF6D061E4EB_mA272E4A1258A5DB68113DF8B90DF5B5B3A724832 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject__CallStatic_TisReturnType_t94E13999E45FF70AA5DA5E427955FC4E439412B2_m7F6D9361C675DD63CF53A9BE7588D79625F85B4D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJNIHelper_GetMethodID_TisReturnType_t0FD1385ACD92B5652F803E183304929EDB7632D9_mAB39D5C2ACF4BF7098D1D76F31302A29A45D3825 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_ReturnType_t0FD1385ACD92B5652F803E183304929EDB7632D9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReturnType_t0FD1385ACD92B5652F803E183304929EDB7632D9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject_FromJavaArrayDeleteLocalRef_TisReturnType_t0FD1385ACD92B5652F803E183304929EDB7632D9_m6244F277416780B7BE2BF89D0D01E635E59EF5DB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJNIHelper_GetFieldID_TisFieldType_tBD81DD51AB3076BC0134BD255EBBAC34E341C535_m715A2F9AB6249CE2205B84D8BBABD111EB27AFA6 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_FieldType_tBD81DD51AB3076BC0134BD255EBBAC34E341C535 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_tBD81DD51AB3076BC0134BD255EBBAC34E341C535 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject_FromJavaArrayDeleteLocalRef_TisFieldType_tBD81DD51AB3076BC0134BD255EBBAC34E341C535_mE7A16B6F1C9E330EE15FEE6608807ED74D02E823 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJNIHelper_GetFieldID_TisFieldType_t2F905C7C598CE04C35E1297452F5AE8BFD208DAE_mFB00C8B4BED9EBCFA629A59166C7EFC55D5D4085 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_FieldType_t2F905C7C598CE04C35E1297452F5AE8BFD208DAE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_t2F905C7C598CE04C35E1297452F5AE8BFD208DAE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJNIHelper_GetMethodID_TisReturnType_t2DAB0875DF34A21B532F695F9B7329A0B5BBCB27_m872C06BDFAA7CC5369EABC3850D99E950C4E9323 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_ReturnType_t2DAB0875DF34A21B532F695F9B7329A0B5BBCB27 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReturnType_t2DAB0875DF34A21B532F695F9B7329A0B5BBCB27 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject_FromJavaArrayDeleteLocalRef_TisReturnType_t2DAB0875DF34A21B532F695F9B7329A0B5BBCB27_m4D0458550132E2BEB3886928F02628AB8F67175E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJNIHelper_GetFieldID_TisFieldType_t31FF3BC94504224433BABB9DAD6CE4032EA80634_m323B9856DC209DF10953B764C89892283C6AE581 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_FieldType_t31FF3BC94504224433BABB9DAD6CE4032EA80634 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_t31FF3BC94504224433BABB9DAD6CE4032EA80634 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJavaObject_FromJavaArrayDeleteLocalRef_TisFieldType_t31FF3BC94504224433BABB9DAD6CE4032EA80634_m60011E2A0F4B6E687443E3F085869DC455438124 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJNIHelper_GetFieldID_TisFieldType_t0374C27CAE86F7DA1E32463BA4A51CE6B68D02E1_mB578307426A086429411417C9DC1C11863EE3FEC },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_FieldType_t0374C27CAE86F7DA1E32463BA4A51CE6B68D02E1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FieldType_t0374C27CAE86F7DA1E32463BA4A51CE6B68D02E1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AndroidJNIHelper_ConvertFromJNIArray_TisReturnType_t7F4379E8D6E3B3545F3D77660B8E3F6DA1DC4DB9_mEE171E15AE5E0AD6E114212C9E6F2EF42DEB024C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReturnType_t7F4379E8D6E3B3545F3D77660B8E3F6DA1DC4DB9 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_ArrayType_t15B09198CBBCA055D6DA2DC75D66B572B87D0FBF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArrayType_t15B09198CBBCA055D6DA2DC75D66B572B87D0FBF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__AndroidJNIHelper_GetSignature_TisReturnType_tA157ECD42EBFB71C50CF7B157BCCB1CDBF4A0B6A_m1DE78CA1017A9BF3BEE85712A53DFEBE683DC87F },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_ReturnType_tA4A9F806902AD20F830C4E904C1531989B535F95 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_ReturnType_t26D5E94BFCB8FB3CF5FF8C82163A3BE8EACEA9A4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__AndroidJNIHelper_ConvertFromJNIArray_TisArrayType_t33FA9A7F66F041B4E2878FF619DA2A8FCDD39085_m2DE197CE4F8061C7658B6308D28812D6AE662FE5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__AndroidJNIHelper_GetMethodID_TisReturnType_tD2A6EF0F4156D493768CADDC35C177D5FF55C4DE_m7AC36AC0446E8D34546FE4D002F9C8B56D25A8B6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__AndroidJNIHelper_GetFieldID_TisFieldType_tC112CF857F2F9A16A01117BD8DE4B420B806AB6D_mFA7B428C4344302ADCD1670CEB890B397DAD9227 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx__AndroidJNIHelper_GetSignature_TisReturnType_t9A95B9FC093A340C4D5AEABCB66770F89AE0048D_mE373542209BEBEDCBECCAB5C5B944D0778370DEC },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_AndroidJNIModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_AndroidJNIModule_CodeGenModule = 
{
	"UnityEngine.AndroidJNIModule.dll",
	409,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	23,
	s_rgctxIndices,
	41,
	s_rgctxValues,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
