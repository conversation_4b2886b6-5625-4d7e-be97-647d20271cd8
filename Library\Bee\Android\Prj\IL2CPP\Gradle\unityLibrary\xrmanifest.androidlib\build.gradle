apply plugin: 'android-library'

dependencies {
    implementation fileTree(dir: 'bin', include: ['*.jar'])
    implementation fileTree(dir: 'libs', include: ['*.jar'])
}

android {
    namespace "com.UnityTechnologies.XR.Manifest"
    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
            //java.srcDirs = ['src']
            res.srcDirs = ['res']
            assets.srcDirs = ['assets']
            jniLibs.srcDirs = ['libs']
        }
    }

    compileSdkVersion 30
    buildToolsVersion '34.0.0'
    defaultConfig {
        targetSdkVersion 22
    }

    lintOptions {
        abortOnError false
    }
}
