1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    xmlns:tools="http://schemas.android.com/tools"
4    package="com.unity3d.player" >
5
6    <uses-sdk android:minSdkVersion="29" />
6-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml
7
8    <uses-feature android:glEsVersion="0x00030000" />
8-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:48:3-52
8-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:48:17-49
9    <uses-feature
9-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:49:3-91
10        android:name="android.hardware.vulkan.version"
10-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:49:17-63
11        android:required="false" />
11-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:49:64-88
12
13    <supports-gl-texture android:name="GL_KHR_texture_compression_astc_ldr" />
13-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:50:3-77
13-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:50:24-74
14
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:51:3-65
15-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:51:20-62
16
17    <uses-feature
17-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:52:3-88
18        android:name="android.hardware.touchscreen"
18-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:52:17-60
19        android:required="false" />
19-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:52:61-85
20    <uses-feature
20-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:53:3-99
21        android:name="android.hardware.touchscreen.multitouch"
21-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:53:17-71
22        android:required="false" />
22-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:53:72-96
23    <uses-feature
23-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:54:3-108
24        android:name="android.hardware.touchscreen.multitouch.distinct"
24-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:54:17-80
25        android:required="false" />
25-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:54:81-105
26
27    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
27-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:55:3-71
27-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:55:20-68
28
29    <application
29-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:3:3-47:17
30        android:extractNativeLibs="true"
30-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:3:16-48
31        android:requestLegacyExternalStorage="true" >
31-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:3:49-92
32        <activity
32-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:4:5-11:16
33            android:name="com.unity3d.player.UnityPlayerActivity"
33-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:4:15-68
34            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
34-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:4:180-361
35            android:hardwareAccelerated="false"
35-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:4:397-432
36            android:launchMode="singleTask"
36-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:4:148-179
37            android:resizeableActivity="false"
37-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:4:362-396
38            android:screenOrientation="fullUser"
38-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:4:111-147
39            android:theme="@style/UnityThemeSelector" >
39-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:4:69-110
40            <intent-filter>
40-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:5:7-8:23
41                <action android:name="android.intent.action.MAIN" />
41-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:6:9-61
41-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:6:17-58
42
43                <category android:name="android.intent.category.LAUNCHER" />
43-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:7:9-69
43-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:7:19-66
44            </intent-filter>
45
46            <meta-data
46-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:9:7-82
47                android:name="unityplayer.UnityActivity"
47-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:9:18-58
48                android:value="true" />
48-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:9:59-79
49            <meta-data
49-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:7-78
50                android:name="android.notch_support"
50-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:18-54
51                android:value="true" />
51-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:55-75
52        </activity>
53
54        <meta-data
54-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:12:5-69
55            android:name="unity.splash-mode"
55-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:12:16-48
56            android:value="0" />
56-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:12:49-66
57        <meta-data
57-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:13:5-74
58            android:name="unity.splash-enable"
58-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:13:16-50
59            android:value="True" />
59-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:13:51-71
60        <meta-data
60-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:14:5-78
61            android:name="unity.launch-fullscreen"
61-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:14:16-54
62            android:value="True" />
62-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:14:55-75
63        <meta-data
63-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:5-81
64            android:name="notch.config"
64-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:16-43
65            android:value="portrait|landscape" />
65-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:44-78
66        <meta-data
66-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:16:5-84
67            android:name="unity.auto-report-fully-drawn"
67-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:16:16-60
68            android:value="true" />
68-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:16:61-81
69        <meta-data
69-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:17:5-65
70            android:name="pvr.app.type"
70-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:17:16-43
71            android:value="vr" />
71-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:17:44-62
72        <meta-data
72-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:18:5-75
73            android:name="pxr.sdk.version_code"
73-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:18:16-51
74            android:value="5130" />
74-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:18:52-72
75        <meta-data
75-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:5-83
76            android:name="pvr.sdk.version"
76-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:16-46
77            android:value="XR Platform_3.2.4" />
77-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:19:47-80
78        <meta-data
78-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:20:5-62
79            android:name="enable_cpt"
79-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:20:16-41
80            android:value="0" />
80-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:20:42-59
81        <meta-data
81-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:21:5-76
82            android:name="Enable_AdaptiveHandModel"
82-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:21:16-55
83            android:value="0" />
83-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:21:56-73
84        <meta-data
84-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:22:5-79
85            android:name="Hand_Tracking_HighFrequency"
85-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:22:16-58
86            android:value="0" />
86-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:22:59-76
87        <meta-data
87-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:23:5-66
88            android:name="rendering_mode"
88-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:23:16-45
89            android:value="0" />
89-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:23:46-63
90        <meta-data
90-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:24:5-64
91            android:name="display_rate"
91-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:24:16-43
92            android:value="0" />
92-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:24:44-61
93        <meta-data
93-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:25:5-63
94            android:name="color_Space"
94-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:25:16-42
95            android:value="1" />
95-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:25:43-60
96        <meta-data
96-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:26:5-62
97            android:name="MRCsupport"
97-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:26:16-41
98            android:value="1" />
98-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:26:42-59
99        <meta-data
99-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:27:5-68
100            android:name="pvr.LateLatching"
100-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:27:16-47
101            android:value="0" />
101-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:27:48-65
102        <meta-data
102-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:28:5-73
103            android:name="pvr.LateLatchingDebug"
103-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:28:16-52
104            android:value="0" />
104-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:28:53-70
105        <meta-data
105-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:29:5-66
106            android:name="pvr.app.splash"
106-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:29:16-45
107            android:value="0" />
107-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:29:46-63
108        <meta-data
108-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:30:5-70
109            android:name="PICO.swift.feature"
109-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:30:16-49
110            android:value="0" />
110-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:30:50-67
111        <meta-data
111-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:31:5-71
112            android:name="adaptive_resolution"
112-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:31:16-50
113            android:value="0" />
113-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:31:51-68
114        <meta-data
114-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:32:5-71
115            android:name="enable_mr_safeguard"
115-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:32:16-50
116            android:value="0" />
116-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:32:51-68
117        <meta-data
117-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:33:5-62
118            android:name="enable_vst"
118-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:33:16-41
119            android:value="0" />
119-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:33:42-59
120        <meta-data
120-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:34:5-65
121            android:name="enable_anchor"
121-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:34:16-44
122            android:value="0" />
122-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:34:45-62
123        <meta-data
123-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:35:5-73
124            android:name="mr_map_mgr_auto_start"
124-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:35:16-52
125            android:value="0" />
125-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:35:53-70
126        <meta-data
126-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:36:5-73
127            android:name="enable_spatial_anchor"
127-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:36:16-52
128            android:value="0" />
128-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:36:53-70
129        <meta-data
129-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:37:5-71
130            android:name="enable_cloud_anchor"
130-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:37:16-50
131            android:value="0" />
131-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:37:51-68
132        <meta-data
132-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:38:5-70
133            android:name="enable_mesh_anchor"
133-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:38:16-49
134            android:value="0" />
134-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:38:50-67
135        <meta-data
135-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:39:5-71
136            android:name="enable_scene_anchor"
136-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:39:16-50
137            android:value="0" />
137-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:39:51-68
138        <meta-data
138-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:40:5-71
139            android:name="pvr.SuperResolution"
139-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:40:16-50
140            android:value="0" />
140-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:40:51-68
141        <meta-data
141-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:41:5-72
142            android:name="pvr.NormalSharpening"
142-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:41:16-51
143            android:value="0" />
143-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:41:52-69
144        <meta-data
144-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:42:5-73
145            android:name="pvr.QualitySharpening"
145-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:42:16-52
146            android:value="0" />
146-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:42:53-70
147        <meta-data
147-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:43:5-79
148            android:name="pvr.FixedFoveatedSharpening"
148-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:43:16-58
149            android:value="0" />
149-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:43:59-76
150        <meta-data
150-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:44:5-78
151            android:name="pvr.SelfAdaptiveSharpening"
151-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:44:16-57
152            android:value="0" />
152-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:44:58-75
153        <meta-data
153-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:45:5-69
154            android:name="pvr.app.secure_mr"
154-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:45:16-48
155            android:value="0" />
155-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:45:49-66
156        <meta-data
156-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:46:5-62
157            android:name="controller"
157-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:46:16-41
158            android:value="1" />
158-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:46:42-59
159    </application>
160
161</manifest>
