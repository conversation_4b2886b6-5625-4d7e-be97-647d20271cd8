﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 Unity.XR.CoreUtils.XROrigin Unity.XR.CoreUtils.ARTrackablesParentTransformChangedEventArgs::get_Origin()
extern void ARTrackablesParentTransformChangedEventArgs_get_Origin_m91D7C3638FBF94D468AD4467ABB2EC9500753F25 (void);
// 0x00000002 UnityEngine.Transform Unity.XR.CoreUtils.ARTrackablesParentTransformChangedEventArgs::get_TrackablesParent()
extern void ARTrackablesParentTransformChangedEventArgs_get_TrackablesParent_m89F1B7B428A07F5142AEC2BF32A83B35B52C0425 (void);
// 0x00000003 System.Void Unity.XR.CoreUtils.ARTrackablesParentTransformChangedEventArgs::.ctor(Unity.XR.CoreUtils.XROrigin,UnityEngine.Transform)
extern void ARTrackablesParentTransformChangedEventArgs__ctor_m46B5D18DF81A7296E36E37917E69AB9E748B6278 (void);
// 0x00000004 System.Boolean Unity.XR.CoreUtils.ARTrackablesParentTransformChangedEventArgs::Equals(Unity.XR.CoreUtils.ARTrackablesParentTransformChangedEventArgs)
extern void ARTrackablesParentTransformChangedEventArgs_Equals_m8CAA7BD42F09BF7349818EF3166792652FB9F4AE (void);
// 0x00000005 System.Boolean Unity.XR.CoreUtils.ARTrackablesParentTransformChangedEventArgs::Equals(System.Object)
extern void ARTrackablesParentTransformChangedEventArgs_Equals_mE6F5F659DD06166ACDDCE62B0652F014568D003B (void);
// 0x00000006 System.Int32 Unity.XR.CoreUtils.ARTrackablesParentTransformChangedEventArgs::GetHashCode()
extern void ARTrackablesParentTransformChangedEventArgs_GetHashCode_mA5FDE9D7D0F5F079886AA4C5DF806E082C725161 (void);
// 0x00000007 System.Boolean Unity.XR.CoreUtils.ARTrackablesParentTransformChangedEventArgs::op_Equality(Unity.XR.CoreUtils.ARTrackablesParentTransformChangedEventArgs,Unity.XR.CoreUtils.ARTrackablesParentTransformChangedEventArgs)
extern void ARTrackablesParentTransformChangedEventArgs_op_Equality_m2A48490D3BBECA67708AC521F46C417AB6279CB0 (void);
// 0x00000008 System.Boolean Unity.XR.CoreUtils.ARTrackablesParentTransformChangedEventArgs::op_Inequality(Unity.XR.CoreUtils.ARTrackablesParentTransformChangedEventArgs,Unity.XR.CoreUtils.ARTrackablesParentTransformChangedEventArgs)
extern void ARTrackablesParentTransformChangedEventArgs_op_Inequality_m51EC05AEF49A34A3740CE4EF201ACAA774D0BC1E (void);
// 0x00000009 System.Void Unity.XR.CoreUtils.ReadOnlyAttribute::.ctor()
extern void ReadOnlyAttribute__ctor_m13C3453A8526FBD0C9F45B0F0539CE1208546815 (void);
// 0x0000000A System.String Unity.XR.CoreUtils.ScriptableSettingsPathAttribute::get_Path()
extern void ScriptableSettingsPathAttribute_get_Path_mC767AB284DF1262E0EAA6AA36628A4A9035B646F (void);
// 0x0000000B System.Void Unity.XR.CoreUtils.ScriptableSettingsPathAttribute::.ctor(System.String)
extern void ScriptableSettingsPathAttribute__ctor_mC942C941C139A27C8B05E732EF21E6E2FFF9E808 (void);
// 0x0000000C UnityEngine.Bounds Unity.XR.CoreUtils.BoundsUtils::GetBounds(System.Collections.Generic.List`1<UnityEngine.GameObject>)
extern void BoundsUtils_GetBounds_m83E66684F6C2CF8B1C875017DE36F7EB23EBEC8B (void);
// 0x0000000D UnityEngine.Bounds Unity.XR.CoreUtils.BoundsUtils::GetBounds(UnityEngine.Transform[])
extern void BoundsUtils_GetBounds_m765AE397EEBFBC2BA3E445BFC0634851617D9DB3 (void);
// 0x0000000E UnityEngine.Bounds Unity.XR.CoreUtils.BoundsUtils::GetBounds(UnityEngine.Transform)
extern void BoundsUtils_GetBounds_m7363605B59CA26E843B5231C3B044870A5E9E02B (void);
// 0x0000000F UnityEngine.Bounds Unity.XR.CoreUtils.BoundsUtils::GetBounds(System.Collections.Generic.List`1<UnityEngine.Renderer>)
extern void BoundsUtils_GetBounds_mFC27EEFA26B5CB8CA64E0BF51DAA9E3FDB7C129E (void);
// 0x00000010 UnityEngine.Bounds Unity.XR.CoreUtils.BoundsUtils::GetBounds(System.Collections.Generic.List`1<T>)
// 0x00000011 UnityEngine.Bounds Unity.XR.CoreUtils.BoundsUtils::GetBounds(System.Collections.Generic.List`1<UnityEngine.Vector3>)
extern void BoundsUtils_GetBounds_m4AF14DACF5FB67E990AC519BF2995811B514C5B4 (void);
// 0x00000012 System.Void Unity.XR.CoreUtils.BoundsUtils::.cctor()
extern void BoundsUtils__cctor_m704AB691443430C18DD4B017724F90FF57CE53E9 (void);
// 0x00000013 THostType[] Unity.XR.CoreUtils.IComponentHost`1::get_HostedComponents()
// 0x00000014 System.Void Unity.XR.CoreUtils.CachedComponentFilter`2::.ctor(TRootType,Unity.XR.CoreUtils.CachedSearchType,System.Boolean)
// 0x00000015 System.Void Unity.XR.CoreUtils.CachedComponentFilter`2::.ctor(TFilterType[],System.Boolean)
// 0x00000016 System.Void Unity.XR.CoreUtils.CachedComponentFilter`2::StoreMatchingComponents(System.Collections.Generic.List`1<TChildType>)
// 0x00000017 TChildType[] Unity.XR.CoreUtils.CachedComponentFilter`2::GetMatchingComponents()
// 0x00000018 System.Void Unity.XR.CoreUtils.CachedComponentFilter`2::FilteredCopyToMaster(System.Boolean)
// 0x00000019 System.Void Unity.XR.CoreUtils.CachedComponentFilter`2::FilteredCopyToMaster(System.Boolean,TRootType)
// 0x0000001A System.Void Unity.XR.CoreUtils.CachedComponentFilter`2::Dispose(System.Boolean)
// 0x0000001B System.Void Unity.XR.CoreUtils.CachedComponentFilter`2::Dispose()
// 0x0000001C System.Void Unity.XR.CoreUtils.CachedComponentFilter`2::.cctor()
// 0x0000001D TCollection Unity.XR.CoreUtils.CollectionPool`2::GetCollection()
// 0x0000001E System.Void Unity.XR.CoreUtils.CollectionPool`2::RecycleCollection(TCollection)
// 0x0000001F System.Void Unity.XR.CoreUtils.CollectionPool`2::.cctor()
// 0x00000020 T Unity.XR.CoreUtils.ComponentUtils`1::GetComponent(UnityEngine.GameObject)
// 0x00000021 T Unity.XR.CoreUtils.ComponentUtils`1::GetComponentInChildren(UnityEngine.GameObject)
// 0x00000022 System.Void Unity.XR.CoreUtils.ComponentUtils`1::.cctor()
// 0x00000023 T Unity.XR.CoreUtils.ComponentUtils::GetOrAddIf(UnityEngine.GameObject,System.Boolean)
// 0x00000024 System.Void Unity.XR.CoreUtils.EnumValues`1::.cctor()
// 0x00000025 System.Boolean Unity.XR.CoreUtils.BoundsExtensions::ContainsCompletely(UnityEngine.Bounds,UnityEngine.Bounds)
extern void BoundsExtensions_ContainsCompletely_m6036B79EAF4770CA66300C25D219A25B1431D844 (void);
// 0x00000026 System.Single Unity.XR.CoreUtils.CameraExtensions::GetVerticalFieldOfView(UnityEngine.Camera,System.Single)
extern void CameraExtensions_GetVerticalFieldOfView_m48B2530F8EDA4FC108667A6A61BA2C8863EE1AEF (void);
// 0x00000027 System.Single Unity.XR.CoreUtils.CameraExtensions::GetHorizontalFieldOfView(UnityEngine.Camera)
extern void CameraExtensions_GetHorizontalFieldOfView_mD8711BC8586101DA2B389F1E2B8C5E57BEF25661 (void);
// 0x00000028 System.Single Unity.XR.CoreUtils.CameraExtensions::GetVerticalOrthographicSize(UnityEngine.Camera,System.Single)
extern void CameraExtensions_GetVerticalOrthographicSize_mB075FA38558BD1C50275EB4757930AE7B2476E9F (void);
// 0x00000029 System.String Unity.XR.CoreUtils.CollectionExtensions::Stringify(System.Collections.Generic.ICollection`1<T>)
// 0x0000002A System.Void Unity.XR.CoreUtils.CollectionExtensions::.cctor()
extern void CollectionExtensions__cctor_m80F18EE99831C527F9AA15118BBF3AE7CDB9C129 (void);
// 0x0000002B System.Collections.Generic.KeyValuePair`2<TKey,TValue> Unity.XR.CoreUtils.DictionaryExtensions::First(System.Collections.Generic.Dictionary`2<TKey,TValue>)
// 0x0000002C System.Void Unity.XR.CoreUtils.GameObjectExtensions::SetHideFlagsRecursively(UnityEngine.GameObject,UnityEngine.HideFlags)
extern void GameObjectExtensions_SetHideFlagsRecursively_m1E8AEDE5259DFEB22AFA907FF6B2021E483D65B6 (void);
// 0x0000002D System.Void Unity.XR.CoreUtils.GameObjectExtensions::AddToHideFlagsRecursively(UnityEngine.GameObject,UnityEngine.HideFlags)
extern void GameObjectExtensions_AddToHideFlagsRecursively_m8D50F2D030ADB0BE37BFA14D05AD2B1C5CEEE154 (void);
// 0x0000002E System.Void Unity.XR.CoreUtils.GameObjectExtensions::SetLayerRecursively(UnityEngine.GameObject,System.Int32)
extern void GameObjectExtensions_SetLayerRecursively_mFDC15B72729883B0FA4D7426A0AD071E2D108392 (void);
// 0x0000002F System.Void Unity.XR.CoreUtils.GameObjectExtensions::SetLayerAndAddToHideFlagsRecursively(UnityEngine.GameObject,System.Int32,UnityEngine.HideFlags)
extern void GameObjectExtensions_SetLayerAndAddToHideFlagsRecursively_m45178194447527B4394A7C204AB136A27A731F32 (void);
// 0x00000030 System.Void Unity.XR.CoreUtils.GameObjectExtensions::SetLayerAndHideFlagsRecursively(UnityEngine.GameObject,System.Int32,UnityEngine.HideFlags)
extern void GameObjectExtensions_SetLayerAndHideFlagsRecursively_m54894DA97376A955980317A8AA646C67E236C2F6 (void);
// 0x00000031 System.Void Unity.XR.CoreUtils.GameObjectExtensions::SetRunInEditModeRecursively(UnityEngine.GameObject,System.Boolean)
extern void GameObjectExtensions_SetRunInEditModeRecursively_m4326968A10A571DC6C35D8EEBEAE2AB7C4A27017 (void);
// 0x00000032 System.Void Unity.XR.CoreUtils.GuidExtensions::Decompose(System.Guid,System.UInt64&,System.UInt64&)
extern void GuidExtensions_Decompose_mF664A6350BD60B57421D4716BC1504967394EDA9 (void);
// 0x00000033 System.Void Unity.XR.CoreUtils.HashSetExtensions::ExceptWithNonAlloc(System.Collections.Generic.HashSet`1<T>,System.Collections.Generic.HashSet`1<T>)
// 0x00000034 T Unity.XR.CoreUtils.HashSetExtensions::First(System.Collections.Generic.HashSet`1<T>)
// 0x00000035 System.Int32 Unity.XR.CoreUtils.LayerMaskExtensions::GetFirstLayerIndex(UnityEngine.LayerMask)
extern void LayerMaskExtensions_GetFirstLayerIndex_m7CFD2E8174487008C2C07ABC5D0F004F68B9EEC5 (void);
// 0x00000036 System.Boolean Unity.XR.CoreUtils.LayerMaskExtensions::Contains(UnityEngine.LayerMask,System.Int32)
extern void LayerMaskExtensions_Contains_mA386A87BF8EA22166EAC8E562FD3E2799A8F3F56 (void);
// 0x00000037 System.Collections.Generic.List`1<T> Unity.XR.CoreUtils.ListExtensions::Fill(System.Collections.Generic.List`1<T>,System.Int32)
// 0x00000038 System.Void Unity.XR.CoreUtils.ListExtensions::EnsureCapacity(System.Collections.Generic.List`1<T>,System.Int32)
// 0x00000039 System.Void Unity.XR.CoreUtils.ListExtensions::SwapAtIndices(System.Collections.Generic.List`1<T>,System.Int32,System.Int32)
// 0x0000003A UnityEngine.Pose Unity.XR.CoreUtils.PoseExtensions::ApplyOffsetTo(UnityEngine.Pose,UnityEngine.Pose)
extern void PoseExtensions_ApplyOffsetTo_mBDD88056E322E4704BF2213D1482B4C013BFBF52 (void);
// 0x0000003B UnityEngine.Vector3 Unity.XR.CoreUtils.PoseExtensions::ApplyOffsetTo(UnityEngine.Pose,UnityEngine.Vector3)
extern void PoseExtensions_ApplyOffsetTo_mC4011BC28A2E9D021B0A245A11937A9FBE79AFFE (void);
// 0x0000003C UnityEngine.Vector3 Unity.XR.CoreUtils.PoseExtensions::ApplyInverseOffsetTo(UnityEngine.Pose,UnityEngine.Vector3)
extern void PoseExtensions_ApplyInverseOffsetTo_m3A6011AD4531543C0F9006A5875047C86DC5CF0C (void);
// 0x0000003D UnityEngine.Quaternion Unity.XR.CoreUtils.QuaternionExtensions::ConstrainYaw(UnityEngine.Quaternion)
extern void QuaternionExtensions_ConstrainYaw_mBAF7F7ECB5D899A052EB0C809624A1636E19FF5C (void);
// 0x0000003E UnityEngine.Quaternion Unity.XR.CoreUtils.QuaternionExtensions::ConstrainYawNormalized(UnityEngine.Quaternion)
extern void QuaternionExtensions_ConstrainYawNormalized_m1AE588E327F3E0423429F523BFC6D5D018492D07 (void);
// 0x0000003F UnityEngine.Quaternion Unity.XR.CoreUtils.QuaternionExtensions::ConstrainYawPitchNormalized(UnityEngine.Quaternion)
extern void QuaternionExtensions_ConstrainYawPitchNormalized_m37A79143BA38F44495784288CC715EFC5D3A2BFD (void);
// 0x00000040 System.Void Unity.XR.CoreUtils.StopwatchExtensions::Restart(System.Diagnostics.Stopwatch)
extern void StopwatchExtensions_Restart_m69A76149AAC314804F8FCC6BAD06FDE88C98F297 (void);
// 0x00000041 System.String Unity.XR.CoreUtils.StringExtensions::FirstToUpper(System.String)
extern void StringExtensions_FirstToUpper_m8FCFF701B617938AE8ABD2F547B16DD95F1B1A6C (void);
// 0x00000042 System.String Unity.XR.CoreUtils.StringExtensions::InsertSpacesBetweenWords(System.String)
extern void StringExtensions_InsertSpacesBetweenWords_m63FCD5B45B62A86B24B361ACFA57FAB3F5CE3510 (void);
// 0x00000043 System.Void Unity.XR.CoreUtils.StringExtensions::.cctor()
extern void StringExtensions__cctor_m2F657CD42D0CFAEA7FD40F67293E61FCC108BDFF (void);
// 0x00000044 UnityEngine.Pose Unity.XR.CoreUtils.TransformExtensions::GetLocalPose(UnityEngine.Transform)
extern void TransformExtensions_GetLocalPose_m1DE50F03B6211C35AD0445A50626F97771B4321D (void);
// 0x00000045 UnityEngine.Pose Unity.XR.CoreUtils.TransformExtensions::GetWorldPose(UnityEngine.Transform)
extern void TransformExtensions_GetWorldPose_m08E343202174666DFF9A07560AB1A72ACD0DDCF1 (void);
// 0x00000046 System.Void Unity.XR.CoreUtils.TransformExtensions::SetLocalPose(UnityEngine.Transform,UnityEngine.Pose)
extern void TransformExtensions_SetLocalPose_m15B9FAC87B7B05C750A6D8FD77A0768818CFB7D4 (void);
// 0x00000047 System.Void Unity.XR.CoreUtils.TransformExtensions::SetWorldPose(UnityEngine.Transform,UnityEngine.Pose)
extern void TransformExtensions_SetWorldPose_mD1B6C71C60FB650943C8CC11546F26F3F28E4FDE (void);
// 0x00000048 UnityEngine.Pose Unity.XR.CoreUtils.TransformExtensions::TransformPose(UnityEngine.Transform,UnityEngine.Pose)
extern void TransformExtensions_TransformPose_m4047A2A74919D9C435751B65FF41503A32611B51 (void);
// 0x00000049 UnityEngine.Pose Unity.XR.CoreUtils.TransformExtensions::InverseTransformPose(UnityEngine.Transform,UnityEngine.Pose)
extern void TransformExtensions_InverseTransformPose_mA2C52EFE2C80EB0DE7F09EA477B80C583B4387FB (void);
// 0x0000004A UnityEngine.Ray Unity.XR.CoreUtils.TransformExtensions::InverseTransformRay(UnityEngine.Transform,UnityEngine.Ray)
extern void TransformExtensions_InverseTransformRay_m27353F07FE4E9F634C8262BB69B0B4487B6CC1A5 (void);
// 0x0000004B System.Void Unity.XR.CoreUtils.TypeExtensions::GetAssignableTypes(System.Type,System.Collections.Generic.List`1<System.Type>,System.Func`2<System.Type,System.Boolean>)
extern void TypeExtensions_GetAssignableTypes_m7EB47C8D3E37D11462B285D5D16BA664A9B24142 (void);
// 0x0000004C System.Void Unity.XR.CoreUtils.TypeExtensions::GetImplementationsOfInterface(System.Type,System.Collections.Generic.List`1<System.Type>)
extern void TypeExtensions_GetImplementationsOfInterface_m3773A5150ADAE97907C536F6C5BAF3E18234B133 (void);
// 0x0000004D System.Void Unity.XR.CoreUtils.TypeExtensions::GetExtensionsOfClass(System.Type,System.Collections.Generic.List`1<System.Type>)
extern void TypeExtensions_GetExtensionsOfClass_mF48058E7D4726AA6D22B0A4D91F7F6EC771D8CA8 (void);
// 0x0000004E System.Void Unity.XR.CoreUtils.TypeExtensions::GetGenericInterfaces(System.Type,System.Type,System.Collections.Generic.List`1<System.Type>)
extern void TypeExtensions_GetGenericInterfaces_m7C31E705E43FC289F8F3A7FCE3103B489FE9FEB5 (void);
// 0x0000004F System.Reflection.PropertyInfo Unity.XR.CoreUtils.TypeExtensions::GetPropertyRecursively(System.Type,System.String,System.Reflection.BindingFlags)
extern void TypeExtensions_GetPropertyRecursively_m9D122527E76B2936FD96BDAB1C5ED53AB59BEABE (void);
// 0x00000050 System.Reflection.FieldInfo Unity.XR.CoreUtils.TypeExtensions::GetFieldRecursively(System.Type,System.String,System.Reflection.BindingFlags)
extern void TypeExtensions_GetFieldRecursively_m60F8D95A17428B4431D403DBC2B0FBD6CD6C0CB5 (void);
// 0x00000051 System.Void Unity.XR.CoreUtils.TypeExtensions::GetFieldsRecursively(System.Type,System.Collections.Generic.List`1<System.Reflection.FieldInfo>,System.Reflection.BindingFlags)
extern void TypeExtensions_GetFieldsRecursively_mEF7995AC262D6DA08C78454843299EF22EAF834F (void);
// 0x00000052 System.Void Unity.XR.CoreUtils.TypeExtensions::GetPropertiesRecursively(System.Type,System.Collections.Generic.List`1<System.Reflection.PropertyInfo>,System.Reflection.BindingFlags)
extern void TypeExtensions_GetPropertiesRecursively_m72D4D92D9C07C7233B4230EC3586D83B2AE081C1 (void);
// 0x00000053 System.Void Unity.XR.CoreUtils.TypeExtensions::GetInterfaceFieldsFromClasses(System.Collections.Generic.IEnumerable`1<System.Type>,System.Collections.Generic.List`1<System.Reflection.FieldInfo>,System.Collections.Generic.List`1<System.Type>,System.Reflection.BindingFlags)
extern void TypeExtensions_GetInterfaceFieldsFromClasses_m1D25F8EE11B324523F825B102EAB956105BCA74E (void);
// 0x00000054 TAttribute Unity.XR.CoreUtils.TypeExtensions::GetAttribute(System.Type,System.Boolean)
// 0x00000055 System.Void Unity.XR.CoreUtils.TypeExtensions::IsDefinedGetInheritedTypes(System.Type,System.Collections.Generic.List`1<System.Type>)
// 0x00000056 System.Reflection.FieldInfo Unity.XR.CoreUtils.TypeExtensions::GetFieldInTypeOrBaseType(System.Type,System.String)
extern void TypeExtensions_GetFieldInTypeOrBaseType_m1EE28449734BB83052CDC44BB1A2BEB0E47B9C2D (void);
// 0x00000057 System.String Unity.XR.CoreUtils.TypeExtensions::GetNameWithGenericArguments(System.Type)
extern void TypeExtensions_GetNameWithGenericArguments_m29963E46E64626C3E7FF55798199529E6FE4DD24 (void);
// 0x00000058 System.String Unity.XR.CoreUtils.TypeExtensions::GetNameWithFullGenericArguments(System.Type)
extern void TypeExtensions_GetNameWithFullGenericArguments_m3360311034AD35828677C1D85B20F55FF5B86816 (void);
// 0x00000059 System.String Unity.XR.CoreUtils.TypeExtensions::GetFullNameWithGenericArguments(System.Type)
extern void TypeExtensions_GetFullNameWithGenericArguments_mEB78A53194DA3D2E56D13A1C96D244698EB3CBCF (void);
// 0x0000005A System.String Unity.XR.CoreUtils.TypeExtensions::GetFullNameWithGenericArgumentsInternal(System.Type)
extern void TypeExtensions_GetFullNameWithGenericArgumentsInternal_mAFB2E37F914668A0596A238E20C75B13A09C043C (void);
// 0x0000005B System.Boolean Unity.XR.CoreUtils.TypeExtensions::IsAssignableFromOrSubclassOf(System.Type,System.Type)
extern void TypeExtensions_IsAssignableFromOrSubclassOf_mCEF9E345C6B02D5A504EDD1F02A3F04B7538244D (void);
// 0x0000005C System.Reflection.MethodInfo Unity.XR.CoreUtils.TypeExtensions::GetMethodRecursively(System.Type,System.String,System.Reflection.BindingFlags)
extern void TypeExtensions_GetMethodRecursively_mD7AC39EC929889F407E9FAF7F548CF965611D542 (void);
// 0x0000005D System.Void Unity.XR.CoreUtils.TypeExtensions::.cctor()
extern void TypeExtensions__cctor_mD85D49F1687F527C722D5A5261E2B65E2D6D6778 (void);
// 0x0000005E System.Void Unity.XR.CoreUtils.TypeExtensions/<>c__DisplayClass2_0::.ctor()
extern void U3CU3Ec__DisplayClass2_0__ctor_m503826A926363EA4EAF9A0DE52DA34382B75C925 (void);
// 0x0000005F System.Void Unity.XR.CoreUtils.TypeExtensions/<>c__DisplayClass2_0::<GetAssignableTypes>b__0(System.Type)
extern void U3CU3Ec__DisplayClass2_0_U3CGetAssignableTypesU3Eb__0_m605F61DF1647ACBAF3D37F9AB38C2478C6BC5E57 (void);
// 0x00000060 System.Void Unity.XR.CoreUtils.BoolUnityEvent::.ctor()
extern void BoolUnityEvent__ctor_mED39BBF2794EA6795D53400E39F0A0CD03BEA40C (void);
// 0x00000061 System.Void Unity.XR.CoreUtils.FloatUnityEvent::.ctor()
extern void FloatUnityEvent__ctor_m5F6D35C2878B13408E44E08A0A10D5D73B4EAEF0 (void);
// 0x00000062 System.Void Unity.XR.CoreUtils.Vector2UnityEvent::.ctor()
extern void Vector2UnityEvent__ctor_mB0AD202040845E7001AA7BC7B27B49E89B500B98 (void);
// 0x00000063 System.Void Unity.XR.CoreUtils.Vector3UnityEvent::.ctor()
extern void Vector3UnityEvent__ctor_m47975F110B4AD23854F23DC5B25708D4467A766E (void);
// 0x00000064 System.Void Unity.XR.CoreUtils.Vector4UnityEvent::.ctor()
extern void Vector4UnityEvent__ctor_m91DD83DD31DE80A95BFAB40818E70C163B42DD90 (void);
// 0x00000065 System.Void Unity.XR.CoreUtils.QuaternionUnityEvent::.ctor()
extern void QuaternionUnityEvent__ctor_m663323B95E33F708F6A67100A8A730C412EE5339 (void);
// 0x00000066 System.Void Unity.XR.CoreUtils.IntUnityEvent::.ctor()
extern void IntUnityEvent__ctor_m08120E25F2173E6C0BA993F891C9CFC37C4EC710 (void);
// 0x00000067 System.Void Unity.XR.CoreUtils.ColorUnityEvent::.ctor()
extern void ColorUnityEvent__ctor_mB2C2672F44208AF4D11C7F1A41DA51BDFA00DAFC (void);
// 0x00000068 System.Void Unity.XR.CoreUtils.StringUnityEvent::.ctor()
extern void StringUnityEvent__ctor_mF1040CBD3090042BA039B8AA8CC0571BEADCB4FD (void);
// 0x00000069 UnityEngine.Vector2 Unity.XR.CoreUtils.Vector2Extensions::Inverse(UnityEngine.Vector2)
extern void Vector2Extensions_Inverse_mA671F7C6408757B5F528061E1B64416359658255 (void);
// 0x0000006A System.Single Unity.XR.CoreUtils.Vector2Extensions::MinComponent(UnityEngine.Vector2)
extern void Vector2Extensions_MinComponent_mB5D153E34E2941D9799187978E278D05244ECCFF (void);
// 0x0000006B System.Single Unity.XR.CoreUtils.Vector2Extensions::MaxComponent(UnityEngine.Vector2)
extern void Vector2Extensions_MaxComponent_mD9485A0D7FF02A1B2CD75CBCC3FCB9143A092BC0 (void);
// 0x0000006C UnityEngine.Vector2 Unity.XR.CoreUtils.Vector2Extensions::Abs(UnityEngine.Vector2)
extern void Vector2Extensions_Abs_m76A90FEAF17AC5D3E9E827175D8DEBE96E8BAC02 (void);
// 0x0000006D UnityEngine.Vector3 Unity.XR.CoreUtils.Vector3Extensions::Inverse(UnityEngine.Vector3)
extern void Vector3Extensions_Inverse_m7520A342C85FE25830F9FCD01764121F9F430082 (void);
// 0x0000006E System.Single Unity.XR.CoreUtils.Vector3Extensions::MinComponent(UnityEngine.Vector3)
extern void Vector3Extensions_MinComponent_m488C8EA6E5FA51A7B5D1EAA326C588B7B836A8EC (void);
// 0x0000006F System.Single Unity.XR.CoreUtils.Vector3Extensions::MaxComponent(UnityEngine.Vector3)
extern void Vector3Extensions_MaxComponent_mFC4E0C3CA7B23E525E73D06294DABD3686988B5F (void);
// 0x00000070 UnityEngine.Vector3 Unity.XR.CoreUtils.Vector3Extensions::Abs(UnityEngine.Vector3)
extern void Vector3Extensions_Abs_mD8164FAB218F0866D3636F1F83EA8645AC325A04 (void);
// 0x00000071 UnityEngine.Vector3 Unity.XR.CoreUtils.Vector3Extensions::Multiply(UnityEngine.Vector3,UnityEngine.Vector3)
extern void Vector3Extensions_Multiply_m3B8F1F9AF0FC579579CFFE05DE88A73F6E95247F (void);
// 0x00000072 UnityEngine.Vector3 Unity.XR.CoreUtils.Vector3Extensions::Divide(UnityEngine.Vector3,UnityEngine.Vector3)
extern void Vector3Extensions_Divide_m57CFB0B4C8D9C763BC040DB58E177577DC039E0E (void);
// 0x00000073 UnityEngine.Vector3 Unity.XR.CoreUtils.Vector3Extensions::SafeDivide(UnityEngine.Vector3,UnityEngine.Vector3)
extern void Vector3Extensions_SafeDivide_mC8342D1EF6C96E254118D0BB443DF85BB6FAC4C8 (void);
// 0x00000074 System.Void Unity.XR.CoreUtils.GameObjectUtils::add_GameObjectInstantiated(System.Action`1<UnityEngine.GameObject>)
extern void GameObjectUtils_add_GameObjectInstantiated_m29D8568CF7592F3E68BF4955585A67A49234289B (void);
// 0x00000075 System.Void Unity.XR.CoreUtils.GameObjectUtils::remove_GameObjectInstantiated(System.Action`1<UnityEngine.GameObject>)
extern void GameObjectUtils_remove_GameObjectInstantiated_mCF0A714F9DC6690DB44814BCBC3732FB34A4E308 (void);
// 0x00000076 UnityEngine.GameObject Unity.XR.CoreUtils.GameObjectUtils::Create()
extern void GameObjectUtils_Create_mB0A569525F64CA1916B933FC12D60D3A49F59D23 (void);
// 0x00000077 UnityEngine.GameObject Unity.XR.CoreUtils.GameObjectUtils::Create(System.String)
extern void GameObjectUtils_Create_m3F64ABF7C1F438F840AAC72F33825EC306F372EA (void);
// 0x00000078 UnityEngine.GameObject Unity.XR.CoreUtils.GameObjectUtils::Instantiate(UnityEngine.GameObject,UnityEngine.Transform,System.Boolean)
extern void GameObjectUtils_Instantiate_mAE3F4262B0D2CD08896C8F73E01E27CBAC6FC7CF (void);
// 0x00000079 UnityEngine.GameObject Unity.XR.CoreUtils.GameObjectUtils::Instantiate(UnityEngine.GameObject,UnityEngine.Vector3,UnityEngine.Quaternion)
extern void GameObjectUtils_Instantiate_mEBCC3B8A0DCEF7CBDEDDA6FA091E04FE9D951AEC (void);
// 0x0000007A UnityEngine.GameObject Unity.XR.CoreUtils.GameObjectUtils::Instantiate(UnityEngine.GameObject,UnityEngine.Transform,UnityEngine.Vector3,UnityEngine.Quaternion)
extern void GameObjectUtils_Instantiate_mCDD665E093028B28576B433C2A2226A5BEE6BD4D (void);
// 0x0000007B UnityEngine.GameObject Unity.XR.CoreUtils.GameObjectUtils::CloneWithHideFlags(UnityEngine.GameObject,UnityEngine.Transform)
extern void GameObjectUtils_CloneWithHideFlags_m68A32C3E4717C4B24CFC90DE1B32FE9F885AF0E9 (void);
// 0x0000007C System.Void Unity.XR.CoreUtils.GameObjectUtils::CopyHideFlagsRecursively(UnityEngine.GameObject,UnityEngine.GameObject)
extern void GameObjectUtils_CopyHideFlagsRecursively_mB73BD8658D1502958E2579DA658EAA338FD27EBA (void);
// 0x0000007D T Unity.XR.CoreUtils.GameObjectUtils::ExhaustiveComponentSearch(UnityEngine.GameObject)
// 0x0000007E T Unity.XR.CoreUtils.GameObjectUtils::ExhaustiveTaggedComponentSearch(UnityEngine.GameObject,System.String)
// 0x0000007F T Unity.XR.CoreUtils.GameObjectUtils::GetComponentInScene(UnityEngine.SceneManagement.Scene)
// 0x00000080 System.Void Unity.XR.CoreUtils.GameObjectUtils::GetComponentsInScene(UnityEngine.SceneManagement.Scene,System.Collections.Generic.List`1<T>,System.Boolean)
// 0x00000081 T Unity.XR.CoreUtils.GameObjectUtils::GetComponentInActiveScene()
// 0x00000082 System.Void Unity.XR.CoreUtils.GameObjectUtils::GetComponentsInActiveScene(System.Collections.Generic.List`1<T>,System.Boolean)
// 0x00000083 System.Void Unity.XR.CoreUtils.GameObjectUtils::GetComponentsInAllScenes(System.Collections.Generic.List`1<T>,System.Boolean)
// 0x00000084 System.Void Unity.XR.CoreUtils.GameObjectUtils::GetChildGameObjects(UnityEngine.GameObject,System.Collections.Generic.List`1<UnityEngine.GameObject>)
extern void GameObjectUtils_GetChildGameObjects_m6BBD356A950CF7C1765EF511D25240BFD9A5C6B0 (void);
// 0x00000085 UnityEngine.GameObject Unity.XR.CoreUtils.GameObjectUtils::GetNamedChild(UnityEngine.GameObject,System.String)
extern void GameObjectUtils_GetNamedChild_m6723E8209E188EDAB22C8B8840F6CAA357EC9FFF (void);
// 0x00000086 System.Void Unity.XR.CoreUtils.GameObjectUtils::.cctor()
extern void GameObjectUtils__cctor_m2F87B5331CC9DA1B89546DC25904CA7876865BA4 (void);
// 0x00000087 System.Void Unity.XR.CoreUtils.GameObjectUtils/<>c__DisplayClass20_0::.ctor()
extern void U3CU3Ec__DisplayClass20_0__ctor_mB1AF49A2B6F9488132A2E8939817133973337254 (void);
// 0x00000088 System.Boolean Unity.XR.CoreUtils.GameObjectUtils/<>c__DisplayClass20_0::<GetNamedChild>b__0(UnityEngine.Transform)
extern void U3CU3Ec__DisplayClass20_0_U3CGetNamedChildU3Eb__0_m7B07F30C4FDAF96F9DF2B6864BF3E6810BDA8271 (void);
// 0x00000089 System.Boolean Unity.XR.CoreUtils.GeometryUtils::FindClosestEdge(System.Collections.Generic.List`1<UnityEngine.Vector3>,UnityEngine.Vector3,UnityEngine.Vector3&,UnityEngine.Vector3&)
extern void GeometryUtils_FindClosestEdge_m9EA8AA61F92D928592608F2487F42725806E3ABA (void);
// 0x0000008A UnityEngine.Vector3 Unity.XR.CoreUtils.GeometryUtils::PointOnOppositeSideOfPolygon(System.Collections.Generic.List`1<UnityEngine.Vector3>,UnityEngine.Vector3)
extern void GeometryUtils_PointOnOppositeSideOfPolygon_mA5DFE4CABEB58E950B407AACE54F9D95F65FDE26 (void);
// 0x0000008B System.Void Unity.XR.CoreUtils.GeometryUtils::TriangulatePolygon(System.Collections.Generic.List`1<System.Int32>,System.Int32,System.Boolean)
extern void GeometryUtils_TriangulatePolygon_mF24897C3EF9FE676012CEA4C90696F64FB52D4B3 (void);
// 0x0000008C System.Boolean Unity.XR.CoreUtils.GeometryUtils::ClosestTimesOnTwoLines(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,System.Single&,System.Single&,System.Double)
extern void GeometryUtils_ClosestTimesOnTwoLines_mAB1FEE7E8661BA5B76CBBBF8ABCD5914B0183E65 (void);
// 0x0000008D System.Boolean Unity.XR.CoreUtils.GeometryUtils::ClosestTimesOnTwoLinesXZ(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,System.Single&,System.Single&,System.Double)
extern void GeometryUtils_ClosestTimesOnTwoLinesXZ_mEC63136A734AFD9C785DED6BDD0EB9344CBCF1EE (void);
// 0x0000008E System.Boolean Unity.XR.CoreUtils.GeometryUtils::ClosestPointsOnTwoLineSegments(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Double)
extern void GeometryUtils_ClosestPointsOnTwoLineSegments_mA5285B20EF9CAAF2A76341E95A2C3E87F0000953 (void);
// 0x0000008F UnityEngine.Vector3 Unity.XR.CoreUtils.GeometryUtils::ClosestPointOnLineSegment(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)
extern void GeometryUtils_ClosestPointOnLineSegment_mC69A2DA38485EB78EAC8C08318379AE87D9C766E (void);
// 0x00000090 System.Void Unity.XR.CoreUtils.GeometryUtils::ClosestPolygonApproach(System.Collections.Generic.List`1<UnityEngine.Vector3>,System.Collections.Generic.List`1<UnityEngine.Vector3>,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single)
extern void GeometryUtils_ClosestPolygonApproach_mD0EB2883F064CC7A73484A610EA9BCB9D38FE38A (void);
// 0x00000091 System.Boolean Unity.XR.CoreUtils.GeometryUtils::PointInPolygon(UnityEngine.Vector3,System.Collections.Generic.List`1<UnityEngine.Vector3>)
extern void GeometryUtils_PointInPolygon_mCF3A7FF888E68A052AFD7F313AF82A2508B350E8 (void);
// 0x00000092 System.Boolean Unity.XR.CoreUtils.GeometryUtils::PointInPolygon3D(UnityEngine.Vector3,System.Collections.Generic.List`1<UnityEngine.Vector3>)
extern void GeometryUtils_PointInPolygon3D_mE7254C77814EEF7920165E26B57587C29B0AC45F (void);
// 0x00000093 UnityEngine.Vector3 Unity.XR.CoreUtils.GeometryUtils::ProjectPointOnPlane(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)
extern void GeometryUtils_ProjectPointOnPlane_mE5F8FE1246D84072410216ADF0AEC1F1F864A456 (void);
// 0x00000094 System.Boolean Unity.XR.CoreUtils.GeometryUtils::ConvexHull2D(System.Collections.Generic.List`1<UnityEngine.Vector3>,System.Collections.Generic.List`1<UnityEngine.Vector3>)
extern void GeometryUtils_ConvexHull2D_mE6AC212C34C42BD9F4671E3AC8522A690E7DBD9B (void);
// 0x00000095 UnityEngine.Vector3 Unity.XR.CoreUtils.GeometryUtils::PolygonCentroid2D(System.Collections.Generic.List`1<UnityEngine.Vector3>)
extern void GeometryUtils_PolygonCentroid2D_mCC31305CA9AC7A72E2B10A041E0E170568A6D38A (void);
// 0x00000096 UnityEngine.Vector2 Unity.XR.CoreUtils.GeometryUtils::OrientedMinimumBoundingBox2D(System.Collections.Generic.List`1<UnityEngine.Vector3>,UnityEngine.Vector3[])
extern void GeometryUtils_OrientedMinimumBoundingBox2D_mE8CA374604AE77B405C0E458C2A54B7B06313DCF (void);
// 0x00000097 System.Void Unity.XR.CoreUtils.GeometryUtils::RotateCalipers(UnityEngine.Vector3,System.Collections.Generic.List`1<UnityEngine.Vector3>,System.Int32&,System.Int32&,System.Int32&,System.Int32&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Vector3&)
extern void GeometryUtils_RotateCalipers_mCC4848296208373B34E7381216ECFEB0CCD24C98 (void);
// 0x00000098 UnityEngine.Quaternion Unity.XR.CoreUtils.GeometryUtils::RotationForBox(UnityEngine.Vector3[])
extern void GeometryUtils_RotationForBox_mCF3B23FB21F160C29B9F929F07B6A34D834DED09 (void);
// 0x00000099 System.Single Unity.XR.CoreUtils.GeometryUtils::ConvexPolygonArea(System.Collections.Generic.List`1<UnityEngine.Vector3>)
extern void GeometryUtils_ConvexPolygonArea_m5F5B0869330E154EB9AF8E43984343C0950B568A (void);
// 0x0000009A System.Boolean Unity.XR.CoreUtils.GeometryUtils::PolygonInPolygon(System.Collections.Generic.List`1<UnityEngine.Vector3>,System.Collections.Generic.List`1<UnityEngine.Vector3>)
extern void GeometryUtils_PolygonInPolygon_m8311330C942DDFE0AD2F400CD74A66F7D223FEB5 (void);
// 0x0000009B System.Boolean Unity.XR.CoreUtils.GeometryUtils::PolygonsWithinRange(System.Collections.Generic.List`1<UnityEngine.Vector3>,System.Collections.Generic.List`1<UnityEngine.Vector3>,System.Single)
extern void GeometryUtils_PolygonsWithinRange_mF28F82D6A44CE51CD4C4B947F281DBA1933054C3 (void);
// 0x0000009C System.Boolean Unity.XR.CoreUtils.GeometryUtils::PolygonsWithinSqRange(System.Collections.Generic.List`1<UnityEngine.Vector3>,System.Collections.Generic.List`1<UnityEngine.Vector3>,System.Single)
extern void GeometryUtils_PolygonsWithinSqRange_m0EEF68D575BA63E1AB7FF9664CE1FF8760D1E8CD (void);
// 0x0000009D System.Boolean Unity.XR.CoreUtils.GeometryUtils::PointOnPolygonBoundsXZ(UnityEngine.Vector3,System.Collections.Generic.List`1<UnityEngine.Vector3>,System.Single)
extern void GeometryUtils_PointOnPolygonBoundsXZ_m3758D9B5C86524D7F5079BE2974D3A3532FBFD52 (void);
// 0x0000009E System.Boolean Unity.XR.CoreUtils.GeometryUtils::PointOnLineSegmentXZ(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3,System.Single)
extern void GeometryUtils_PointOnLineSegmentXZ_mB398B28D94695F9B58C980FBF8C73D7EF5DCAE17 (void);
// 0x0000009F UnityEngine.Quaternion Unity.XR.CoreUtils.GeometryUtils::NormalizeRotationKeepingUp(UnityEngine.Quaternion)
extern void GeometryUtils_NormalizeRotationKeepingUp_m02F940C2EDF3FBE568AA79A8FD1F3767285AD37B (void);
// 0x000000A0 UnityEngine.Pose Unity.XR.CoreUtils.GeometryUtils::PolygonUVPoseFromPlanePose(UnityEngine.Pose)
extern void GeometryUtils_PolygonUVPoseFromPlanePose_m812F4601721F468FF33A362C79E05E8A3420B30D (void);
// 0x000000A1 UnityEngine.Vector2 Unity.XR.CoreUtils.GeometryUtils::PolygonVertexToUV(UnityEngine.Vector3,UnityEngine.Pose,UnityEngine.Pose)
extern void GeometryUtils_PolygonVertexToUV_m3AEE8EC1E4ED6F931323614A99C25DFD7789D1B5 (void);
// 0x000000A2 System.Void Unity.XR.CoreUtils.GeometryUtils::.cctor()
extern void GeometryUtils__cctor_mC01D372753F14134ED7A7051D6E5A97B9DAAD5E8 (void);
// 0x000000A3 System.Guid Unity.XR.CoreUtils.GuidUtil::Compose(System.UInt64,System.UInt64)
extern void GuidUtil_Compose_mC92886B7D4DA4351F5DA158136BF059B74E0CA90 (void);
// 0x000000A4 System.Int32 Unity.XR.CoreUtils.HashCodeUtil::Combine(System.Int32,System.Int32)
extern void HashCodeUtil_Combine_mE4BC0854C5A01EAD5211DA2ED260520F97352116 (void);
// 0x000000A5 System.Int32 Unity.XR.CoreUtils.HashCodeUtil::ReferenceHash(System.Object)
extern void HashCodeUtil_ReferenceHash_m8CC95813163E44F2EA0D5C9D111EE0313ABCCCAF (void);
// 0x000000A6 System.Int32 Unity.XR.CoreUtils.HashCodeUtil::Combine(System.Int32,System.Int32,System.Int32)
extern void HashCodeUtil_Combine_mD279582774DF01FFF0B091155F2712DAE6320DF9 (void);
// 0x000000A7 System.Int32 Unity.XR.CoreUtils.HashCodeUtil::Combine(System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashCodeUtil_Combine_mF5B1742C9C8BDBEFBDEE21388C8ECB278B2B2F21 (void);
// 0x000000A8 System.Int32 Unity.XR.CoreUtils.HashCodeUtil::Combine(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashCodeUtil_Combine_m4B164B3989B7F01D1E3F81F80547FEFBA79F20E4 (void);
// 0x000000A9 System.Int32 Unity.XR.CoreUtils.HashCodeUtil::Combine(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashCodeUtil_Combine_mBDA87310F4B4FEAD11A64C0C14326EAF38DAC7F0 (void);
// 0x000000AA System.Int32 Unity.XR.CoreUtils.HashCodeUtil::Combine(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashCodeUtil_Combine_m8005317A51D101F7B2D7210B49FC4D598B83861B (void);
// 0x000000AB System.Int32 Unity.XR.CoreUtils.HashCodeUtil::Combine(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)
extern void HashCodeUtil_Combine_m5E2E4249C0A409496B7228E27608F8AA5816359F (void);
// 0x000000AC UnityEngine.Material Unity.XR.CoreUtils.MaterialUtils::GetMaterialClone(UnityEngine.Renderer)
extern void MaterialUtils_GetMaterialClone_m5ED6EA15ABDC46665A47A80CC2F93EB2E8CB162C (void);
// 0x000000AD UnityEngine.Material Unity.XR.CoreUtils.MaterialUtils::GetMaterialClone(UnityEngine.UI.Graphic)
extern void MaterialUtils_GetMaterialClone_m00C9BAC027E4AEDE85F2C43FFE558E9D43087FA6 (void);
// 0x000000AE UnityEngine.Material[] Unity.XR.CoreUtils.MaterialUtils::CloneMaterials(UnityEngine.Renderer)
extern void MaterialUtils_CloneMaterials_m5066152CE93374F088DB986B30459DA217512861 (void);
// 0x000000AF UnityEngine.Color Unity.XR.CoreUtils.MaterialUtils::HexToColor(System.String)
extern void MaterialUtils_HexToColor_mAF1FAE50DEEEA65CBAC0C7B711CB6653F9720352 (void);
// 0x000000B0 UnityEngine.Color Unity.XR.CoreUtils.MaterialUtils::HueShift(UnityEngine.Color,System.Single)
extern void MaterialUtils_HueShift_m51EDC11258726DC003527DA4F14D8AA5E6FC6E07 (void);
// 0x000000B1 System.Void Unity.XR.CoreUtils.MaterialUtils::AddMaterial(UnityEngine.Renderer,UnityEngine.Material)
extern void MaterialUtils_AddMaterial_m4C1041B94E9AA972C86EE6C3704935F24581DA4A (void);
// 0x000000B2 System.Boolean Unity.XR.CoreUtils.MathUtility::Approximately(System.Single,System.Single)
extern void MathUtility_Approximately_mDB89C82F0E5FC9904A58FD4B2E5111FFEDEA01D1 (void);
// 0x000000B3 System.Boolean Unity.XR.CoreUtils.MathUtility::ApproximatelyZero(System.Single)
extern void MathUtility_ApproximatelyZero_m4F8E97EAD2F5CCA7D30599B685C9393875E7656D (void);
// 0x000000B4 System.Double Unity.XR.CoreUtils.MathUtility::Clamp(System.Double,System.Double,System.Double)
extern void MathUtility_Clamp_mC9BA8E481CDB4149ACB8356CFE8171B66742DBCF (void);
// 0x000000B5 System.Double Unity.XR.CoreUtils.MathUtility::ShortestAngleDistance(System.Double,System.Double,System.Double,System.Double)
extern void MathUtility_ShortestAngleDistance_m66C4E52726C62B97FFB375565965592BD4A283B6 (void);
// 0x000000B6 System.Single Unity.XR.CoreUtils.MathUtility::ShortestAngleDistance(System.Single,System.Single,System.Single,System.Single)
extern void MathUtility_ShortestAngleDistance_mE3B22F474CA49275C12E87E85BEE6DF9A3B7CFDC (void);
// 0x000000B7 System.Boolean Unity.XR.CoreUtils.MathUtility::IsUndefined(System.Single)
extern void MathUtility_IsUndefined_m66D5C7F65EC97CAF981187F9B7F9A2299C1DFF94 (void);
// 0x000000B8 System.Boolean Unity.XR.CoreUtils.MathUtility::IsAxisAligned(UnityEngine.Vector3)
extern void MathUtility_IsAxisAligned_m52997CCDEC8B8DBDEE9EF6FA31E0AFA602EC5B91 (void);
// 0x000000B9 System.Boolean Unity.XR.CoreUtils.MathUtility::IsPositivePowerOfTwo(System.Int32)
extern void MathUtility_IsPositivePowerOfTwo_m4F9B82704D9E5982B0720F9BA13A77E88D1E4E85 (void);
// 0x000000BA System.Int32 Unity.XR.CoreUtils.MathUtility::FirstActiveFlagIndex(System.Int32)
extern void MathUtility_FirstActiveFlagIndex_m367DF0B76D971F06D6A3AC1A74EB767577E3A2BC (void);
// 0x000000BB System.Void Unity.XR.CoreUtils.MathUtility::.cctor()
extern void MathUtility__cctor_mF1AF6383C249E65B6B71A6091C2FC393F565B3A3 (void);
// 0x000000BC System.Void Unity.XR.CoreUtils.NativeArrayUtils::EnsureCapacity(Unity.Collections.NativeArray`1<T>&,System.Int32,Unity.Collections.Allocator,Unity.Collections.NativeArrayOptions)
// 0x000000BD T Unity.XR.CoreUtils.ObjectPool`1::Get()
// 0x000000BE System.Void Unity.XR.CoreUtils.ObjectPool`1::Recycle(T)
// 0x000000BF System.Void Unity.XR.CoreUtils.ObjectPool`1::ClearInstance(T)
// 0x000000C0 System.Void Unity.XR.CoreUtils.ObjectPool`1::.ctor()
// 0x000000C1 System.Action`1<Unity.XR.CoreUtils.OnDestroyNotifier> Unity.XR.CoreUtils.OnDestroyNotifier::get_Destroyed()
extern void OnDestroyNotifier_get_Destroyed_m089279C60BA06F3BA1D78A8350ECE9882BD77603 (void);
// 0x000000C2 System.Void Unity.XR.CoreUtils.OnDestroyNotifier::set_Destroyed(System.Action`1<Unity.XR.CoreUtils.OnDestroyNotifier>)
extern void OnDestroyNotifier_set_Destroyed_m3C67D583F4CB4AD07A5BE88ED107C951E708A2B9 (void);
// 0x000000C3 System.Void Unity.XR.CoreUtils.OnDestroyNotifier::OnDestroy()
extern void OnDestroyNotifier_OnDestroy_m2253AFC37FC6668700F1E10857B730F60567DF90 (void);
// 0x000000C4 System.Void Unity.XR.CoreUtils.OnDestroyNotifier::.ctor()
extern void OnDestroyNotifier__ctor_mD07E187557F7C3C49B5DA9056AEED330FDAAF1E8 (void);
// 0x000000C5 System.Reflection.Assembly[] Unity.XR.CoreUtils.ReflectionUtils::GetCachedAssemblies()
extern void ReflectionUtils_GetCachedAssemblies_mA5F18B04A795FEDC83D8B42E786850C986541337 (void);
// 0x000000C6 System.Collections.Generic.List`1<System.Type[]> Unity.XR.CoreUtils.ReflectionUtils::GetCachedTypesPerAssembly()
extern void ReflectionUtils_GetCachedTypesPerAssembly_m86DA078F3122A2A8E9C28EE5F4B818F821D3C649 (void);
// 0x000000C7 System.Collections.Generic.List`1<System.Collections.Generic.Dictionary`2<System.String,System.Type>> Unity.XR.CoreUtils.ReflectionUtils::GetCachedAssemblyTypeMaps()
extern void ReflectionUtils_GetCachedAssemblyTypeMaps_mF6B13E977E8F3E7C27D8040CEC04963478F2967E (void);
// 0x000000C8 System.Void Unity.XR.CoreUtils.ReflectionUtils::PreWarmTypeCache()
extern void ReflectionUtils_PreWarmTypeCache_m4BA9D3049F0CB5E784A89CC4450ECBB852F29460 (void);
// 0x000000C9 System.Void Unity.XR.CoreUtils.ReflectionUtils::ForEachAssembly(System.Action`1<System.Reflection.Assembly>)
extern void ReflectionUtils_ForEachAssembly_mE6E08D4B8E94239B14E949F0D905356CEF4C750B (void);
// 0x000000CA System.Void Unity.XR.CoreUtils.ReflectionUtils::ForEachType(System.Action`1<System.Type>)
extern void ReflectionUtils_ForEachType_m12D12BEAE4348C46F8E5AA2E9B49815E52DB8F14 (void);
// 0x000000CB System.Type Unity.XR.CoreUtils.ReflectionUtils::FindType(System.Func`2<System.Type,System.Boolean>)
extern void ReflectionUtils_FindType_m8EFF03B529AFAAB087D24348207AF3CE8CCDB45D (void);
// 0x000000CC System.Type Unity.XR.CoreUtils.ReflectionUtils::FindTypeByFullName(System.String)
extern void ReflectionUtils_FindTypeByFullName_mDA24A5ADEE610D2DD8D4BC324EE542B863BFCE17 (void);
// 0x000000CD System.Void Unity.XR.CoreUtils.ReflectionUtils::FindTypesBatch(System.Collections.Generic.List`1<System.Func`2<System.Type,System.Boolean>>,System.Collections.Generic.List`1<System.Type>)
extern void ReflectionUtils_FindTypesBatch_mB92E0FF20A248E1235531B1BA230FBA07E3CBD71 (void);
// 0x000000CE System.Void Unity.XR.CoreUtils.ReflectionUtils::FindTypesByFullNameBatch(System.Collections.Generic.List`1<System.String>,System.Collections.Generic.List`1<System.Type>)
extern void ReflectionUtils_FindTypesByFullNameBatch_mE1707233668E04E981A3A2A6F3A560145B94908A (void);
// 0x000000CF System.Type Unity.XR.CoreUtils.ReflectionUtils::FindTypeInAssemblyByFullName(System.String,System.String)
extern void ReflectionUtils_FindTypeInAssemblyByFullName_mE2BE3F2AF5D0C2FE1B2F53B741B337CD4E3D1F7D (void);
// 0x000000D0 System.String Unity.XR.CoreUtils.ReflectionUtils::NicifyVariableName(System.String)
extern void ReflectionUtils_NicifyVariableName_mBE793D2C0FC12FA380B12C98F847EEDE93BE1D6D (void);
// 0x000000D1 T Unity.XR.CoreUtils.ScriptableSettings`1::get_Instance()
// 0x000000D2 T Unity.XR.CoreUtils.ScriptableSettings`1::CreateAndLoad()
// 0x000000D3 System.Void Unity.XR.CoreUtils.ScriptableSettings`1::.ctor()
// 0x000000D4 Unity.XR.CoreUtils.ScriptableSettingsBase Unity.XR.CoreUtils.ScriptableSettingsBase::GetInstanceByType(System.Type)
extern void ScriptableSettingsBase_GetInstanceByType_mDFE8821B150DD82581A25676A95EC50B494A835B (void);
// 0x000000D5 System.Void Unity.XR.CoreUtils.ScriptableSettingsBase::Awake()
extern void ScriptableSettingsBase_Awake_mBDD42FA0D36730C98E0E480E8E1C4E711A96B7C6 (void);
// 0x000000D6 System.Void Unity.XR.CoreUtils.ScriptableSettingsBase::OnEnable()
extern void ScriptableSettingsBase_OnEnable_m0157D649A6242B26149407B9B55F14985BCD4BAF (void);
// 0x000000D7 System.Void Unity.XR.CoreUtils.ScriptableSettingsBase::OnLoaded()
extern void ScriptableSettingsBase_OnLoaded_mBB3F1122638C208CA6F9E3EE0D70A86701401563 (void);
// 0x000000D8 System.Boolean Unity.XR.CoreUtils.ScriptableSettingsBase::ValidatePath(System.String,System.String&)
extern void ScriptableSettingsBase_ValidatePath_m6F745ADC48CADCCC7DF26D660CE0DDBB446222BB (void);
// 0x000000D9 System.Void Unity.XR.CoreUtils.ScriptableSettingsBase::.ctor()
extern void ScriptableSettingsBase__ctor_mA76719D647AA7F514E58FE1571177FFA8440F558 (void);
// 0x000000DA System.Void Unity.XR.CoreUtils.ScriptableSettingsBase::.cctor()
extern void ScriptableSettingsBase__cctor_mEA148991272FE8DE91DF2E77BC06A6C962D3BFAE (void);
// 0x000000DB System.Void Unity.XR.CoreUtils.ScriptableSettingsBase`1::.ctor()
// 0x000000DC System.Void Unity.XR.CoreUtils.ScriptableSettingsBase`1::Save(System.String)
// 0x000000DD System.String Unity.XR.CoreUtils.ScriptableSettingsBase`1::GetFilePath()
// 0x000000DE System.Void Unity.XR.CoreUtils.ScriptableSettingsBase`1::.cctor()
// 0x000000DF Unity.XR.CoreUtils.SerializableGuid Unity.XR.CoreUtils.SerializableGuid::get_Empty()
extern void SerializableGuid_get_Empty_mB5D0E94939D41D74EB9914EC728A5DCCF7F795B5 (void);
// 0x000000E0 System.Guid Unity.XR.CoreUtils.SerializableGuid::get_Guid()
extern void SerializableGuid_get_Guid_m4122C089FF196FE3C90C3EC44B1B6C30A4BBCB49 (void);
// 0x000000E1 System.Void Unity.XR.CoreUtils.SerializableGuid::.ctor(System.UInt64,System.UInt64)
extern void SerializableGuid__ctor_mCB52F194155784E55FC04EEB9A5F086FE6F3EFF2 (void);
// 0x000000E2 System.Int32 Unity.XR.CoreUtils.SerializableGuid::GetHashCode()
extern void SerializableGuid_GetHashCode_mF9A1263046263FEC8529C736D400C3ADC2280ACA (void);
// 0x000000E3 System.Boolean Unity.XR.CoreUtils.SerializableGuid::Equals(System.Object)
extern void SerializableGuid_Equals_mCE9639B13AA49783B83F2803220A7C839735515D (void);
// 0x000000E4 System.String Unity.XR.CoreUtils.SerializableGuid::ToString()
extern void SerializableGuid_ToString_m0575B5BD028C35F5919D6696BD12AB2EDADF1E70 (void);
// 0x000000E5 System.String Unity.XR.CoreUtils.SerializableGuid::ToString(System.String)
extern void SerializableGuid_ToString_m2C4DBE079278618D23A8CD447C60763AC240361E (void);
// 0x000000E6 System.String Unity.XR.CoreUtils.SerializableGuid::ToString(System.String,System.IFormatProvider)
extern void SerializableGuid_ToString_mD57FADE1F89584D3F52212B039B903A5AAC8EAEA (void);
// 0x000000E7 System.Boolean Unity.XR.CoreUtils.SerializableGuid::Equals(Unity.XR.CoreUtils.SerializableGuid)
extern void SerializableGuid_Equals_m336B71967347FB7BD21D01C6EC949404D0BCAEB9 (void);
// 0x000000E8 System.Boolean Unity.XR.CoreUtils.SerializableGuid::op_Equality(Unity.XR.CoreUtils.SerializableGuid,Unity.XR.CoreUtils.SerializableGuid)
extern void SerializableGuid_op_Equality_mEC5E2250FCD166B0A1F31BCB164FDB464DD52A24 (void);
// 0x000000E9 System.Boolean Unity.XR.CoreUtils.SerializableGuid::op_Inequality(Unity.XR.CoreUtils.SerializableGuid,Unity.XR.CoreUtils.SerializableGuid)
extern void SerializableGuid_op_Inequality_m76662FD15C95D7FC5826FAB1F0FA738B67EDC1AE (void);
// 0x000000EA System.Void Unity.XR.CoreUtils.SerializableGuid::.cctor()
extern void SerializableGuid__cctor_m75645656C914A2FEB9DF0D256193E9DEFC3F9495 (void);
// 0x000000EB Unity.XR.CoreUtils.SerializableGuid Unity.XR.CoreUtils.SerializableGuidUtil::Create(System.Guid)
extern void SerializableGuidUtil_Create_mD7ABDD5139917939FC08E62234ABD598B3463FDE (void);
// 0x000000EC System.Void Unity.XR.CoreUtils.TextureUtils::RenderTextureToTexture2D(UnityEngine.RenderTexture,UnityEngine.Texture2D)
extern void TextureUtils_RenderTextureToTexture2D_mB894DA1E00A62AEB7669F4EEB931E46DCE0B0944 (void);
// 0x000000ED System.Void Unity.XR.CoreUtils.UndoBlock::.ctor(System.String,System.Boolean)
extern void UndoBlock__ctor_mDAEDEB99D66092E46FB17AEDF712B864B0414CB3 (void);
// 0x000000EE System.Void Unity.XR.CoreUtils.UndoBlock::RegisterCreatedObject(UnityEngine.Object)
extern void UndoBlock_RegisterCreatedObject_m98FC5C9CF54100F6D841E05D0939FD39E6D8444B (void);
// 0x000000EF System.Void Unity.XR.CoreUtils.UndoBlock::RecordObject(UnityEngine.Object)
extern void UndoBlock_RecordObject_m452F0C62E711962689F7B7749CE464B7B6A3ABA7 (void);
// 0x000000F0 System.Void Unity.XR.CoreUtils.UndoBlock::SetTransformParent(UnityEngine.Transform,UnityEngine.Transform)
extern void UndoBlock_SetTransformParent_m99B405AD156DC20AAC3BE18D9168D20A039785EF (void);
// 0x000000F1 T Unity.XR.CoreUtils.UndoBlock::AddComponent(UnityEngine.GameObject)
// 0x000000F2 System.Void Unity.XR.CoreUtils.UndoBlock::Dispose(System.Boolean)
extern void UndoBlock_Dispose_m2768E03B14C160E718D182B4ED9E50A0F8BA6548 (void);
// 0x000000F3 System.Void Unity.XR.CoreUtils.UndoBlock::Dispose()
extern void UndoBlock_Dispose_mD20DB28590E9B55E8263B57D90A83E6A98E0D0E3 (void);
// 0x000000F4 System.Void Unity.XR.CoreUtils.UnityObjectUtils::Destroy(UnityEngine.Object,System.Boolean)
extern void UnityObjectUtils_Destroy_mFA9A21EE17656D12F1B126AD9C2CC4EDFD35F157 (void);
// 0x000000F5 T Unity.XR.CoreUtils.UnityObjectUtils::ConvertUnityObjectToType(UnityEngine.Object)
// 0x000000F6 System.Void Unity.XR.CoreUtils.UnityObjectUtils::RemoveDestroyedObjects(System.Collections.Generic.List`1<T>)
// 0x000000F7 System.Void Unity.XR.CoreUtils.UnityObjectUtils::RemoveDestroyedKeys(System.Collections.Generic.Dictionary`2<TKey,TValue>)
// 0x000000F8 System.Void Unity.XR.CoreUtils.XRLoggingUtils::.cctor()
extern void XRLoggingUtils__cctor_mCC38DDF72BDD9B437EE570D7D60F2A8117657A2A (void);
// 0x000000F9 System.Void Unity.XR.CoreUtils.XRLoggingUtils::Log(System.String,UnityEngine.Object)
extern void XRLoggingUtils_Log_m41CC4C5F97FFCD627CDC49D148C3997AE3683B27 (void);
// 0x000000FA System.Void Unity.XR.CoreUtils.XRLoggingUtils::LogWarning(System.String,UnityEngine.Object)
extern void XRLoggingUtils_LogWarning_m58F15039BFEC116B7AE4555A526AC7CB9EDABB7A (void);
// 0x000000FB System.Void Unity.XR.CoreUtils.XRLoggingUtils::LogError(System.String,UnityEngine.Object)
extern void XRLoggingUtils_LogError_mC5522AB4200CD87E6677D2E59C51BA5824070B63 (void);
// 0x000000FC System.Void Unity.XR.CoreUtils.XRLoggingUtils::LogException(System.Exception,UnityEngine.Object)
extern void XRLoggingUtils_LogException_m17845BE70D3F1732EAA83D29AC5B8581A3279BFD (void);
// 0x000000FD UnityEngine.Camera Unity.XR.CoreUtils.XROrigin::get_Camera()
extern void XROrigin_get_Camera_m8959027D616F5BD9AEAE3E41ADEE23BBC2CE3629 (void);
// 0x000000FE System.Void Unity.XR.CoreUtils.XROrigin::set_Camera(UnityEngine.Camera)
extern void XROrigin_set_Camera_m4C858ED48CE3A20504A55FAA1A24FE05D1CC450B (void);
// 0x000000FF UnityEngine.Transform Unity.XR.CoreUtils.XROrigin::get_TrackablesParent()
extern void XROrigin_get_TrackablesParent_m6F7933DF03A5376C31D328F865F77D28EEC18E9C (void);
// 0x00000100 System.Void Unity.XR.CoreUtils.XROrigin::set_TrackablesParent(UnityEngine.Transform)
extern void XROrigin_set_TrackablesParent_m2E813980627386E9DE2EA90D39FEEFAF80F31BC5 (void);
// 0x00000101 System.Void Unity.XR.CoreUtils.XROrigin::add_TrackablesParentTransformChanged(System.Action`1<Unity.XR.CoreUtils.ARTrackablesParentTransformChangedEventArgs>)
extern void XROrigin_add_TrackablesParentTransformChanged_m04D2A05E3000931435B7F4CAC332E0EC2693B1EF (void);
// 0x00000102 System.Void Unity.XR.CoreUtils.XROrigin::remove_TrackablesParentTransformChanged(System.Action`1<Unity.XR.CoreUtils.ARTrackablesParentTransformChangedEventArgs>)
extern void XROrigin_remove_TrackablesParentTransformChanged_m5517FF0B97A3705A7F03F8E42092165C6666163C (void);
// 0x00000103 UnityEngine.GameObject Unity.XR.CoreUtils.XROrigin::get_Origin()
extern void XROrigin_get_Origin_mCE6A3B327ACE6FAEDCC67A9DC952FEED191C26B6 (void);
// 0x00000104 System.Void Unity.XR.CoreUtils.XROrigin::set_Origin(UnityEngine.GameObject)
extern void XROrigin_set_Origin_m832CE9176B8C54EDC64059AFC67807EFE078249E (void);
// 0x00000105 UnityEngine.GameObject Unity.XR.CoreUtils.XROrigin::get_CameraFloorOffsetObject()
extern void XROrigin_get_CameraFloorOffsetObject_m24DB58FD33D0D5436DC3A6F023D8780C1B82FD07 (void);
// 0x00000106 System.Void Unity.XR.CoreUtils.XROrigin::set_CameraFloorOffsetObject(UnityEngine.GameObject)
extern void XROrigin_set_CameraFloorOffsetObject_m3182CAC8A600DB7EF22432EA3B71BF76A21C4839 (void);
// 0x00000107 Unity.XR.CoreUtils.XROrigin/TrackingOriginMode Unity.XR.CoreUtils.XROrigin::get_RequestedTrackingOriginMode()
extern void XROrigin_get_RequestedTrackingOriginMode_m8475634D9A0C8ECA371A3F2EC216A55F7D2F2D3C (void);
// 0x00000108 System.Void Unity.XR.CoreUtils.XROrigin::set_RequestedTrackingOriginMode(Unity.XR.CoreUtils.XROrigin/TrackingOriginMode)
extern void XROrigin_set_RequestedTrackingOriginMode_m3B166DBAA7C7B18C63EBEA83A308911C094DF554 (void);
// 0x00000109 System.Single Unity.XR.CoreUtils.XROrigin::get_CameraYOffset()
extern void XROrigin_get_CameraYOffset_m223B472CA64A210F0F315A503FF621A6C74EC2A3 (void);
// 0x0000010A System.Void Unity.XR.CoreUtils.XROrigin::set_CameraYOffset(System.Single)
extern void XROrigin_set_CameraYOffset_mE11AF77FBC8B774E6CED34B10960AC9F747B67D1 (void);
// 0x0000010B UnityEngine.XR.TrackingOriginModeFlags Unity.XR.CoreUtils.XROrigin::get_CurrentTrackingOriginMode()
extern void XROrigin_get_CurrentTrackingOriginMode_m3117576FC85371E692EFFA853AF5297CEF150589 (void);
// 0x0000010C System.Void Unity.XR.CoreUtils.XROrigin::set_CurrentTrackingOriginMode(UnityEngine.XR.TrackingOriginModeFlags)
extern void XROrigin_set_CurrentTrackingOriginMode_mD2DF2D77407214FFDBED47C114DB0C1348C4F84E (void);
// 0x0000010D UnityEngine.Vector3 Unity.XR.CoreUtils.XROrigin::get_OriginInCameraSpacePos()
extern void XROrigin_get_OriginInCameraSpacePos_mF8CAAA59DDF4635AD3D7B1237B0742AA9BE283E6 (void);
// 0x0000010E UnityEngine.Vector3 Unity.XR.CoreUtils.XROrigin::get_CameraInOriginSpacePos()
extern void XROrigin_get_CameraInOriginSpacePos_m6646CE94E1798A767E559EB1D785D00AE8C68EB1 (void);
// 0x0000010F System.Single Unity.XR.CoreUtils.XROrigin::get_CameraInOriginSpaceHeight()
extern void XROrigin_get_CameraInOriginSpaceHeight_m1DC15C0A56A969838A827F425ABBED375751BFC5 (void);
// 0x00000110 System.Void Unity.XR.CoreUtils.XROrigin::MoveOffsetHeight()
extern void XROrigin_MoveOffsetHeight_m6336FBEAEA9FA0742D0B1740E2316A9CABDAA7AF (void);
// 0x00000111 System.Void Unity.XR.CoreUtils.XROrigin::MoveOffsetHeight(System.Single)
extern void XROrigin_MoveOffsetHeight_mF0B8B1C8D45F9EF0D2DD9534B443D6EC2A9FC248 (void);
// 0x00000112 System.Void Unity.XR.CoreUtils.XROrigin::TryInitializeCamera()
extern void XROrigin_TryInitializeCamera_mA9C7C0C6C44A0694CDA78FD55D1952E9506717C3 (void);
// 0x00000113 System.Boolean Unity.XR.CoreUtils.XROrigin::SetupCamera()
extern void XROrigin_SetupCamera_mE5719DCA5F732BE1D7BA0F543C614851A9D43655 (void);
// 0x00000114 System.Boolean Unity.XR.CoreUtils.XROrigin::SetupCamera(UnityEngine.XR.XRInputSubsystem)
extern void XROrigin_SetupCamera_mB2D4BC328855A681FAE0D20BB2011E44C98A0E89 (void);
// 0x00000115 System.Void Unity.XR.CoreUtils.XROrigin::OnInputSubsystemTrackingOriginUpdated(UnityEngine.XR.XRInputSubsystem)
extern void XROrigin_OnInputSubsystemTrackingOriginUpdated_m1D58DF267E36A73C5C5C5E155284D64D83810FD9 (void);
// 0x00000116 System.Collections.IEnumerator Unity.XR.CoreUtils.XROrigin::RepeatInitializeCamera()
extern void XROrigin_RepeatInitializeCamera_mB5CEC27430D87F2017CFD3DAEC8275D68D71F319 (void);
// 0x00000117 System.Boolean Unity.XR.CoreUtils.XROrigin::RotateAroundCameraUsingOriginUp(System.Single)
extern void XROrigin_RotateAroundCameraUsingOriginUp_m42AE0DFCFBA84AC3CBAAB74D78FB1EA361102EA2 (void);
// 0x00000118 System.Boolean Unity.XR.CoreUtils.XROrigin::RotateAroundCameraPosition(UnityEngine.Vector3,System.Single)
extern void XROrigin_RotateAroundCameraPosition_m7E496775B85028CDE1EDA5DFEFC36350F371AA59 (void);
// 0x00000119 System.Boolean Unity.XR.CoreUtils.XROrigin::MatchOriginUp(UnityEngine.Vector3)
extern void XROrigin_MatchOriginUp_m21E7F97625F9C616B757226DB083A8FE00297D1C (void);
// 0x0000011A System.Boolean Unity.XR.CoreUtils.XROrigin::MatchOriginUpCameraForward(UnityEngine.Vector3,UnityEngine.Vector3)
extern void XROrigin_MatchOriginUpCameraForward_m8D6A19292733DBEA380BF94DF74A6F9BC33E1F90 (void);
// 0x0000011B System.Boolean Unity.XR.CoreUtils.XROrigin::MatchOriginUpOriginForward(UnityEngine.Vector3,UnityEngine.Vector3)
extern void XROrigin_MatchOriginUpOriginForward_m6BB0CD69861590B4CA6F850D6824A47B37D2D5D3 (void);
// 0x0000011C System.Boolean Unity.XR.CoreUtils.XROrigin::MoveCameraToWorldLocation(UnityEngine.Vector3)
extern void XROrigin_MoveCameraToWorldLocation_m7AA0DF514F9F8E9E68541C314FAB868D043E5B4D (void);
// 0x0000011D System.Void Unity.XR.CoreUtils.XROrigin::Awake()
extern void XROrigin_Awake_mFC495553BD50D97BCE9BB513A9B2D4C2262B21A0 (void);
// 0x0000011E UnityEngine.Pose Unity.XR.CoreUtils.XROrigin::GetCameraOriginPose()
extern void XROrigin_GetCameraOriginPose_m8FBEAA33C27E17B6F461C20C6E53B51E6CDAFFCB (void);
// 0x0000011F System.Void Unity.XR.CoreUtils.XROrigin::OnEnable()
extern void XROrigin_OnEnable_mFD45AFA9DDB9FE432E6037D592DCEDBD28DF3ECA (void);
// 0x00000120 System.Void Unity.XR.CoreUtils.XROrigin::OnDisable()
extern void XROrigin_OnDisable_m26B6FCDEAC250714CDEAC57039B8ADE42B1B411C (void);
// 0x00000121 System.Void Unity.XR.CoreUtils.XROrigin::OnBeforeRender()
extern void XROrigin_OnBeforeRender_m4D292D007D30840EE0BBEE60BB1EF3E1FB3DF018 (void);
// 0x00000122 System.Void Unity.XR.CoreUtils.XROrigin::OnValidate()
extern void XROrigin_OnValidate_m26B16F4C6A9414A815BDED9E3C3075C9AA2E2AD3 (void);
// 0x00000123 System.Void Unity.XR.CoreUtils.XROrigin::Start()
extern void XROrigin_Start_m7F0D352F4EBEC07BE7C779E8B2997E6121C2E445 (void);
// 0x00000124 System.Void Unity.XR.CoreUtils.XROrigin::OnDestroy()
extern void XROrigin_OnDestroy_m90EB715086F324C473AA55917913EF5F130ED2DE (void);
// 0x00000125 System.Void Unity.XR.CoreUtils.XROrigin::.ctor()
extern void XROrigin__ctor_mD5AE064CEEF8128D772FABADBA6CDC2993C8079A (void);
// 0x00000126 System.Void Unity.XR.CoreUtils.XROrigin::.cctor()
extern void XROrigin__cctor_m07732DF2A1B8B4A195B2671173B9F89B8DDD776B (void);
// 0x00000127 System.Boolean Unity.XR.CoreUtils.XROrigin::<OnValidate>g__IsModeStale|60_0()
extern void XROrigin_U3COnValidateU3Eg__IsModeStaleU7C60_0_mB0B1ACE8BD4524258867625EA557440EF4CA5C30 (void);
// 0x00000128 System.Void Unity.XR.CoreUtils.XROrigin/<RepeatInitializeCamera>d__48::.ctor(System.Int32)
extern void U3CRepeatInitializeCameraU3Ed__48__ctor_mEA756E5C40DFD2B29DAAEC516D2C2C4EC21837BA (void);
// 0x00000129 System.Void Unity.XR.CoreUtils.XROrigin/<RepeatInitializeCamera>d__48::System.IDisposable.Dispose()
extern void U3CRepeatInitializeCameraU3Ed__48_System_IDisposable_Dispose_m90C7F4D459D9169823A8741D4D8A8C4B7F097E1D (void);
// 0x0000012A System.Boolean Unity.XR.CoreUtils.XROrigin/<RepeatInitializeCamera>d__48::MoveNext()
extern void U3CRepeatInitializeCameraU3Ed__48_MoveNext_m360FC31D4B39708F2973FBB50495B52C8E07A045 (void);
// 0x0000012B System.Object Unity.XR.CoreUtils.XROrigin/<RepeatInitializeCamera>d__48::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRepeatInitializeCameraU3Ed__48_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCD469BF5623FB12496FA276FD3960E2E0C3038F1 (void);
// 0x0000012C System.Void Unity.XR.CoreUtils.XROrigin/<RepeatInitializeCamera>d__48::System.Collections.IEnumerator.Reset()
extern void U3CRepeatInitializeCameraU3Ed__48_System_Collections_IEnumerator_Reset_m5A0B2B0CD539415D4C1A16942B2E63B08DBA95D0 (void);
// 0x0000012D System.Object Unity.XR.CoreUtils.XROrigin/<RepeatInitializeCamera>d__48::System.Collections.IEnumerator.get_Current()
extern void U3CRepeatInitializeCameraU3Ed__48_System_Collections_IEnumerator_get_Current_m083AEC9CB984904F6E211766D5A4AAA0A8F17BDF (void);
// 0x0000012E System.Void Unity.XR.CoreUtils.Datums.AnimationCurveDatum::.ctor()
extern void AnimationCurveDatum__ctor_mCB9F5DE7324578D1B0329ED4205F6945ECEFE3E2 (void);
// 0x0000012F System.Void Unity.XR.CoreUtils.Datums.AnimationCurveDatumProperty::.ctor(UnityEngine.AnimationCurve)
extern void AnimationCurveDatumProperty__ctor_m6544F46DBCF1D1BAE33AF0E635DE16D0463C9E80 (void);
// 0x00000130 System.Void Unity.XR.CoreUtils.Datums.AnimationCurveDatumProperty::.ctor(Unity.XR.CoreUtils.Datums.AnimationCurveDatum)
extern void AnimationCurveDatumProperty__ctor_mBB9CEA0E13EFFD138543E7542627DE56B8DBEA3D (void);
// 0x00000131 System.String Unity.XR.CoreUtils.Datums.Datum`1::get_Comments()
// 0x00000132 System.Void Unity.XR.CoreUtils.Datums.Datum`1::set_Comments(System.String)
// 0x00000133 System.Boolean Unity.XR.CoreUtils.Datums.Datum`1::get_ReadOnly()
// 0x00000134 System.Void Unity.XR.CoreUtils.Datums.Datum`1::set_ReadOnly(System.Boolean)
// 0x00000135 Unity.XR.CoreUtils.Bindings.Variables.IReadOnlyBindableVariable`1<T> Unity.XR.CoreUtils.Datums.Datum`1::get_BindableVariableReference()
// 0x00000136 T Unity.XR.CoreUtils.Datums.Datum`1::get_Value()
// 0x00000137 System.Void Unity.XR.CoreUtils.Datums.Datum`1::set_Value(T)
// 0x00000138 System.Void Unity.XR.CoreUtils.Datums.Datum`1::OnEnable()
// 0x00000139 System.Void Unity.XR.CoreUtils.Datums.Datum`1::.ctor()
// 0x0000013A System.Void Unity.XR.CoreUtils.Datums.DatumProperty`2::.ctor()
// 0x0000013B System.Void Unity.XR.CoreUtils.Datums.DatumProperty`2::.ctor(TValue)
// 0x0000013C System.Void Unity.XR.CoreUtils.Datums.DatumProperty`2::.ctor(TDatum)
// 0x0000013D TValue Unity.XR.CoreUtils.Datums.DatumProperty`2::get_Value()
// 0x0000013E System.Void Unity.XR.CoreUtils.Datums.DatumProperty`2::set_Value(TValue)
// 0x0000013F Unity.XR.CoreUtils.Datums.Datum`1<TValue> Unity.XR.CoreUtils.Datums.DatumProperty`2::get_Datum()
// 0x00000140 TValue Unity.XR.CoreUtils.Datums.DatumProperty`2::get_ConstantValue()
// 0x00000141 TValue Unity.XR.CoreUtils.Datums.DatumProperty`2::op_Implicit(Unity.XR.CoreUtils.Datums.DatumProperty`2<TValue,TDatum>)
// 0x00000142 System.Void Unity.XR.CoreUtils.Datums.FloatDatum::.ctor()
extern void FloatDatum__ctor_m746EBB97665A6285A158D1BC33D0F37A0134250F (void);
// 0x00000143 System.Void Unity.XR.CoreUtils.Datums.FloatDatumProperty::.ctor(System.Single)
extern void FloatDatumProperty__ctor_m3DD79AF9D444666BDD91FCF272BF011809878A5A (void);
// 0x00000144 System.Void Unity.XR.CoreUtils.Datums.FloatDatumProperty::.ctor(Unity.XR.CoreUtils.Datums.FloatDatum)
extern void FloatDatumProperty__ctor_m07B133ACD210CD1C65645E9D03C2AE5790FA4321 (void);
// 0x00000145 System.Void Unity.XR.CoreUtils.Datums.IntDatum::SetValueRounded(System.Single)
extern void IntDatum_SetValueRounded_m59EC4B8DE575BB14AEBC78D995C093BF49FBFBD1 (void);
// 0x00000146 System.Void Unity.XR.CoreUtils.Datums.IntDatum::.ctor()
extern void IntDatum__ctor_mD3B338867E8976BB649F98AF4BAB7E9CA5A3F4D6 (void);
// 0x00000147 System.Void Unity.XR.CoreUtils.Datums.IntDatumProperty::.ctor(System.Int32)
extern void IntDatumProperty__ctor_mAB95877405370ACA469BCEB3BE1058243D53D80C (void);
// 0x00000148 System.Void Unity.XR.CoreUtils.Datums.IntDatumProperty::.ctor(Unity.XR.CoreUtils.Datums.IntDatum)
extern void IntDatumProperty__ctor_mBAB490E7375D65C280CF9A5433D7DF1189D51663 (void);
// 0x00000149 System.Void Unity.XR.CoreUtils.Datums.StringDatum::.ctor()
extern void StringDatum__ctor_m6B3E3BDE5B34B32E826EBC5ECEA6CECB00E29CB8 (void);
// 0x0000014A System.Void Unity.XR.CoreUtils.Datums.StringDatumProperty::.ctor(System.String)
extern void StringDatumProperty__ctor_mDBFDE37F1FC8D1BF0DD1AB8C3ED6D90A1B4169E8 (void);
// 0x0000014B System.Void Unity.XR.CoreUtils.Datums.StringDatumProperty::.ctor(Unity.XR.CoreUtils.Datums.StringDatum)
extern void StringDatumProperty__ctor_mB4D7997FEAD1E57E83CE2A64163D2D84603A2169 (void);
// 0x0000014C System.Int32 Unity.XR.CoreUtils.Collections.HashSetList`1::get_Count()
// 0x0000014D System.Boolean Unity.XR.CoreUtils.Collections.HashSetList`1::System.Collections.Generic.ICollection<T>.get_IsReadOnly()
// 0x0000014E T Unity.XR.CoreUtils.Collections.HashSetList`1::get_Item(System.Int32)
// 0x0000014F System.Void Unity.XR.CoreUtils.Collections.HashSetList`1::.ctor(System.Int32)
// 0x00000150 System.Collections.Generic.List`1/Enumerator<T> Unity.XR.CoreUtils.Collections.HashSetList`1::GetEnumerator()
// 0x00000151 System.Collections.Generic.IEnumerator`1<T> Unity.XR.CoreUtils.Collections.HashSetList`1::System.Collections.Generic.IEnumerable<T>.GetEnumerator()
// 0x00000152 System.Collections.IEnumerator Unity.XR.CoreUtils.Collections.HashSetList`1::System.Collections.IEnumerable.GetEnumerator()
// 0x00000153 System.Void Unity.XR.CoreUtils.Collections.HashSetList`1::System.Collections.Generic.ICollection<T>.Add(T)
// 0x00000154 System.Boolean Unity.XR.CoreUtils.Collections.HashSetList`1::Add(T)
// 0x00000155 System.Boolean Unity.XR.CoreUtils.Collections.HashSetList`1::Remove(T)
// 0x00000156 System.Void Unity.XR.CoreUtils.Collections.HashSetList`1::ExceptWith(System.Collections.Generic.IEnumerable`1<T>)
// 0x00000157 System.Void Unity.XR.CoreUtils.Collections.HashSetList`1::IntersectWith(System.Collections.Generic.IEnumerable`1<T>)
// 0x00000158 System.Boolean Unity.XR.CoreUtils.Collections.HashSetList`1::IsProperSubsetOf(System.Collections.Generic.IEnumerable`1<T>)
// 0x00000159 System.Boolean Unity.XR.CoreUtils.Collections.HashSetList`1::IsProperSupersetOf(System.Collections.Generic.IEnumerable`1<T>)
// 0x0000015A System.Boolean Unity.XR.CoreUtils.Collections.HashSetList`1::IsSubsetOf(System.Collections.Generic.IEnumerable`1<T>)
// 0x0000015B System.Boolean Unity.XR.CoreUtils.Collections.HashSetList`1::IsSupersetOf(System.Collections.Generic.IEnumerable`1<T>)
// 0x0000015C System.Boolean Unity.XR.CoreUtils.Collections.HashSetList`1::Overlaps(System.Collections.Generic.IEnumerable`1<T>)
// 0x0000015D System.Boolean Unity.XR.CoreUtils.Collections.HashSetList`1::SetEquals(System.Collections.Generic.IEnumerable`1<T>)
// 0x0000015E System.Void Unity.XR.CoreUtils.Collections.HashSetList`1::SymmetricExceptWith(System.Collections.Generic.IEnumerable`1<T>)
// 0x0000015F System.Void Unity.XR.CoreUtils.Collections.HashSetList`1::UnionWith(System.Collections.Generic.IEnumerable`1<T>)
// 0x00000160 System.Void Unity.XR.CoreUtils.Collections.HashSetList`1::Clear()
// 0x00000161 System.Boolean Unity.XR.CoreUtils.Collections.HashSetList`1::Contains(T)
// 0x00000162 System.Void Unity.XR.CoreUtils.Collections.HashSetList`1::CopyTo(T[],System.Int32)
// 0x00000163 System.Void Unity.XR.CoreUtils.Collections.HashSetList`1::GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)
// 0x00000164 System.Void Unity.XR.CoreUtils.Collections.HashSetList`1::OnDeserialization(System.Object)
// 0x00000165 System.Void Unity.XR.CoreUtils.Collections.HashSetList`1::RefreshList()
// 0x00000166 System.Collections.Generic.IReadOnlyList`1<T> Unity.XR.CoreUtils.Collections.HashSetList`1::AsList()
// 0x00000167 System.Int32 Unity.XR.CoreUtils.Collections.ReadOnlyList`1::get_Count()
// 0x00000168 T Unity.XR.CoreUtils.Collections.ReadOnlyList`1::get_Item(System.Int32)
// 0x00000169 System.Void Unity.XR.CoreUtils.Collections.ReadOnlyList`1::.ctor(System.Collections.Generic.List`1<T>)
// 0x0000016A System.Collections.Generic.List`1/Enumerator<T> Unity.XR.CoreUtils.Collections.ReadOnlyList`1::GetEnumerator()
// 0x0000016B System.Collections.Generic.IEnumerator`1<T> Unity.XR.CoreUtils.Collections.ReadOnlyList`1::System.Collections.Generic.IEnumerable<T>.GetEnumerator()
// 0x0000016C System.Collections.IEnumerator Unity.XR.CoreUtils.Collections.ReadOnlyList`1::System.Collections.IEnumerable.GetEnumerator()
// 0x0000016D System.Collections.Generic.List`1<Unity.XR.CoreUtils.Collections.SerializableDictionary`2/Item<TKey,TValue>> Unity.XR.CoreUtils.Collections.SerializableDictionary`2::get_SerializedItems()
// 0x0000016E System.Void Unity.XR.CoreUtils.Collections.SerializableDictionary`2::.ctor()
// 0x0000016F System.Void Unity.XR.CoreUtils.Collections.SerializableDictionary`2::.ctor(System.Collections.Generic.IDictionary`2<TKey,TValue>)
// 0x00000170 System.Void Unity.XR.CoreUtils.Collections.SerializableDictionary`2::OnBeforeSerialize()
// 0x00000171 System.Void Unity.XR.CoreUtils.Collections.SerializableDictionary`2::OnAfterDeserialize()
// 0x00000172 System.Void Unity.XR.CoreUtils.Capabilities.CapabilityProfile::add_CapabilityChanged(System.Action`1<Unity.XR.CoreUtils.Capabilities.CapabilityProfile>)
extern void CapabilityProfile_add_CapabilityChanged_mAE600078BCC8CE0A86F5F502AE6472E9DA95F69A (void);
// 0x00000173 System.Void Unity.XR.CoreUtils.Capabilities.CapabilityProfile::remove_CapabilityChanged(System.Action`1<Unity.XR.CoreUtils.Capabilities.CapabilityProfile>)
extern void CapabilityProfile_remove_CapabilityChanged_mFB43EB950CC81EB8F7ECCA71CD3B1DE87230BCA4 (void);
// 0x00000174 System.Void Unity.XR.CoreUtils.Capabilities.CapabilityProfile::ReportCapabilityChanged()
extern void CapabilityProfile_ReportCapabilityChanged_m982911A7EC7540EAA01DCE63832E20EA85E1BAB7 (void);
// 0x00000175 System.Void Unity.XR.CoreUtils.Capabilities.CapabilityProfile::.ctor()
extern void CapabilityProfile__ctor_mC876B490D739A543CCE3681BB4EC06351864C20E (void);
// 0x00000176 System.Void Unity.XR.CoreUtils.Capabilities.CapabilityDictionary::ForceSerialize()
extern void CapabilityDictionary_ForceSerialize_m94EB2842F14B8989D3FBBD173B31BF1C48DD2DAC (void);
// 0x00000177 System.Void Unity.XR.CoreUtils.Capabilities.CapabilityDictionary::OnBeforeSerialize()
extern void CapabilityDictionary_OnBeforeSerialize_m530D56E1F39A2E17E5E110A070674CCFDB3E13F1 (void);
// 0x00000178 System.Void Unity.XR.CoreUtils.Capabilities.CapabilityDictionary::.ctor()
extern void CapabilityDictionary__ctor_m149B6D19C0E4B5493FFA729C6A67632BE767DFA4 (void);
// 0x00000179 System.Void Unity.XR.CoreUtils.Capabilities.CustomCapabilityKeyAttribute::.ctor(System.Int32)
extern void CustomCapabilityKeyAttribute__ctor_mEB750E551167D1BEB548A32E552EE6892E32F812 (void);
// 0x0000017A System.Boolean Unity.XR.CoreUtils.Capabilities.ICapabilityModifier::TryGetCapabilityValue(System.String,System.Boolean&)
// 0x0000017B System.Void Unity.XR.CoreUtils.Bindings.BindingsGroup::AddBinding(Unity.XR.CoreUtils.Bindings.IEventBinding)
extern void BindingsGroup_AddBinding_m18697E73861A1C67DE0674F162B43CCE9E67C89B (void);
// 0x0000017C System.Void Unity.XR.CoreUtils.Bindings.BindingsGroup::ClearBinding(Unity.XR.CoreUtils.Bindings.IEventBinding)
extern void BindingsGroup_ClearBinding_m31D78883F22CB8FABEDCBA467F899025844DF8CF (void);
// 0x0000017D System.Void Unity.XR.CoreUtils.Bindings.BindingsGroup::Bind()
extern void BindingsGroup_Bind_mEB5D800FD760BE473E8AE69B2BEE468721DB6689 (void);
// 0x0000017E System.Void Unity.XR.CoreUtils.Bindings.BindingsGroup::Unbind()
extern void BindingsGroup_Unbind_mB331E12F9A051D5BB4552B8E3EA7680B41DB81B8 (void);
// 0x0000017F System.Void Unity.XR.CoreUtils.Bindings.BindingsGroup::Clear()
extern void BindingsGroup_Clear_m092668459F10E46245228224D1334DAB089B72E3 (void);
// 0x00000180 System.Void Unity.XR.CoreUtils.Bindings.BindingsGroup::.ctor()
extern void BindingsGroup__ctor_m1EFD3F405050571D3A66F9B95E8FBC296866A716 (void);
// 0x00000181 System.Action Unity.XR.CoreUtils.Bindings.EventBinding::get_BindAction()
extern void EventBinding_get_BindAction_m4EB40C78013954CE11D84F801EEEBDE34BF158A5 (void);
// 0x00000182 System.Void Unity.XR.CoreUtils.Bindings.EventBinding::set_BindAction(System.Action)
extern void EventBinding_set_BindAction_mA347945485E3D62DE3724949A23F5B81D772B1B4 (void);
// 0x00000183 System.Action Unity.XR.CoreUtils.Bindings.EventBinding::get_UnbindAction()
extern void EventBinding_get_UnbindAction_m15D4D1FB573EB315F412346C20C46A47C0576F24 (void);
// 0x00000184 System.Void Unity.XR.CoreUtils.Bindings.EventBinding::set_UnbindAction(System.Action)
extern void EventBinding_set_UnbindAction_m619D3FBBC839918C46E94BE3E4C4E6224FC88770 (void);
// 0x00000185 System.Boolean Unity.XR.CoreUtils.Bindings.EventBinding::get_IsBound()
extern void EventBinding_get_IsBound_mDB69C30373F220F1AEC541228E5489DAA403548C (void);
// 0x00000186 System.Void Unity.XR.CoreUtils.Bindings.EventBinding::.ctor(System.Action,System.Action)
extern void EventBinding__ctor_m8565D0F2D1084AF810D4BE0D5272D366DB102AC4 (void);
// 0x00000187 System.Void Unity.XR.CoreUtils.Bindings.EventBinding::Bind()
extern void EventBinding_Bind_m4EF06F026C80482430123A70B1F1068EEF90EB1B (void);
// 0x00000188 System.Void Unity.XR.CoreUtils.Bindings.EventBinding::Unbind()
extern void EventBinding_Unbind_mA991D6B531B6178FB3AA382DE29FD4EA010816F5 (void);
// 0x00000189 System.Void Unity.XR.CoreUtils.Bindings.EventBinding::ClearBinding()
extern void EventBinding_ClearBinding_mA6B5BF23CDAD914AD7F8C78909786A866E33DCC5 (void);
// 0x0000018A System.Boolean Unity.XR.CoreUtils.Bindings.IEventBinding::get_IsBound()
// 0x0000018B System.Void Unity.XR.CoreUtils.Bindings.IEventBinding::Bind()
// 0x0000018C System.Void Unity.XR.CoreUtils.Bindings.IEventBinding::Unbind()
// 0x0000018D System.Void Unity.XR.CoreUtils.Bindings.IEventBinding::ClearBinding()
// 0x0000018E System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableEnum`1::.ctor(T,System.Boolean,System.Func`3<T,T,System.Boolean>,System.Boolean)
// 0x0000018F System.Boolean Unity.XR.CoreUtils.Bindings.Variables.BindableEnum`1::ValueEquals(T)
// 0x00000190 System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariable`1::.ctor(T,System.Boolean,System.Func`3<T,T,System.Boolean>,System.Boolean)
// 0x00000191 System.Boolean Unity.XR.CoreUtils.Bindings.Variables.BindableVariable`1::ValueEquals(T)
// 0x00000192 System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableAlloc`1::.ctor(T,System.Boolean,System.Func`3<T,T,System.Boolean>,System.Boolean)
// 0x00000193 System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1::add_valueUpdated(System.Action`1<T>)
// 0x00000194 System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1::remove_valueUpdated(System.Action`1<T>)
// 0x00000195 T Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1::get_Value()
// 0x00000196 System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1::set_Value(T)
// 0x00000197 System.Int32 Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1::get_BindingCount()
// 0x00000198 System.Boolean Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1::SetValueWithoutNotify(T)
// 0x00000199 Unity.XR.CoreUtils.Bindings.IEventBinding Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1::Subscribe(System.Action`1<T>)
// 0x0000019A Unity.XR.CoreUtils.Bindings.IEventBinding Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1::SubscribeAndUpdate(System.Action`1<T>)
// 0x0000019B System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1::Unsubscribe(System.Action`1<T>)
// 0x0000019C System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1::IncrementReferenceCount()
// 0x0000019D System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1::DecrementReferenceCount()
// 0x0000019E System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1::.ctor(T,System.Boolean,System.Func`3<T,T,System.Boolean>,System.Boolean)
// 0x0000019F System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1::BroadcastValue()
// 0x000001A0 System.Threading.Tasks.Task`1<T> Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1::Task(System.Func`2<T,System.Boolean>,System.Threading.CancellationToken)
// 0x000001A1 System.Threading.Tasks.Task`1<T> Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1::Task(T,System.Threading.CancellationToken)
// 0x000001A2 System.Boolean Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1::ValueEquals(T)
// 0x000001A3 System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1/<>c__DisplayClass14_0::.ctor()
// 0x000001A4 System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1/<>c__DisplayClass14_0::<Subscribe>b__0()
// 0x000001A5 System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableBase`1/<>c__DisplayClass14_0::<Subscribe>b__1()
// 0x000001A6 System.Threading.Tasks.Task`1<T> Unity.XR.CoreUtils.Bindings.Variables.BindableVariableTaskPredicate`1::get_Task()
// 0x000001A7 System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableTaskPredicate`1::.ctor(Unity.XR.CoreUtils.Bindings.Variables.IReadOnlyBindableVariable`1<T>,System.Func`2<T,System.Boolean>,System.Threading.CancellationToken)
// 0x000001A8 System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableTaskPredicate`1::Cancelled()
// 0x000001A9 System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableTaskPredicate`1::Await(T)
// 0x000001AA System.Threading.Tasks.Task`1<T> Unity.XR.CoreUtils.Bindings.Variables.BindableVariableTaskState`1::get_task()
// 0x000001AB System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableTaskState`1::.ctor(Unity.XR.CoreUtils.Bindings.Variables.IReadOnlyBindableVariable`1<T>,T,System.Threading.CancellationToken)
// 0x000001AC System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableTaskState`1::Cancelled()
// 0x000001AD System.Void Unity.XR.CoreUtils.Bindings.Variables.BindableVariableTaskState`1::Await(T)
// 0x000001AE Unity.XR.CoreUtils.Bindings.IEventBinding Unity.XR.CoreUtils.Bindings.Variables.IReadOnlyBindableVariable`1::Subscribe(System.Action`1<T>)
// 0x000001AF Unity.XR.CoreUtils.Bindings.IEventBinding Unity.XR.CoreUtils.Bindings.Variables.IReadOnlyBindableVariable`1::SubscribeAndUpdate(System.Action`1<T>)
// 0x000001B0 System.Void Unity.XR.CoreUtils.Bindings.Variables.IReadOnlyBindableVariable`1::Unsubscribe(System.Action`1<T>)
// 0x000001B1 T Unity.XR.CoreUtils.Bindings.Variables.IReadOnlyBindableVariable`1::get_Value()
// 0x000001B2 System.Int32 Unity.XR.CoreUtils.Bindings.Variables.IReadOnlyBindableVariable`1::get_BindingCount()
// 0x000001B3 System.Boolean Unity.XR.CoreUtils.Bindings.Variables.IReadOnlyBindableVariable`1::ValueEquals(T)
// 0x000001B4 System.Threading.Tasks.Task`1<T> Unity.XR.CoreUtils.Bindings.Variables.IReadOnlyBindableVariable`1::Task(System.Func`2<T,System.Boolean>,System.Threading.CancellationToken)
// 0x000001B5 System.Threading.Tasks.Task`1<T> Unity.XR.CoreUtils.Bindings.Variables.IReadOnlyBindableVariable`1::Task(T,System.Threading.CancellationToken)
// 0x000001B6 System.Void Unity.XR.CoreUtils.GUI.EnumDisplayAttribute::.ctor(System.Object[])
extern void EnumDisplayAttribute__ctor_mA3848EC7E676432BB6BF008C27E7E2C2B63A576A (void);
// 0x000001B7 System.Void Unity.XR.CoreUtils.GUI.FlagsPropertyAttribute::.ctor()
extern void FlagsPropertyAttribute__ctor_mB1E45E123C188E357A1FB8D6FA2ADD9182A9D034 (void);
static Il2CppMethodPointer s_methodPointers[439] = 
{
	ARTrackablesParentTransformChangedEventArgs_get_Origin_m91D7C3638FBF94D468AD4467ABB2EC9500753F25,
	ARTrackablesParentTransformChangedEventArgs_get_TrackablesParent_m89F1B7B428A07F5142AEC2BF32A83B35B52C0425,
	ARTrackablesParentTransformChangedEventArgs__ctor_m46B5D18DF81A7296E36E37917E69AB9E748B6278,
	ARTrackablesParentTransformChangedEventArgs_Equals_m8CAA7BD42F09BF7349818EF3166792652FB9F4AE,
	ARTrackablesParentTransformChangedEventArgs_Equals_mE6F5F659DD06166ACDDCE62B0652F014568D003B,
	ARTrackablesParentTransformChangedEventArgs_GetHashCode_mA5FDE9D7D0F5F079886AA4C5DF806E082C725161,
	ARTrackablesParentTransformChangedEventArgs_op_Equality_m2A48490D3BBECA67708AC521F46C417AB6279CB0,
	ARTrackablesParentTransformChangedEventArgs_op_Inequality_m51EC05AEF49A34A3740CE4EF201ACAA774D0BC1E,
	ReadOnlyAttribute__ctor_m13C3453A8526FBD0C9F45B0F0539CE1208546815,
	ScriptableSettingsPathAttribute_get_Path_mC767AB284DF1262E0EAA6AA36628A4A9035B646F,
	ScriptableSettingsPathAttribute__ctor_mC942C941C139A27C8B05E732EF21E6E2FFF9E808,
	BoundsUtils_GetBounds_m83E66684F6C2CF8B1C875017DE36F7EB23EBEC8B,
	BoundsUtils_GetBounds_m765AE397EEBFBC2BA3E445BFC0634851617D9DB3,
	BoundsUtils_GetBounds_m7363605B59CA26E843B5231C3B044870A5E9E02B,
	BoundsUtils_GetBounds_mFC27EEFA26B5CB8CA64E0BF51DAA9E3FDB7C129E,
	NULL,
	BoundsUtils_GetBounds_m4AF14DACF5FB67E990AC519BF2995811B514C5B4,
	BoundsUtils__cctor_m704AB691443430C18DD4B017724F90FF57CE53E9,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	BoundsExtensions_ContainsCompletely_m6036B79EAF4770CA66300C25D219A25B1431D844,
	CameraExtensions_GetVerticalFieldOfView_m48B2530F8EDA4FC108667A6A61BA2C8863EE1AEF,
	CameraExtensions_GetHorizontalFieldOfView_mD8711BC8586101DA2B389F1E2B8C5E57BEF25661,
	CameraExtensions_GetVerticalOrthographicSize_mB075FA38558BD1C50275EB4757930AE7B2476E9F,
	NULL,
	CollectionExtensions__cctor_m80F18EE99831C527F9AA15118BBF3AE7CDB9C129,
	NULL,
	GameObjectExtensions_SetHideFlagsRecursively_m1E8AEDE5259DFEB22AFA907FF6B2021E483D65B6,
	GameObjectExtensions_AddToHideFlagsRecursively_m8D50F2D030ADB0BE37BFA14D05AD2B1C5CEEE154,
	GameObjectExtensions_SetLayerRecursively_mFDC15B72729883B0FA4D7426A0AD071E2D108392,
	GameObjectExtensions_SetLayerAndAddToHideFlagsRecursively_m45178194447527B4394A7C204AB136A27A731F32,
	GameObjectExtensions_SetLayerAndHideFlagsRecursively_m54894DA97376A955980317A8AA646C67E236C2F6,
	GameObjectExtensions_SetRunInEditModeRecursively_m4326968A10A571DC6C35D8EEBEAE2AB7C4A27017,
	GuidExtensions_Decompose_mF664A6350BD60B57421D4716BC1504967394EDA9,
	NULL,
	NULL,
	LayerMaskExtensions_GetFirstLayerIndex_m7CFD2E8174487008C2C07ABC5D0F004F68B9EEC5,
	LayerMaskExtensions_Contains_mA386A87BF8EA22166EAC8E562FD3E2799A8F3F56,
	NULL,
	NULL,
	NULL,
	PoseExtensions_ApplyOffsetTo_mBDD88056E322E4704BF2213D1482B4C013BFBF52,
	PoseExtensions_ApplyOffsetTo_mC4011BC28A2E9D021B0A245A11937A9FBE79AFFE,
	PoseExtensions_ApplyInverseOffsetTo_m3A6011AD4531543C0F9006A5875047C86DC5CF0C,
	QuaternionExtensions_ConstrainYaw_mBAF7F7ECB5D899A052EB0C809624A1636E19FF5C,
	QuaternionExtensions_ConstrainYawNormalized_m1AE588E327F3E0423429F523BFC6D5D018492D07,
	QuaternionExtensions_ConstrainYawPitchNormalized_m37A79143BA38F44495784288CC715EFC5D3A2BFD,
	StopwatchExtensions_Restart_m69A76149AAC314804F8FCC6BAD06FDE88C98F297,
	StringExtensions_FirstToUpper_m8FCFF701B617938AE8ABD2F547B16DD95F1B1A6C,
	StringExtensions_InsertSpacesBetweenWords_m63FCD5B45B62A86B24B361ACFA57FAB3F5CE3510,
	StringExtensions__cctor_m2F657CD42D0CFAEA7FD40F67293E61FCC108BDFF,
	TransformExtensions_GetLocalPose_m1DE50F03B6211C35AD0445A50626F97771B4321D,
	TransformExtensions_GetWorldPose_m08E343202174666DFF9A07560AB1A72ACD0DDCF1,
	TransformExtensions_SetLocalPose_m15B9FAC87B7B05C750A6D8FD77A0768818CFB7D4,
	TransformExtensions_SetWorldPose_mD1B6C71C60FB650943C8CC11546F26F3F28E4FDE,
	TransformExtensions_TransformPose_m4047A2A74919D9C435751B65FF41503A32611B51,
	TransformExtensions_InverseTransformPose_mA2C52EFE2C80EB0DE7F09EA477B80C583B4387FB,
	TransformExtensions_InverseTransformRay_m27353F07FE4E9F634C8262BB69B0B4487B6CC1A5,
	TypeExtensions_GetAssignableTypes_m7EB47C8D3E37D11462B285D5D16BA664A9B24142,
	TypeExtensions_GetImplementationsOfInterface_m3773A5150ADAE97907C536F6C5BAF3E18234B133,
	TypeExtensions_GetExtensionsOfClass_mF48058E7D4726AA6D22B0A4D91F7F6EC771D8CA8,
	TypeExtensions_GetGenericInterfaces_m7C31E705E43FC289F8F3A7FCE3103B489FE9FEB5,
	TypeExtensions_GetPropertyRecursively_m9D122527E76B2936FD96BDAB1C5ED53AB59BEABE,
	TypeExtensions_GetFieldRecursively_m60F8D95A17428B4431D403DBC2B0FBD6CD6C0CB5,
	TypeExtensions_GetFieldsRecursively_mEF7995AC262D6DA08C78454843299EF22EAF834F,
	TypeExtensions_GetPropertiesRecursively_m72D4D92D9C07C7233B4230EC3586D83B2AE081C1,
	TypeExtensions_GetInterfaceFieldsFromClasses_m1D25F8EE11B324523F825B102EAB956105BCA74E,
	NULL,
	NULL,
	TypeExtensions_GetFieldInTypeOrBaseType_m1EE28449734BB83052CDC44BB1A2BEB0E47B9C2D,
	TypeExtensions_GetNameWithGenericArguments_m29963E46E64626C3E7FF55798199529E6FE4DD24,
	TypeExtensions_GetNameWithFullGenericArguments_m3360311034AD35828677C1D85B20F55FF5B86816,
	TypeExtensions_GetFullNameWithGenericArguments_mEB78A53194DA3D2E56D13A1C96D244698EB3CBCF,
	TypeExtensions_GetFullNameWithGenericArgumentsInternal_mAFB2E37F914668A0596A238E20C75B13A09C043C,
	TypeExtensions_IsAssignableFromOrSubclassOf_mCEF9E345C6B02D5A504EDD1F02A3F04B7538244D,
	TypeExtensions_GetMethodRecursively_mD7AC39EC929889F407E9FAF7F548CF965611D542,
	TypeExtensions__cctor_mD85D49F1687F527C722D5A5261E2B65E2D6D6778,
	U3CU3Ec__DisplayClass2_0__ctor_m503826A926363EA4EAF9A0DE52DA34382B75C925,
	U3CU3Ec__DisplayClass2_0_U3CGetAssignableTypesU3Eb__0_m605F61DF1647ACBAF3D37F9AB38C2478C6BC5E57,
	BoolUnityEvent__ctor_mED39BBF2794EA6795D53400E39F0A0CD03BEA40C,
	FloatUnityEvent__ctor_m5F6D35C2878B13408E44E08A0A10D5D73B4EAEF0,
	Vector2UnityEvent__ctor_mB0AD202040845E7001AA7BC7B27B49E89B500B98,
	Vector3UnityEvent__ctor_m47975F110B4AD23854F23DC5B25708D4467A766E,
	Vector4UnityEvent__ctor_m91DD83DD31DE80A95BFAB40818E70C163B42DD90,
	QuaternionUnityEvent__ctor_m663323B95E33F708F6A67100A8A730C412EE5339,
	IntUnityEvent__ctor_m08120E25F2173E6C0BA993F891C9CFC37C4EC710,
	ColorUnityEvent__ctor_mB2C2672F44208AF4D11C7F1A41DA51BDFA00DAFC,
	StringUnityEvent__ctor_mF1040CBD3090042BA039B8AA8CC0571BEADCB4FD,
	Vector2Extensions_Inverse_mA671F7C6408757B5F528061E1B64416359658255,
	Vector2Extensions_MinComponent_mB5D153E34E2941D9799187978E278D05244ECCFF,
	Vector2Extensions_MaxComponent_mD9485A0D7FF02A1B2CD75CBCC3FCB9143A092BC0,
	Vector2Extensions_Abs_m76A90FEAF17AC5D3E9E827175D8DEBE96E8BAC02,
	Vector3Extensions_Inverse_m7520A342C85FE25830F9FCD01764121F9F430082,
	Vector3Extensions_MinComponent_m488C8EA6E5FA51A7B5D1EAA326C588B7B836A8EC,
	Vector3Extensions_MaxComponent_mFC4E0C3CA7B23E525E73D06294DABD3686988B5F,
	Vector3Extensions_Abs_mD8164FAB218F0866D3636F1F83EA8645AC325A04,
	Vector3Extensions_Multiply_m3B8F1F9AF0FC579579CFFE05DE88A73F6E95247F,
	Vector3Extensions_Divide_m57CFB0B4C8D9C763BC040DB58E177577DC039E0E,
	Vector3Extensions_SafeDivide_mC8342D1EF6C96E254118D0BB443DF85BB6FAC4C8,
	GameObjectUtils_add_GameObjectInstantiated_m29D8568CF7592F3E68BF4955585A67A49234289B,
	GameObjectUtils_remove_GameObjectInstantiated_mCF0A714F9DC6690DB44814BCBC3732FB34A4E308,
	GameObjectUtils_Create_mB0A569525F64CA1916B933FC12D60D3A49F59D23,
	GameObjectUtils_Create_m3F64ABF7C1F438F840AAC72F33825EC306F372EA,
	GameObjectUtils_Instantiate_mAE3F4262B0D2CD08896C8F73E01E27CBAC6FC7CF,
	GameObjectUtils_Instantiate_mEBCC3B8A0DCEF7CBDEDDA6FA091E04FE9D951AEC,
	GameObjectUtils_Instantiate_mCDD665E093028B28576B433C2A2226A5BEE6BD4D,
	GameObjectUtils_CloneWithHideFlags_m68A32C3E4717C4B24CFC90DE1B32FE9F885AF0E9,
	GameObjectUtils_CopyHideFlagsRecursively_mB73BD8658D1502958E2579DA658EAA338FD27EBA,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	GameObjectUtils_GetChildGameObjects_m6BBD356A950CF7C1765EF511D25240BFD9A5C6B0,
	GameObjectUtils_GetNamedChild_m6723E8209E188EDAB22C8B8840F6CAA357EC9FFF,
	GameObjectUtils__cctor_m2F87B5331CC9DA1B89546DC25904CA7876865BA4,
	U3CU3Ec__DisplayClass20_0__ctor_mB1AF49A2B6F9488132A2E8939817133973337254,
	U3CU3Ec__DisplayClass20_0_U3CGetNamedChildU3Eb__0_m7B07F30C4FDAF96F9DF2B6864BF3E6810BDA8271,
	GeometryUtils_FindClosestEdge_m9EA8AA61F92D928592608F2487F42725806E3ABA,
	GeometryUtils_PointOnOppositeSideOfPolygon_mA5DFE4CABEB58E950B407AACE54F9D95F65FDE26,
	GeometryUtils_TriangulatePolygon_mF24897C3EF9FE676012CEA4C90696F64FB52D4B3,
	GeometryUtils_ClosestTimesOnTwoLines_mAB1FEE7E8661BA5B76CBBBF8ABCD5914B0183E65,
	GeometryUtils_ClosestTimesOnTwoLinesXZ_mEC63136A734AFD9C785DED6BDD0EB9344CBCF1EE,
	GeometryUtils_ClosestPointsOnTwoLineSegments_mA5285B20EF9CAAF2A76341E95A2C3E87F0000953,
	GeometryUtils_ClosestPointOnLineSegment_mC69A2DA38485EB78EAC8C08318379AE87D9C766E,
	GeometryUtils_ClosestPolygonApproach_mD0EB2883F064CC7A73484A610EA9BCB9D38FE38A,
	GeometryUtils_PointInPolygon_mCF3A7FF888E68A052AFD7F313AF82A2508B350E8,
	GeometryUtils_PointInPolygon3D_mE7254C77814EEF7920165E26B57587C29B0AC45F,
	GeometryUtils_ProjectPointOnPlane_mE5F8FE1246D84072410216ADF0AEC1F1F864A456,
	GeometryUtils_ConvexHull2D_mE6AC212C34C42BD9F4671E3AC8522A690E7DBD9B,
	GeometryUtils_PolygonCentroid2D_mCC31305CA9AC7A72E2B10A041E0E170568A6D38A,
	GeometryUtils_OrientedMinimumBoundingBox2D_mE8CA374604AE77B405C0E458C2A54B7B06313DCF,
	GeometryUtils_RotateCalipers_mCC4848296208373B34E7381216ECFEB0CCD24C98,
	GeometryUtils_RotationForBox_mCF3B23FB21F160C29B9F929F07B6A34D834DED09,
	GeometryUtils_ConvexPolygonArea_m5F5B0869330E154EB9AF8E43984343C0950B568A,
	GeometryUtils_PolygonInPolygon_m8311330C942DDFE0AD2F400CD74A66F7D223FEB5,
	GeometryUtils_PolygonsWithinRange_mF28F82D6A44CE51CD4C4B947F281DBA1933054C3,
	GeometryUtils_PolygonsWithinSqRange_m0EEF68D575BA63E1AB7FF9664CE1FF8760D1E8CD,
	GeometryUtils_PointOnPolygonBoundsXZ_m3758D9B5C86524D7F5079BE2974D3A3532FBFD52,
	GeometryUtils_PointOnLineSegmentXZ_mB398B28D94695F9B58C980FBF8C73D7EF5DCAE17,
	GeometryUtils_NormalizeRotationKeepingUp_m02F940C2EDF3FBE568AA79A8FD1F3767285AD37B,
	GeometryUtils_PolygonUVPoseFromPlanePose_m812F4601721F468FF33A362C79E05E8A3420B30D,
	GeometryUtils_PolygonVertexToUV_m3AEE8EC1E4ED6F931323614A99C25DFD7789D1B5,
	GeometryUtils__cctor_mC01D372753F14134ED7A7051D6E5A97B9DAAD5E8,
	GuidUtil_Compose_mC92886B7D4DA4351F5DA158136BF059B74E0CA90,
	HashCodeUtil_Combine_mE4BC0854C5A01EAD5211DA2ED260520F97352116,
	HashCodeUtil_ReferenceHash_m8CC95813163E44F2EA0D5C9D111EE0313ABCCCAF,
	HashCodeUtil_Combine_mD279582774DF01FFF0B091155F2712DAE6320DF9,
	HashCodeUtil_Combine_mF5B1742C9C8BDBEFBDEE21388C8ECB278B2B2F21,
	HashCodeUtil_Combine_m4B164B3989B7F01D1E3F81F80547FEFBA79F20E4,
	HashCodeUtil_Combine_mBDA87310F4B4FEAD11A64C0C14326EAF38DAC7F0,
	HashCodeUtil_Combine_m8005317A51D101F7B2D7210B49FC4D598B83861B,
	HashCodeUtil_Combine_m5E2E4249C0A409496B7228E27608F8AA5816359F,
	MaterialUtils_GetMaterialClone_m5ED6EA15ABDC46665A47A80CC2F93EB2E8CB162C,
	MaterialUtils_GetMaterialClone_m00C9BAC027E4AEDE85F2C43FFE558E9D43087FA6,
	MaterialUtils_CloneMaterials_m5066152CE93374F088DB986B30459DA217512861,
	MaterialUtils_HexToColor_mAF1FAE50DEEEA65CBAC0C7B711CB6653F9720352,
	MaterialUtils_HueShift_m51EDC11258726DC003527DA4F14D8AA5E6FC6E07,
	MaterialUtils_AddMaterial_m4C1041B94E9AA972C86EE6C3704935F24581DA4A,
	MathUtility_Approximately_mDB89C82F0E5FC9904A58FD4B2E5111FFEDEA01D1,
	MathUtility_ApproximatelyZero_m4F8E97EAD2F5CCA7D30599B685C9393875E7656D,
	MathUtility_Clamp_mC9BA8E481CDB4149ACB8356CFE8171B66742DBCF,
	MathUtility_ShortestAngleDistance_m66C4E52726C62B97FFB375565965592BD4A283B6,
	MathUtility_ShortestAngleDistance_mE3B22F474CA49275C12E87E85BEE6DF9A3B7CFDC,
	MathUtility_IsUndefined_m66D5C7F65EC97CAF981187F9B7F9A2299C1DFF94,
	MathUtility_IsAxisAligned_m52997CCDEC8B8DBDEE9EF6FA31E0AFA602EC5B91,
	MathUtility_IsPositivePowerOfTwo_m4F9B82704D9E5982B0720F9BA13A77E88D1E4E85,
	MathUtility_FirstActiveFlagIndex_m367DF0B76D971F06D6A3AC1A74EB767577E3A2BC,
	MathUtility__cctor_mF1AF6383C249E65B6B71A6091C2FC393F565B3A3,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	OnDestroyNotifier_get_Destroyed_m089279C60BA06F3BA1D78A8350ECE9882BD77603,
	OnDestroyNotifier_set_Destroyed_m3C67D583F4CB4AD07A5BE88ED107C951E708A2B9,
	OnDestroyNotifier_OnDestroy_m2253AFC37FC6668700F1E10857B730F60567DF90,
	OnDestroyNotifier__ctor_mD07E187557F7C3C49B5DA9056AEED330FDAAF1E8,
	ReflectionUtils_GetCachedAssemblies_mA5F18B04A795FEDC83D8B42E786850C986541337,
	ReflectionUtils_GetCachedTypesPerAssembly_m86DA078F3122A2A8E9C28EE5F4B818F821D3C649,
	ReflectionUtils_GetCachedAssemblyTypeMaps_mF6B13E977E8F3E7C27D8040CEC04963478F2967E,
	ReflectionUtils_PreWarmTypeCache_m4BA9D3049F0CB5E784A89CC4450ECBB852F29460,
	ReflectionUtils_ForEachAssembly_mE6E08D4B8E94239B14E949F0D905356CEF4C750B,
	ReflectionUtils_ForEachType_m12D12BEAE4348C46F8E5AA2E9B49815E52DB8F14,
	ReflectionUtils_FindType_m8EFF03B529AFAAB087D24348207AF3CE8CCDB45D,
	ReflectionUtils_FindTypeByFullName_mDA24A5ADEE610D2DD8D4BC324EE542B863BFCE17,
	ReflectionUtils_FindTypesBatch_mB92E0FF20A248E1235531B1BA230FBA07E3CBD71,
	ReflectionUtils_FindTypesByFullNameBatch_mE1707233668E04E981A3A2A6F3A560145B94908A,
	ReflectionUtils_FindTypeInAssemblyByFullName_mE2BE3F2AF5D0C2FE1B2F53B741B337CD4E3D1F7D,
	ReflectionUtils_NicifyVariableName_mBE793D2C0FC12FA380B12C98F847EEDE93BE1D6D,
	NULL,
	NULL,
	NULL,
	ScriptableSettingsBase_GetInstanceByType_mDFE8821B150DD82581A25676A95EC50B494A835B,
	ScriptableSettingsBase_Awake_mBDD42FA0D36730C98E0E480E8E1C4E711A96B7C6,
	ScriptableSettingsBase_OnEnable_m0157D649A6242B26149407B9B55F14985BCD4BAF,
	ScriptableSettingsBase_OnLoaded_mBB3F1122638C208CA6F9E3EE0D70A86701401563,
	ScriptableSettingsBase_ValidatePath_m6F745ADC48CADCCC7DF26D660CE0DDBB446222BB,
	ScriptableSettingsBase__ctor_mA76719D647AA7F514E58FE1571177FFA8440F558,
	ScriptableSettingsBase__cctor_mEA148991272FE8DE91DF2E77BC06A6C962D3BFAE,
	NULL,
	NULL,
	NULL,
	NULL,
	SerializableGuid_get_Empty_mB5D0E94939D41D74EB9914EC728A5DCCF7F795B5,
	SerializableGuid_get_Guid_m4122C089FF196FE3C90C3EC44B1B6C30A4BBCB49,
	SerializableGuid__ctor_mCB52F194155784E55FC04EEB9A5F086FE6F3EFF2,
	SerializableGuid_GetHashCode_mF9A1263046263FEC8529C736D400C3ADC2280ACA,
	SerializableGuid_Equals_mCE9639B13AA49783B83F2803220A7C839735515D,
	SerializableGuid_ToString_m0575B5BD028C35F5919D6696BD12AB2EDADF1E70,
	SerializableGuid_ToString_m2C4DBE079278618D23A8CD447C60763AC240361E,
	SerializableGuid_ToString_mD57FADE1F89584D3F52212B039B903A5AAC8EAEA,
	SerializableGuid_Equals_m336B71967347FB7BD21D01C6EC949404D0BCAEB9,
	SerializableGuid_op_Equality_mEC5E2250FCD166B0A1F31BCB164FDB464DD52A24,
	SerializableGuid_op_Inequality_m76662FD15C95D7FC5826FAB1F0FA738B67EDC1AE,
	SerializableGuid__cctor_m75645656C914A2FEB9DF0D256193E9DEFC3F9495,
	SerializableGuidUtil_Create_mD7ABDD5139917939FC08E62234ABD598B3463FDE,
	TextureUtils_RenderTextureToTexture2D_mB894DA1E00A62AEB7669F4EEB931E46DCE0B0944,
	UndoBlock__ctor_mDAEDEB99D66092E46FB17AEDF712B864B0414CB3,
	UndoBlock_RegisterCreatedObject_m98FC5C9CF54100F6D841E05D0939FD39E6D8444B,
	UndoBlock_RecordObject_m452F0C62E711962689F7B7749CE464B7B6A3ABA7,
	UndoBlock_SetTransformParent_m99B405AD156DC20AAC3BE18D9168D20A039785EF,
	NULL,
	UndoBlock_Dispose_m2768E03B14C160E718D182B4ED9E50A0F8BA6548,
	UndoBlock_Dispose_mD20DB28590E9B55E8263B57D90A83E6A98E0D0E3,
	UnityObjectUtils_Destroy_mFA9A21EE17656D12F1B126AD9C2CC4EDFD35F157,
	NULL,
	NULL,
	NULL,
	XRLoggingUtils__cctor_mCC38DDF72BDD9B437EE570D7D60F2A8117657A2A,
	XRLoggingUtils_Log_m41CC4C5F97FFCD627CDC49D148C3997AE3683B27,
	XRLoggingUtils_LogWarning_m58F15039BFEC116B7AE4555A526AC7CB9EDABB7A,
	XRLoggingUtils_LogError_mC5522AB4200CD87E6677D2E59C51BA5824070B63,
	XRLoggingUtils_LogException_m17845BE70D3F1732EAA83D29AC5B8581A3279BFD,
	XROrigin_get_Camera_m8959027D616F5BD9AEAE3E41ADEE23BBC2CE3629,
	XROrigin_set_Camera_m4C858ED48CE3A20504A55FAA1A24FE05D1CC450B,
	XROrigin_get_TrackablesParent_m6F7933DF03A5376C31D328F865F77D28EEC18E9C,
	XROrigin_set_TrackablesParent_m2E813980627386E9DE2EA90D39FEEFAF80F31BC5,
	XROrigin_add_TrackablesParentTransformChanged_m04D2A05E3000931435B7F4CAC332E0EC2693B1EF,
	XROrigin_remove_TrackablesParentTransformChanged_m5517FF0B97A3705A7F03F8E42092165C6666163C,
	XROrigin_get_Origin_mCE6A3B327ACE6FAEDCC67A9DC952FEED191C26B6,
	XROrigin_set_Origin_m832CE9176B8C54EDC64059AFC67807EFE078249E,
	XROrigin_get_CameraFloorOffsetObject_m24DB58FD33D0D5436DC3A6F023D8780C1B82FD07,
	XROrigin_set_CameraFloorOffsetObject_m3182CAC8A600DB7EF22432EA3B71BF76A21C4839,
	XROrigin_get_RequestedTrackingOriginMode_m8475634D9A0C8ECA371A3F2EC216A55F7D2F2D3C,
	XROrigin_set_RequestedTrackingOriginMode_m3B166DBAA7C7B18C63EBEA83A308911C094DF554,
	XROrigin_get_CameraYOffset_m223B472CA64A210F0F315A503FF621A6C74EC2A3,
	XROrigin_set_CameraYOffset_mE11AF77FBC8B774E6CED34B10960AC9F747B67D1,
	XROrigin_get_CurrentTrackingOriginMode_m3117576FC85371E692EFFA853AF5297CEF150589,
	XROrigin_set_CurrentTrackingOriginMode_mD2DF2D77407214FFDBED47C114DB0C1348C4F84E,
	XROrigin_get_OriginInCameraSpacePos_mF8CAAA59DDF4635AD3D7B1237B0742AA9BE283E6,
	XROrigin_get_CameraInOriginSpacePos_m6646CE94E1798A767E559EB1D785D00AE8C68EB1,
	XROrigin_get_CameraInOriginSpaceHeight_m1DC15C0A56A969838A827F425ABBED375751BFC5,
	XROrigin_MoveOffsetHeight_m6336FBEAEA9FA0742D0B1740E2316A9CABDAA7AF,
	XROrigin_MoveOffsetHeight_mF0B8B1C8D45F9EF0D2DD9534B443D6EC2A9FC248,
	XROrigin_TryInitializeCamera_mA9C7C0C6C44A0694CDA78FD55D1952E9506717C3,
	XROrigin_SetupCamera_mE5719DCA5F732BE1D7BA0F543C614851A9D43655,
	XROrigin_SetupCamera_mB2D4BC328855A681FAE0D20BB2011E44C98A0E89,
	XROrigin_OnInputSubsystemTrackingOriginUpdated_m1D58DF267E36A73C5C5C5E155284D64D83810FD9,
	XROrigin_RepeatInitializeCamera_mB5CEC27430D87F2017CFD3DAEC8275D68D71F319,
	XROrigin_RotateAroundCameraUsingOriginUp_m42AE0DFCFBA84AC3CBAAB74D78FB1EA361102EA2,
	XROrigin_RotateAroundCameraPosition_m7E496775B85028CDE1EDA5DFEFC36350F371AA59,
	XROrigin_MatchOriginUp_m21E7F97625F9C616B757226DB083A8FE00297D1C,
	XROrigin_MatchOriginUpCameraForward_m8D6A19292733DBEA380BF94DF74A6F9BC33E1F90,
	XROrigin_MatchOriginUpOriginForward_m6BB0CD69861590B4CA6F850D6824A47B37D2D5D3,
	XROrigin_MoveCameraToWorldLocation_m7AA0DF514F9F8E9E68541C314FAB868D043E5B4D,
	XROrigin_Awake_mFC495553BD50D97BCE9BB513A9B2D4C2262B21A0,
	XROrigin_GetCameraOriginPose_m8FBEAA33C27E17B6F461C20C6E53B51E6CDAFFCB,
	XROrigin_OnEnable_mFD45AFA9DDB9FE432E6037D592DCEDBD28DF3ECA,
	XROrigin_OnDisable_m26B6FCDEAC250714CDEAC57039B8ADE42B1B411C,
	XROrigin_OnBeforeRender_m4D292D007D30840EE0BBEE60BB1EF3E1FB3DF018,
	XROrigin_OnValidate_m26B16F4C6A9414A815BDED9E3C3075C9AA2E2AD3,
	XROrigin_Start_m7F0D352F4EBEC07BE7C779E8B2997E6121C2E445,
	XROrigin_OnDestroy_m90EB715086F324C473AA55917913EF5F130ED2DE,
	XROrigin__ctor_mD5AE064CEEF8128D772FABADBA6CDC2993C8079A,
	XROrigin__cctor_m07732DF2A1B8B4A195B2671173B9F89B8DDD776B,
	XROrigin_U3COnValidateU3Eg__IsModeStaleU7C60_0_mB0B1ACE8BD4524258867625EA557440EF4CA5C30,
	U3CRepeatInitializeCameraU3Ed__48__ctor_mEA756E5C40DFD2B29DAAEC516D2C2C4EC21837BA,
	U3CRepeatInitializeCameraU3Ed__48_System_IDisposable_Dispose_m90C7F4D459D9169823A8741D4D8A8C4B7F097E1D,
	U3CRepeatInitializeCameraU3Ed__48_MoveNext_m360FC31D4B39708F2973FBB50495B52C8E07A045,
	U3CRepeatInitializeCameraU3Ed__48_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCD469BF5623FB12496FA276FD3960E2E0C3038F1,
	U3CRepeatInitializeCameraU3Ed__48_System_Collections_IEnumerator_Reset_m5A0B2B0CD539415D4C1A16942B2E63B08DBA95D0,
	U3CRepeatInitializeCameraU3Ed__48_System_Collections_IEnumerator_get_Current_m083AEC9CB984904F6E211766D5A4AAA0A8F17BDF,
	AnimationCurveDatum__ctor_mCB9F5DE7324578D1B0329ED4205F6945ECEFE3E2,
	AnimationCurveDatumProperty__ctor_m6544F46DBCF1D1BAE33AF0E635DE16D0463C9E80,
	AnimationCurveDatumProperty__ctor_mBB9CEA0E13EFFD138543E7542627DE56B8DBEA3D,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	FloatDatum__ctor_m746EBB97665A6285A158D1BC33D0F37A0134250F,
	FloatDatumProperty__ctor_m3DD79AF9D444666BDD91FCF272BF011809878A5A,
	FloatDatumProperty__ctor_m07B133ACD210CD1C65645E9D03C2AE5790FA4321,
	IntDatum_SetValueRounded_m59EC4B8DE575BB14AEBC78D995C093BF49FBFBD1,
	IntDatum__ctor_mD3B338867E8976BB649F98AF4BAB7E9CA5A3F4D6,
	IntDatumProperty__ctor_mAB95877405370ACA469BCEB3BE1058243D53D80C,
	IntDatumProperty__ctor_mBAB490E7375D65C280CF9A5433D7DF1189D51663,
	StringDatum__ctor_m6B3E3BDE5B34B32E826EBC5ECEA6CECB00E29CB8,
	StringDatumProperty__ctor_mDBFDE37F1FC8D1BF0DD1AB8C3ED6D90A1B4169E8,
	StringDatumProperty__ctor_mB4D7997FEAD1E57E83CE2A64163D2D84603A2169,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	CapabilityProfile_add_CapabilityChanged_mAE600078BCC8CE0A86F5F502AE6472E9DA95F69A,
	CapabilityProfile_remove_CapabilityChanged_mFB43EB950CC81EB8F7ECCA71CD3B1DE87230BCA4,
	CapabilityProfile_ReportCapabilityChanged_m982911A7EC7540EAA01DCE63832E20EA85E1BAB7,
	CapabilityProfile__ctor_mC876B490D739A543CCE3681BB4EC06351864C20E,
	CapabilityDictionary_ForceSerialize_m94EB2842F14B8989D3FBBD173B31BF1C48DD2DAC,
	CapabilityDictionary_OnBeforeSerialize_m530D56E1F39A2E17E5E110A070674CCFDB3E13F1,
	CapabilityDictionary__ctor_m149B6D19C0E4B5493FFA729C6A67632BE767DFA4,
	CustomCapabilityKeyAttribute__ctor_mEB750E551167D1BEB548A32E552EE6892E32F812,
	NULL,
	BindingsGroup_AddBinding_m18697E73861A1C67DE0674F162B43CCE9E67C89B,
	BindingsGroup_ClearBinding_m31D78883F22CB8FABEDCBA467F899025844DF8CF,
	BindingsGroup_Bind_mEB5D800FD760BE473E8AE69B2BEE468721DB6689,
	BindingsGroup_Unbind_mB331E12F9A051D5BB4552B8E3EA7680B41DB81B8,
	BindingsGroup_Clear_m092668459F10E46245228224D1334DAB089B72E3,
	BindingsGroup__ctor_m1EFD3F405050571D3A66F9B95E8FBC296866A716,
	EventBinding_get_BindAction_m4EB40C78013954CE11D84F801EEEBDE34BF158A5,
	EventBinding_set_BindAction_mA347945485E3D62DE3724949A23F5B81D772B1B4,
	EventBinding_get_UnbindAction_m15D4D1FB573EB315F412346C20C46A47C0576F24,
	EventBinding_set_UnbindAction_m619D3FBBC839918C46E94BE3E4C4E6224FC88770,
	EventBinding_get_IsBound_mDB69C30373F220F1AEC541228E5489DAA403548C,
	EventBinding__ctor_m8565D0F2D1084AF810D4BE0D5272D366DB102AC4,
	EventBinding_Bind_m4EF06F026C80482430123A70B1F1068EEF90EB1B,
	EventBinding_Unbind_mA991D6B531B6178FB3AA382DE29FD4EA010816F5,
	EventBinding_ClearBinding_mA6B5BF23CDAD914AD7F8C78909786A866E33DCC5,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	EnumDisplayAttribute__ctor_mA3848EC7E676432BB6BF008C27E7E2C2B63A576A,
	FlagsPropertyAttribute__ctor_mB1E45E123C188E357A1FB8D6FA2ADD9182A9D034,
};
extern void ARTrackablesParentTransformChangedEventArgs_get_Origin_m91D7C3638FBF94D468AD4467ABB2EC9500753F25_AdjustorThunk (void);
extern void ARTrackablesParentTransformChangedEventArgs_get_TrackablesParent_m89F1B7B428A07F5142AEC2BF32A83B35B52C0425_AdjustorThunk (void);
extern void ARTrackablesParentTransformChangedEventArgs__ctor_m46B5D18DF81A7296E36E37917E69AB9E748B6278_AdjustorThunk (void);
extern void ARTrackablesParentTransformChangedEventArgs_Equals_m8CAA7BD42F09BF7349818EF3166792652FB9F4AE_AdjustorThunk (void);
extern void ARTrackablesParentTransformChangedEventArgs_Equals_mE6F5F659DD06166ACDDCE62B0652F014568D003B_AdjustorThunk (void);
extern void ARTrackablesParentTransformChangedEventArgs_GetHashCode_mA5FDE9D7D0F5F079886AA4C5DF806E082C725161_AdjustorThunk (void);
extern void SerializableGuid_get_Guid_m4122C089FF196FE3C90C3EC44B1B6C30A4BBCB49_AdjustorThunk (void);
extern void SerializableGuid__ctor_mCB52F194155784E55FC04EEB9A5F086FE6F3EFF2_AdjustorThunk (void);
extern void SerializableGuid_GetHashCode_mF9A1263046263FEC8529C736D400C3ADC2280ACA_AdjustorThunk (void);
extern void SerializableGuid_Equals_mCE9639B13AA49783B83F2803220A7C839735515D_AdjustorThunk (void);
extern void SerializableGuid_ToString_m0575B5BD028C35F5919D6696BD12AB2EDADF1E70_AdjustorThunk (void);
extern void SerializableGuid_ToString_m2C4DBE079278618D23A8CD447C60763AC240361E_AdjustorThunk (void);
extern void SerializableGuid_ToString_mD57FADE1F89584D3F52212B039B903A5AAC8EAEA_AdjustorThunk (void);
extern void SerializableGuid_Equals_m336B71967347FB7BD21D01C6EC949404D0BCAEB9_AdjustorThunk (void);
extern void EventBinding_get_BindAction_m4EB40C78013954CE11D84F801EEEBDE34BF158A5_AdjustorThunk (void);
extern void EventBinding_set_BindAction_mA347945485E3D62DE3724949A23F5B81D772B1B4_AdjustorThunk (void);
extern void EventBinding_get_UnbindAction_m15D4D1FB573EB315F412346C20C46A47C0576F24_AdjustorThunk (void);
extern void EventBinding_set_UnbindAction_m619D3FBBC839918C46E94BE3E4C4E6224FC88770_AdjustorThunk (void);
extern void EventBinding_get_IsBound_mDB69C30373F220F1AEC541228E5489DAA403548C_AdjustorThunk (void);
extern void EventBinding__ctor_m8565D0F2D1084AF810D4BE0D5272D366DB102AC4_AdjustorThunk (void);
extern void EventBinding_Bind_m4EF06F026C80482430123A70B1F1068EEF90EB1B_AdjustorThunk (void);
extern void EventBinding_Unbind_mA991D6B531B6178FB3AA382DE29FD4EA010816F5_AdjustorThunk (void);
extern void EventBinding_ClearBinding_mA6B5BF23CDAD914AD7F8C78909786A866E33DCC5_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[23] = 
{
	{ 0x06000001, ARTrackablesParentTransformChangedEventArgs_get_Origin_m91D7C3638FBF94D468AD4467ABB2EC9500753F25_AdjustorThunk },
	{ 0x06000002, ARTrackablesParentTransformChangedEventArgs_get_TrackablesParent_m89F1B7B428A07F5142AEC2BF32A83B35B52C0425_AdjustorThunk },
	{ 0x06000003, ARTrackablesParentTransformChangedEventArgs__ctor_m46B5D18DF81A7296E36E37917E69AB9E748B6278_AdjustorThunk },
	{ 0x06000004, ARTrackablesParentTransformChangedEventArgs_Equals_m8CAA7BD42F09BF7349818EF3166792652FB9F4AE_AdjustorThunk },
	{ 0x06000005, ARTrackablesParentTransformChangedEventArgs_Equals_mE6F5F659DD06166ACDDCE62B0652F014568D003B_AdjustorThunk },
	{ 0x06000006, ARTrackablesParentTransformChangedEventArgs_GetHashCode_mA5FDE9D7D0F5F079886AA4C5DF806E082C725161_AdjustorThunk },
	{ 0x060000E0, SerializableGuid_get_Guid_m4122C089FF196FE3C90C3EC44B1B6C30A4BBCB49_AdjustorThunk },
	{ 0x060000E1, SerializableGuid__ctor_mCB52F194155784E55FC04EEB9A5F086FE6F3EFF2_AdjustorThunk },
	{ 0x060000E2, SerializableGuid_GetHashCode_mF9A1263046263FEC8529C736D400C3ADC2280ACA_AdjustorThunk },
	{ 0x060000E3, SerializableGuid_Equals_mCE9639B13AA49783B83F2803220A7C839735515D_AdjustorThunk },
	{ 0x060000E4, SerializableGuid_ToString_m0575B5BD028C35F5919D6696BD12AB2EDADF1E70_AdjustorThunk },
	{ 0x060000E5, SerializableGuid_ToString_m2C4DBE079278618D23A8CD447C60763AC240361E_AdjustorThunk },
	{ 0x060000E6, SerializableGuid_ToString_mD57FADE1F89584D3F52212B039B903A5AAC8EAEA_AdjustorThunk },
	{ 0x060000E7, SerializableGuid_Equals_m336B71967347FB7BD21D01C6EC949404D0BCAEB9_AdjustorThunk },
	{ 0x06000181, EventBinding_get_BindAction_m4EB40C78013954CE11D84F801EEEBDE34BF158A5_AdjustorThunk },
	{ 0x06000182, EventBinding_set_BindAction_mA347945485E3D62DE3724949A23F5B81D772B1B4_AdjustorThunk },
	{ 0x06000183, EventBinding_get_UnbindAction_m15D4D1FB573EB315F412346C20C46A47C0576F24_AdjustorThunk },
	{ 0x06000184, EventBinding_set_UnbindAction_m619D3FBBC839918C46E94BE3E4C4E6224FC88770_AdjustorThunk },
	{ 0x06000185, EventBinding_get_IsBound_mDB69C30373F220F1AEC541228E5489DAA403548C_AdjustorThunk },
	{ 0x06000186, EventBinding__ctor_m8565D0F2D1084AF810D4BE0D5272D366DB102AC4_AdjustorThunk },
	{ 0x06000187, EventBinding_Bind_m4EF06F026C80482430123A70B1F1068EEF90EB1B_AdjustorThunk },
	{ 0x06000188, EventBinding_Unbind_mA991D6B531B6178FB3AA382DE29FD4EA010816F5_AdjustorThunk },
	{ 0x06000189, EventBinding_ClearBinding_mA6B5BF23CDAD914AD7F8C78909786A866E33DCC5_AdjustorThunk },
};
static const int32_t s_InvokerIndices[439] = 
{
	6088,
	6088,
	2647,
	3304,
	3416,
	6059,
	8226,
	8226,
	6215,
	6088,
	4880,
	9556,
	9556,
	9556,
	9556,
	0,
	9556,
	10922,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8229,
	8597,
	9897,
	8597,
	0,
	10922,
	0,
	8800,
	8800,
	8800,
	8019,
	8019,
	8792,
	7948,
	0,
	0,
	9691,
	8268,
	0,
	0,
	0,
	8562,
	8675,
	8675,
	9854,
	9854,
	9854,
	10090,
	9804,
	9804,
	10922,
	9836,
	9836,
	8806,
	8806,
	8561,
	8561,
	8569,
	8032,
	8805,
	8805,
	8032,
	7773,
	7773,
	8030,
	8030,
	7424,
	0,
	0,
	8526,
	9804,
	9804,
	9804,
	9804,
	8279,
	7773,
	10922,
	6215,
	4880,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	10057,
	9904,
	9904,
	10057,
	10068,
	9905,
	9905,
	10068,
	8679,
	8679,
	8679,
	10090,
	10090,
	10880,
	9804,
	7772,
	7782,
	7305,
	8526,
	8805,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	8805,
	8526,
	10922,
	6215,
	3416,
	7063,
	8674,
	8017,
	6487,
	6487,
	6487,
	7878,
	6973,
	8335,
	8335,
	7878,
	8279,
	10065,
	8659,
	6375,
	9853,
	9897,
	8279,
	7575,
	7575,
	7594,
	7077,
	9854,
	9837,
	7875,
	10922,
	8385,
	8423,
	9692,
	7659,
	7119,
	6697,
	6550,
	6489,
	6442,
	9804,
	9804,
	9804,
	9602,
	8350,
	8805,
	8298,
	9576,
	7617,
	7082,
	7326,
	9576,
	9583,
	9567,
	9687,
	10922,
	0,
	0,
	0,
	0,
	0,
	6088,
	4880,
	6215,
	6215,
	10880,
	10880,
	10880,
	10922,
	10090,
	10090,
	9804,
	9804,
	8805,
	8805,
	8526,
	9804,
	0,
	0,
	0,
	9804,
	6215,
	6215,
	6215,
	8273,
	6215,
	10922,
	0,
	0,
	0,
	0,
	10906,
	6039,
	2723,
	6059,
	3416,
	6088,
	4312,
	2044,
	3467,
	8297,
	8297,
	10922,
	9886,
	8805,
	2632,
	4880,
	4880,
	2647,
	0,
	4782,
	6215,
	8792,
	0,
	0,
	0,
	10922,
	8805,
	8805,
	8805,
	8805,
	6088,
	4880,
	6088,
	4880,
	4880,
	4880,
	6088,
	4880,
	6088,
	4880,
	6059,
	4853,
	6147,
	4935,
	6059,
	4853,
	6207,
	6207,
	6147,
	6215,
	4935,
	6215,
	5994,
	3416,
	4880,
	6088,
	3469,
	1667,
	3530,
	1668,
	1668,
	3530,
	6215,
	6102,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	10922,
	5994,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	6215,
	4880,
	4880,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6215,
	4935,
	4880,
	4935,
	6215,
	4853,
	4880,
	6215,
	4880,
	4880,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	10090,
	10090,
	6215,
	6215,
	6215,
	6215,
	6215,
	4853,
	0,
	4880,
	4880,
	6215,
	6215,
	6215,
	6215,
	6088,
	4880,
	6088,
	4880,
	5994,
	2647,
	6215,
	6215,
	6215,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	4880,
	6215,
};
static const Il2CppTokenRangePair s_rgctxIndices[44] = 
{
	{ 0x02000008, { 10, 38 } },
	{ 0x02000009, { 53, 10 } },
	{ 0x0200000A, { 63, 7 } },
	{ 0x0200000C, { 73, 3 } },
	{ 0x02000031, { 135, 8 } },
	{ 0x02000034, { 143, 8 } },
	{ 0x02000036, { 151, 3 } },
	{ 0x02000042, { 193, 5 } },
	{ 0x02000043, { 198, 7 } },
	{ 0x0200004A, { 205, 31 } },
	{ 0x0200004B, { 236, 6 } },
	{ 0x0200004C, { 242, 26 } },
	{ 0x02000056, { 268, 5 } },
	{ 0x02000057, { 273, 6 } },
	{ 0x02000058, { 279, 2 } },
	{ 0x02000059, { 281, 28 } },
	{ 0x0200005A, { 309, 4 } },
	{ 0x0200005B, { 313, 15 } },
	{ 0x0200005C, { 328, 14 } },
	{ 0x06000010, { 0, 10 } },
	{ 0x06000016, { 48, 3 } },
	{ 0x06000017, { 51, 2 } },
	{ 0x06000023, { 70, 3 } },
	{ 0x06000029, { 76, 7 } },
	{ 0x0600002B, { 83, 6 } },
	{ 0x06000033, { 89, 8 } },
	{ 0x06000034, { 97, 6 } },
	{ 0x06000037, { 103, 3 } },
	{ 0x06000038, { 106, 3 } },
	{ 0x06000039, { 109, 3 } },
	{ 0x06000054, { 112, 2 } },
	{ 0x06000055, { 114, 1 } },
	{ 0x0600007D, { 115, 3 } },
	{ 0x0600007E, { 118, 4 } },
	{ 0x0600007F, { 122, 2 } },
	{ 0x06000080, { 124, 3 } },
	{ 0x06000081, { 127, 1 } },
	{ 0x06000082, { 128, 1 } },
	{ 0x06000083, { 129, 1 } },
	{ 0x060000BC, { 130, 5 } },
	{ 0x060000F1, { 154, 1 } },
	{ 0x060000F5, { 155, 3 } },
	{ 0x060000F6, { 158, 13 } },
	{ 0x060000F7, { 171, 22 } },
};
extern const uint32_t g_rgctx_List_1_t0A4C576D72FA015EEC445B36DDA0B069D03B44CB;
extern const uint32_t g_rgctx_List_1_get_Count_m37644178964C20177A506C57647E1B5BD74D9E02;
extern const uint32_t g_rgctx_List_1_get_Item_m7BCA07FD30A02307DEBB130FD30BAA7AB3FAB533;
extern const uint32_t g_rgctx_T_t98C9EAD757BE0361B8FC5EDAF954C1BDA2655567;
extern const uint32_t g_rgctx_List_1_GetEnumerator_m0E4052F21A013140E1307EC51954EB049410EC1F;
extern const uint32_t g_rgctx_Enumerator_get_Current_m28BBC6C52F9878B28FC707352CFB66732137FCE3;
extern const uint32_t g_rgctx_Enumerator_tA9A159496BEB14387EF2DBC372C1A274F76705E0;
extern const uint32_t g_rgctx_Enumerator_MoveNext_m63F33C65B9F7DC27B327B912EDFF38E20CF02561;
extern const uint32_t g_rgctx_Enumerator_tA9A159496BEB14387EF2DBC372C1A274F76705E0;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_tA9A159496BEB14387EF2DBC372C1A274F76705E0_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_CollectionPool_2_GetCollection_m8731ED9CB409ABA6653CD9A3C309698FC971BF6B;
extern const uint32_t g_rgctx_CollectionPool_2_t2C0DCF5057B08AC0F5DB722C839462FEF51BFEAD;
extern const uint32_t g_rgctx_CachedComponentFilter_2_t9F8A204056C7ADFA3FEE14185E9809F71EF8A08D;
extern const uint32_t g_rgctx_List_1_t9AF37C3E2C8C126C11D7049DDC09B8E0BBAE9759;
extern const uint32_t g_rgctx_List_1_Clear_mACFA586E5760FDE83B64D12AAEF38CD91A728D3C;
extern const uint32_t g_rgctx_List_1_t61181A9947613C3F281DF80BA625509AA7E74F55;
extern const uint32_t g_rgctx_List_1_Clear_mAB8BDB60AB3CC53A60FD25D85F531BB8CB6D43C6;
extern const uint32_t g_rgctx_TRootType_t910BC4159BB09B9D00748EC9ADB2989D8716C49B;
extern const uint32_t g_rgctx_Component_GetComponents_TisTFilterType_t97AEC65DAC31E6678DDCC177826654855EACF00D_m6E9A3A3028BF0B0CDC8CD78D02ED6C82E826F1EE;
extern const uint32_t g_rgctx_Component_GetComponents_TisIComponentHost_1_tA12DEBF9CE24B4B00569BE0B05ACE7313BE48BE5_m81542E7C98D5FBEF31F86153A195BB0B354B8C8F;
extern const uint32_t g_rgctx_CachedComponentFilter_2_FilteredCopyToMaster_m2386A9F34B100F38467DE115873885ADD89EAD79;
extern const uint32_t g_rgctx_Component_GetComponent_TisTRootType_t910BC4159BB09B9D00748EC9ADB2989D8716C49B_m83293055964329E704ED89A867E25E2106DE8E0A;
extern const uint32_t g_rgctx_Component_GetComponentsInChildren_TisTFilterType_t97AEC65DAC31E6678DDCC177826654855EACF00D_m3109DEB726B3576676D4EC51E97DC8FF8ABCC0F0;
extern const uint32_t g_rgctx_Component_GetComponentsInChildren_TisIComponentHost_1_tA12DEBF9CE24B4B00569BE0B05ACE7313BE48BE5_m29164B92B6E9D1EB5723ABA8B96875CF49C2C3BE;
extern const uint32_t g_rgctx_CachedComponentFilter_2_FilteredCopyToMaster_m7B5B4F055B9931A2C0C2AE0AEC982DFFBD34C3D8;
extern const uint32_t g_rgctx_List_1_AddRange_mC80E41BA137AB9F0EC203DF0F940EC3C82987936;
extern const uint32_t g_rgctx_List_1_GetEnumerator_m679654554DB147BDFCF1E91E617B328F657A893D;
extern const uint32_t g_rgctx_Enumerator_get_Current_m471D625245C210ED91FFF6AEDC3784E3EBD4ABB8;
extern const uint32_t g_rgctx_Enumerator_t7B0C9571E0436FAA1EA471B6C3A06646C63A5D7C;
extern const uint32_t g_rgctx_TFilterType_t97AEC65DAC31E6678DDCC177826654855EACF00D;
extern const uint32_t g_rgctx_Enumerator_MoveNext_m3ABFE069A9E1558B09EEC9C0E2DA8308A51E1947;
extern const uint32_t g_rgctx_Enumerator_t7B0C9571E0436FAA1EA471B6C3A06646C63A5D7C;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_t7B0C9571E0436FAA1EA471B6C3A06646C63A5D7C_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_List_1_GetEnumerator_m7B360F10AE8B6D5ED85E1F16DB3F7ADDE66E8E9C;
extern const uint32_t g_rgctx_Enumerator_get_Current_mE6349DD576C5B011ED61C6823E508CFD29A408C9;
extern const uint32_t g_rgctx_Enumerator_tB79AB7811E4322036E517CFDE47CECF915D264CB;
extern const uint32_t g_rgctx_IComponentHost_1_tA12DEBF9CE24B4B00569BE0B05ACE7313BE48BE5;
extern const uint32_t g_rgctx_IComponentHost_1_get_HostedComponents_m15DDAE0C1C748DC60E690170F3DB1E7D19AC31D4;
extern const uint32_t g_rgctx_Enumerator_MoveNext_m7344872DED954C2545F737B598CB080BC42915C1;
extern const uint32_t g_rgctx_Enumerator_tB79AB7811E4322036E517CFDE47CECF915D264CB;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_tB79AB7811E4322036E517CFDE47CECF915D264CB_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_List_1_Add_mCC8A03F7974CF02D55A967B083314EA97C96B0F0;
extern const uint32_t g_rgctx_Component_GetComponentInParent_TisTRootType_t910BC4159BB09B9D00748EC9ADB2989D8716C49B_m2057D84D1F3A9E019502E52A97CFB6EB63453183;
extern const uint32_t g_rgctx_CollectionPool_2_RecycleCollection_m9B82E22F9FBC27F9A62BF1F5671CD0F0692CD060;
extern const uint32_t g_rgctx_CachedComponentFilter_2_t9F8A204056C7ADFA3FEE14185E9809F71EF8A08D;
extern const uint32_t g_rgctx_CachedComponentFilter_2_Dispose_m6985F60F6DA08FCAA1A8BDCA4175A7BA3BD5AB26;
extern const uint32_t g_rgctx_List_1__ctor_mFC93005A5929DF1A79B7E82E5FD2139B3C31F8CF;
extern const uint32_t g_rgctx_List_1__ctor_m984AD4D9554EDBD0107844807DCE4CD02A034CAE;
extern const uint32_t g_rgctx_TChildType_tFF64304C428176EEF3D952C5BF5B304E6C037C15;
extern const uint32_t g_rgctx_List_1_tA09CE31C69889D2D9E3A38B5F93D6F009435D74C;
extern const uint32_t g_rgctx_List_1_Add_m69CC62EBF39467FE829750C35A541CADE5621ED7;
extern const uint32_t g_rgctx_TChildType_tDB5A61C14076E62C581848EE0EF222299125AB48;
extern const uint32_t g_rgctx_TChildTypeU5BU5D_t6E57A1410F370E60D591E10B591CCD7D8C9CE568;
extern const uint32_t g_rgctx_CollectionPool_2_t68DF8F721A9F6123977FB5B6431DA91C34DFD0BB;
extern const uint32_t g_rgctx_Queue_1_t3D3BF26F8B19065723D95A73E53AD2CAE0AC4B63;
extern const uint32_t g_rgctx_Queue_1_get_Count_m66B2C1C20E7AB4AA180ED67D71C0A287F3FA68FD;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisTCollection_t3FA83A93ECEA0B573846FDDE8363A05B096ED4C9_m1D0F48413E2CD938697A0E1C689C848C7300B970;
extern const uint32_t g_rgctx_Queue_1_Dequeue_m23E76365C777909D787ABB1250CD1FC4A8D38CD0;
extern const uint32_t g_rgctx_TCollection_t3FA83A93ECEA0B573846FDDE8363A05B096ED4C9;
extern const uint32_t g_rgctx_ICollection_1_tE2A5FFDF9E3C3532B4ED69AA4762C8CEF888EFDA;
extern const Il2CppRGCTXConstrainedData g_rgctx_TCollection_t3FA83A93ECEA0B573846FDDE8363A05B096ED4C9_ICollection_1_Clear_m6229909D33A9019717CFE81A4C3C14BA9C88FB94;
extern const uint32_t g_rgctx_Queue_1_Enqueue_m85E59BEC1FAAF18CD2210EDAE91CE7B4B9B18A4C;
extern const uint32_t g_rgctx_Queue_1__ctor_mCB5166A5B4CE3A8BEFDD90E87D4EB107C2F2F241;
extern const uint32_t g_rgctx_ComponentUtils_1_tAA8BF3405715E21D43CFF82D83EB7807F33E3FCC;
extern const uint32_t g_rgctx_GameObject_GetComponents_TisT_tFE377E9CC4A5268EDB3EE76BBD1B6D96EDA722CA_m74D025D8361FDA40C95A23BFE328BAA8DEB002AF;
extern const uint32_t g_rgctx_List_1_t208EB6C1A24250C383C9964B4FEEE5929A530429;
extern const uint32_t g_rgctx_List_1_get_Count_m013C936A0C8FA975ACFA6D40274B9AFAC3324D90;
extern const uint32_t g_rgctx_List_1_get_Item_m43C2FE32C769744AC1B03610DAE93B4AE74B2E53;
extern const uint32_t g_rgctx_GameObject_GetComponentsInChildren_TisT_tFE377E9CC4A5268EDB3EE76BBD1B6D96EDA722CA_m484E91C37A39BBF417EEEB6092F6CD6B6280C22F;
extern const uint32_t g_rgctx_List_1__ctor_m587DBDF688F570E926A3A31F59C250E33B14466F;
extern const uint32_t g_rgctx_GameObject_GetComponent_TisT_t62AE29F7832D5BD762C94868F168E8721B97DEAB_mB2D3CA8ADE589AFFF7511DDC1C1ED6A1981592A0;
extern const uint32_t g_rgctx_T_t62AE29F7832D5BD762C94868F168E8721B97DEAB;
extern const uint32_t g_rgctx_GameObject_AddComponent_TisT_t62AE29F7832D5BD762C94868F168E8721B97DEAB_m639780EA5AFBE3754E8DC2E84BC057C29B8A5ECD;
extern const uint32_t g_rgctx_T_tDF94EB564F474BFE34322C8863A95353618C4056;
extern const uint32_t g_rgctx_TU5BU5D_t51738BF475F285E3A65084DBF8471615191ED234;
extern const uint32_t g_rgctx_EnumValues_1_tB582F27FFA983E40792E29F0E6C6286A981F941F;
extern const uint32_t g_rgctx_ICollection_1_t09FA51C4384724753356D9AF5B1496B95F458C6C;
extern const uint32_t g_rgctx_ICollection_1_get_Count_mB812F6537CF22E060176800A67E990CB94B9FD92;
extern const uint32_t g_rgctx_IEnumerable_1_t7C6C4593DA0500C886902B81CA649EB9CB95FBDC;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_m6A67ECD5EAF42E0020533A6AA6FB636957C708D5;
extern const uint32_t g_rgctx_IEnumerator_1_t6F88365103CBA77D0BAA8A915B0BF9EB7CA480DB;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_m97E8FA59AB0D8F1D5F8BE74DB72546F8F42951BA;
extern const uint32_t g_rgctx_T_t32DD03B24BB35AB1F3E72814C290B3C16528774A;
extern const uint32_t g_rgctx_Dictionary_2_t1CA3FEBDB326DA22BDF686E87AA6B5701BD90DB2;
extern const uint32_t g_rgctx_Dictionary_2_GetEnumerator_m76EC8E266CD3E6645E328E4B6B3B631695ADED19;
extern const uint32_t g_rgctx_Enumerator_MoveNext_mD7E3DC91229F834D543EE7555C31616084D36589;
extern const uint32_t g_rgctx_Enumerator_t06370A3FB2CDF57B2252C104A804F18584928203;
extern const uint32_t g_rgctx_Enumerator_get_Current_m2DB59FFCE6AEFA56B2C9C2D8385F15A3A1457C3A;
extern const uint32_t g_rgctx_Enumerator_Dispose_m5AAFE2649589BB63F4B4CC20279200CF9B8A171E;
extern const uint32_t g_rgctx_HashSet_1_t2637E7FC917777E9AF5615FB0C434A69B2DDA58E;
extern const uint32_t g_rgctx_HashSet_1_GetEnumerator_mFB67B47B114FD894BE2EF9001DEB7B36C5B27133;
extern const uint32_t g_rgctx_Enumerator_get_Current_m5F16111BD6F570C3D34DF5C0F687B16241A76485;
extern const uint32_t g_rgctx_Enumerator_t6EAB881885A85DFC5AC432D8F2073A8783597D3C;
extern const uint32_t g_rgctx_HashSet_1_Remove_mA5A3EF1FD96F5A1B76A609C32D609A014C76993A;
extern const uint32_t g_rgctx_Enumerator_MoveNext_m981A8D17E71D4FFC7F5EE8E09CBAC207B61B03C1;
extern const uint32_t g_rgctx_Enumerator_t6EAB881885A85DFC5AC432D8F2073A8783597D3C;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_t6EAB881885A85DFC5AC432D8F2073A8783597D3C_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_HashSet_1_tA5C7D8278C4D98A65C8B9D39CCB70DA57A6BD1CA;
extern const uint32_t g_rgctx_HashSet_1_GetEnumerator_mED772169DAE31DB0390B293422BCCB0F280B652F;
extern const uint32_t g_rgctx_Enumerator_MoveNext_m4CB24E15141772C937A020BF1614181616153425;
extern const uint32_t g_rgctx_Enumerator_t62588449604C328D3BC039421424DECB73C1C4DC;
extern const uint32_t g_rgctx_Enumerator_get_Current_m51BF001C625F749541D4B69D4380D93451F57918;
extern const uint32_t g_rgctx_Enumerator_Dispose_mBEFC4175648CAD071546AD333DA9145D1778E7D8;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisT_tCAEF24D659CFA94F3B404417E929C1D8BBCEAE32_m1D9507E845DEFF3D9F86E24479C8B2CF7075199F;
extern const uint32_t g_rgctx_List_1_t0320FFF919A7E201F7C33EF925A83D3B25B0476F;
extern const uint32_t g_rgctx_List_1_Add_m7A26EEE2753B73BE36C4BDD66274204E2284DA54;
extern const uint32_t g_rgctx_List_1_tBFB2524062A27158F28773FC3B491ED2D797B955;
extern const uint32_t g_rgctx_List_1_get_Capacity_mAF0500FD97423D50DADC61429337FC7C950E9FF0;
extern const uint32_t g_rgctx_List_1_set_Capacity_m412328013E4A4E6FB162EF7A2379FE16D5CFF6B9;
extern const uint32_t g_rgctx_List_1_t3312455E4796EF5E2B69CCAD2BEB66C0C85E5F06;
extern const uint32_t g_rgctx_List_1_get_Item_m46CFEA86DAA07BDB5E02BE6F70FB8132666B6E9A;
extern const uint32_t g_rgctx_List_1_set_Item_m11EEAB32EE548076D3E5B79C2FEF530562B723C7;
extern const uint32_t g_rgctx_TAttribute_t11E3864BBF093C3DFAB00E272A7EF48902BE8EF1;
extern const uint32_t g_rgctx_TAttribute_t11E3864BBF093C3DFAB00E272A7EF48902BE8EF1;
extern const uint32_t g_rgctx_TAttribute_t97501E0A34B2B83F02C51B19EEB8C2A4094D3D56;
extern const uint32_t g_rgctx_GameObject_GetComponentInChildren_TisT_tEE8BBF0DF2212CCBCAF93DE6860FAEE8952A5050_mF1CBD13B7F70A35D7E94EEFE7BAC986402FD2DE4;
extern const uint32_t g_rgctx_T_tEE8BBF0DF2212CCBCAF93DE6860FAEE8952A5050;
extern const uint32_t g_rgctx_Object_FindObjectOfType_TisT_tEE8BBF0DF2212CCBCAF93DE6860FAEE8952A5050_mAEC86C45DAE7D9E8B66A4E065A55FAB3409EE0DD;
extern const uint32_t g_rgctx_GameObject_GetComponentsInChildren_TisT_t78DD48F25C462AE3AC90D8DFCB75828158D5DAF0_mF745FE6D250E0C5163FFB6073812618FE221E3BB;
extern const uint32_t g_rgctx_T_t78DD48F25C462AE3AC90D8DFCB75828158D5DAF0;
extern const uint32_t g_rgctx_GameObject_GetComponent_TisT_t78DD48F25C462AE3AC90D8DFCB75828158D5DAF0_m9210EDDA3A05DEFA25DE01AF1DD5AD6E78A36FA0;
extern const uint32_t g_rgctx_Object_FindObjectOfType_TisT_t78DD48F25C462AE3AC90D8DFCB75828158D5DAF0_m9F3A4F4056257304956101E22CB60E75F1D4FA36;
extern const uint32_t g_rgctx_GameObject_GetComponentInChildren_TisT_t35E1482DDC73A5CCB07222F99A200D98996AA784_mB4C29DBC5F838F29C8271A6DACC833332635BE9E;
extern const uint32_t g_rgctx_T_t35E1482DDC73A5CCB07222F99A200D98996AA784;
extern const uint32_t g_rgctx_GameObject_GetComponentsInChildren_TisT_t02AB4B3910299221F81070243A2CB1817B7E200E_m0250D2FA77A49DA194785034D3202A0456EE15A6;
extern const uint32_t g_rgctx_List_1_tFEDC503254B883B128AEC09D7731B6E21452C32D;
extern const uint32_t g_rgctx_List_1_AddRange_m064A6C4B562518D1F8D89D59AE52DF9E796D1659;
extern const uint32_t g_rgctx_GameObjectUtils_GetComponentInScene_TisT_tB30B33D71CCB400B7768862AC168FC26869F6B1F_m01AFC3506ECFA6CBBA53CEDC81E989E20428E241;
extern const uint32_t g_rgctx_GameObjectUtils_GetComponentsInScene_TisT_t5FD900F9B89043E43702496D1E18A60845499B92_m7D2B56C6661083C8C138AEC459F38742184CC2B9;
extern const uint32_t g_rgctx_GameObjectUtils_GetComponentsInScene_TisT_t584E06157D7D8F1849D340C32CEBD692E4FB6AF9_m557A2CF3A040A238E40F7C65A26CEEDC3A5E0B8C;
extern const uint32_t g_rgctx_NativeArray_1_t0198D15C15E1C860D921CDF9D0341668F576A514;
extern const uint32_t g_rgctx_NativeArray_1_get_IsCreated_m64D5ED155611D6D73D99F1FA3E1BA2C47C14FA5B;
extern const uint32_t g_rgctx_NativeArray_1_Dispose_mD02FDD6F7600EA3764E02D73DD0C6E20960F2C8B;
extern const uint32_t g_rgctx_NativeArray_1_t0198D15C15E1C860D921CDF9D0341668F576A514;
extern const uint32_t g_rgctx_NativeArray_1__ctor_m45D6DC2A50C19347E777C32BD7670AA621F99C3F;
extern const uint32_t g_rgctx_Queue_1_t13A6260A2FD5C8157AB33D16FA42FCEB72240B06;
extern const uint32_t g_rgctx_Queue_1_get_Count_m098FC3C80525E22BF08BBC7A5DFFAE46B2167907;
extern const uint32_t g_rgctx_Queue_1_Dequeue_m51B578D46AECA36CF999B147DFDEED63CC77016C;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisT_t6817021F4A7EAA4D352F23CF91779E4891FAC71D_mE685A4580CA92B0406A883B9A0ACD5B9CA482DB9;
extern const uint32_t g_rgctx_ObjectPool_1_tD19C2A9E2ED973F7475DA6B78193D5917B0DCE88;
extern const uint32_t g_rgctx_ObjectPool_1_ClearInstance_m2FB8C52A61FCC751DC7F22FA968D7DCBA45DF29F;
extern const uint32_t g_rgctx_Queue_1_Enqueue_mD46AE6D92A1DCC609D60D353D71C80E297BDB8FE;
extern const uint32_t g_rgctx_Queue_1__ctor_m77EF736B080DD9B6616CCD6E44B468CD1DD2D593;
extern const uint32_t g_rgctx_ScriptableSettingsBase_1_tCC1B8E644A002165B9D289E629ACBCC8B5204C02;
extern const uint32_t g_rgctx_T_tD75C904482F31E0C214186BAFACA01AAB4FBF363;
extern const uint32_t g_rgctx_ScriptableSettings_1_CreateAndLoad_m174B694E02F94E8C532DCA12AB3AABE77E2E46D5;
extern const uint32_t g_rgctx_ScriptableSettings_1_tEBDCEA06A56D935B7439A14F8513ACB8C0A9324C;
extern const uint32_t g_rgctx_ScriptableSettingsBase_1_GetFilePath_m880B2C056E15937042F5477667F8EDAA728BB1F4;
extern const uint32_t g_rgctx_ScriptableObject_CreateInstance_TisT_tD75C904482F31E0C214186BAFACA01AAB4FBF363_m382CF5AD8CC05F1172F2C6636C77B11E9183AC92;
extern const uint32_t g_rgctx_ScriptableSettingsBase_1_Save_m579556D0ADDEDD27267C37AFF0E66ACFA44241BE;
extern const uint32_t g_rgctx_ScriptableSettingsBase_1__ctor_m656DFE56F50E42B3AFC0966AF2DB4C079F87C944;
extern const uint32_t g_rgctx_ScriptableSettingsBase_1_t38EEBDA86A8D7AAF469BF841A8F15D5C1B343D6C;
extern const uint32_t g_rgctx_T_t2D4A2C3F601E1DE59938CB9FE4D7055ACC03CB49;
extern const uint32_t g_rgctx_T_t2D4A2C3F601E1DE59938CB9FE4D7055ACC03CB49;
extern const uint32_t g_rgctx_GameObject_AddComponent_TisT_t0573350EC878887841167DA02B13BF9E339E364F_m3CC0FBC349CDAE4079FB63629746D012DABBC633;
extern const uint32_t g_rgctx_T_t7467C6371FA4DFDB0FFE6E2E764E48993306EBD8;
extern const uint32_t g_rgctx_GameObject_GetComponent_TisT_t7467C6371FA4DFDB0FFE6E2E764E48993306EBD8_mA9986D0EDB379CA770BD86803DBDDFFBC5B10E98;
extern const uint32_t g_rgctx_Component_GetComponent_TisT_t7467C6371FA4DFDB0FFE6E2E764E48993306EBD8_m5205C6F27861594C7207149B2FD530B506CD9934;
extern const uint32_t g_rgctx_CollectionPool_2_GetCollection_mD69CCD54CABAD931B3BA7AC86E36DC668C39245A;
extern const uint32_t g_rgctx_CollectionPool_2_tDA8221E9E034384B3086345506F38C4A7A6862D6;
extern const uint32_t g_rgctx_List_1_t82C2434488C5D74B35026906B7C5A3B959599CDA;
extern const uint32_t g_rgctx_List_1_GetEnumerator_m4E2664783B367320FBC9E48D5207CA5BBD107892;
extern const uint32_t g_rgctx_Enumerator_get_Current_m3E3F3926F37EDBDBAF9F7B159DA04D6C58105DD8;
extern const uint32_t g_rgctx_Enumerator_t412F56D8C0FC5AD0C28A48B11682C3C443DB4E80;
extern const uint32_t g_rgctx_T_t68411077569C25C87404DF73BC509CB415AEFAE7;
extern const uint32_t g_rgctx_List_1_Add_mD8B9BE3E7B0D9FF434E4670E5E96475F55250B92;
extern const uint32_t g_rgctx_Enumerator_MoveNext_m181FEA3BF04C8557826105F6C6D9D091D1E1AD48;
extern const uint32_t g_rgctx_Enumerator_t412F56D8C0FC5AD0C28A48B11682C3C443DB4E80;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_t412F56D8C0FC5AD0C28A48B11682C3C443DB4E80_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_List_1_Remove_m95922E3103FA038FF59609FAFB7A3D206B89C837;
extern const uint32_t g_rgctx_CollectionPool_2_RecycleCollection_m14A2828E9BEF2372BFCAE9655F062536DB3867A7;
extern const uint32_t g_rgctx_CollectionPool_2_GetCollection_m765A81B863729C414472651F32193A53B8E49E86;
extern const uint32_t g_rgctx_CollectionPool_2_t07BA497160CA9E978A9D1B9E4B2FE2A4353E747A;
extern const uint32_t g_rgctx_Dictionary_2_tC285B5AD1AFF370AEC488639598662644FA5A89D;
extern const uint32_t g_rgctx_Dictionary_2_GetEnumerator_m47D2784A3AF987DED601665F150D6422736771C5;
extern const uint32_t g_rgctx_Enumerator_get_Current_mDBD466C298E6C70CDB7A4EB77BD74D0EA906C236;
extern const uint32_t g_rgctx_Enumerator_t31B2C3AD126DA00A42645EFCC58D772D19A9BF0A;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Key_m30A3B3CE6F4AAD293C9CDC243E5EB363F008947A;
extern const uint32_t g_rgctx_KeyValuePair_2_tB2DEAE6D3EAA1B2D817444A365B67B3309BCB1C6;
extern const uint32_t g_rgctx_TKey_t79EEFB13E0D8483E9FB33C19B517A7DEBCEB6526;
extern const uint32_t g_rgctx_List_1_tDEE79E42BEA14F2A42EE17579560F4519D187993;
extern const uint32_t g_rgctx_List_1_Add_mD889BB4354595D53BD83DDA7A6E3222DFDFD9173;
extern const uint32_t g_rgctx_Enumerator_MoveNext_mBF1BBE71BB4E92C49FD525BBDEEEBD865A2DE905;
extern const uint32_t g_rgctx_Enumerator_t31B2C3AD126DA00A42645EFCC58D772D19A9BF0A;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_t31B2C3AD126DA00A42645EFCC58D772D19A9BF0A_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_List_1_GetEnumerator_m45A14C654A1DD3B5802746F2DE270DA6CA70ABD4;
extern const uint32_t g_rgctx_Enumerator_get_Current_m6901658742FB9F709DDF553617D42550F778393F;
extern const uint32_t g_rgctx_Enumerator_t5A30F3ED5835EB5D0C878B675C7F8DAC48511FFD;
extern const uint32_t g_rgctx_Dictionary_2_Remove_mF8E09CC605DDFF5CFCC4F4FF25D6E6A0117A33EC;
extern const uint32_t g_rgctx_Enumerator_MoveNext_mEE789A7A68588E5C874AC3730FCF0E03F717A7B9;
extern const uint32_t g_rgctx_Enumerator_t5A30F3ED5835EB5D0C878B675C7F8DAC48511FFD;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_t5A30F3ED5835EB5D0C878B675C7F8DAC48511FFD_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_CollectionPool_2_RecycleCollection_mC06B2C77B645407A1C6640A47E9A20F09090866A;
extern const uint32_t g_rgctx_BindableVariableBase_1_tDA193480D7C716364C6B72AD822606EED48DE215;
extern const uint32_t g_rgctx_BindableVariableBase_1_set_Value_mFD7F5C39C10C9AD7CBB234C2630944E0F3C1074A;
extern const uint32_t g_rgctx_Datum_1_get_Value_m3A82B9E89ADB085543BA469610056F5DDE2C500C;
extern const uint32_t g_rgctx_BindableVariableAlloc_1_t2B7893BB06B6CD3F1CAE351620252C8A146BC0C9;
extern const uint32_t g_rgctx_BindableVariableAlloc_1__ctor_m1062F761A3A042C94B27A453052DDB30E3A643C0;
extern const uint32_t g_rgctx_DatumProperty_2_get_Datum_m437890CDFC443AD61F09F545D584EEE3B70D4845;
extern const uint32_t g_rgctx_Datum_1_t0A2FE67C0725956A9918D7F71A70D08B807D6E20;
extern const uint32_t g_rgctx_Datum_1_get_Value_mD30E8B1538270911DDC128C7F6131368B4835C03;
extern const uint32_t g_rgctx_Datum_1_set_Value_mFDEBE9C1D5A59064BC608717859150EC91EDEDE6;
extern const uint32_t g_rgctx_TDatum_tC2CF7E2DE4BD2184E6690352C3CC7C29FF9CBEAD;
extern const uint32_t g_rgctx_DatumProperty_2_tE1E0F4E97A6C14ADB1C673AB22650BD4F29A5CF2;
extern const uint32_t g_rgctx_DatumProperty_2_get_Value_mA8F9D83C07DED9DD8E8B62AE1AF064C604841E2C;
extern const uint32_t g_rgctx_List_1_t7FEB9B37967F0C9FC5400C483C3821DB2CB398BE;
extern const uint32_t g_rgctx_List_1_get_Count_m3A42C91EDB4D2A2D5D3B4E89C26C1D82D3939F23;
extern const uint32_t g_rgctx_List_1_get_Item_mF716D857CADC789E97025251095DCCFE1B2E4F7D;
extern const uint32_t g_rgctx_List_1__ctor_mEC563D2CF1D963865A53EF28A6467F6688B1BAE4;
extern const uint32_t g_rgctx_HashSet_1_tBDB3E27D16342D273E2F6302CDF3B159BC282D05;
extern const uint32_t g_rgctx_HashSet_1__ctor_m32FCE7C7EC5B95BD486503C409348D4842B442D7;
extern const uint32_t g_rgctx_List_1_GetEnumerator_m968EF1F73B8EC8E289BA4C49DFDA451EDBAA1CAF;
extern const uint32_t g_rgctx_Enumerator_t03E80689F9403B15A8F8DDE5957D116028BA4172;
extern const uint32_t g_rgctx_HashSetList_1_GetEnumerator_mA71915410B30FCBE98280F6F3A096E4928BC49C2;
extern const uint32_t g_rgctx_HashSet_1_Add_m369051318EC0F6D57FC3003A33F63A81BCA7B96D;
extern const uint32_t g_rgctx_List_1_Add_m3708E5EB9AF3E47867BE29295026AA7A10B98356;
extern const uint32_t g_rgctx_HashSet_1_Remove_mAD3864A00F8E7AA1903A0FE2F3331DECB564AD83;
extern const uint32_t g_rgctx_List_1_Remove_m6C052B49F44831C9DF1AA26ECDAAEB1A4AE05505;
extern const uint32_t g_rgctx_HashSet_1_ExceptWith_mB628054E5347C2A5491673F64EAA8F1ECCC74CCF;
extern const uint32_t g_rgctx_HashSetList_1_RefreshList_m65B65E0F6C7E78551A072FE20DC51E9763034E15;
extern const uint32_t g_rgctx_HashSet_1_IntersectWith_m82904D350E91BDF8011D25886737CF1FCA34ED1F;
extern const uint32_t g_rgctx_HashSet_1_IsProperSubsetOf_mE90FFD545CD2E52164B4636F75E392AEC132C5E3;
extern const uint32_t g_rgctx_HashSet_1_IsProperSupersetOf_m29469F0893EE35BEECAA7CCC2F60C99B583EF3C5;
extern const uint32_t g_rgctx_HashSet_1_IsSubsetOf_mF3CACCE5ADDD6E5A7107CDEA633F40A31BB2E67E;
extern const uint32_t g_rgctx_HashSet_1_IsSupersetOf_mA5E1A56C5DA107CEF3E8A08FD6099C53FDF6B680;
extern const uint32_t g_rgctx_HashSet_1_Overlaps_m6538E2E9478E531A033CED36065DFEE8FD9C01DE;
extern const uint32_t g_rgctx_HashSet_1_SetEquals_m5C9D3C334DA6F2797747ADE1759BD2B67C97D5A5;
extern const uint32_t g_rgctx_HashSet_1_SymmetricExceptWith_m9C937233F502762E585662816F6A0B0F5775E7B6;
extern const uint32_t g_rgctx_HashSet_1_UnionWith_mB56C858DBE14BD0D81D816C336572C3170B6C6C1;
extern const uint32_t g_rgctx_HashSet_1_Clear_m9F5ACF5CF7CB1007B5B607895A866747F7283CB8;
extern const uint32_t g_rgctx_List_1_Clear_m0ED5F8945CCD041B1D2AFC362F5105610158B5B4;
extern const uint32_t g_rgctx_HashSet_1_Contains_m300BBF90D9849BA36EA9DFC879FB737D0E0BA51B;
extern const uint32_t g_rgctx_List_1_CopyTo_m62CEAF2C421C7409F42A46F81CA0C45E19A3E234;
extern const uint32_t g_rgctx_HashSet_1_GetObjectData_m8AF8292B1CEA0C171C30FD96B668C2DF5E3249EB;
extern const uint32_t g_rgctx_HashSet_1_OnDeserialization_m13229C3B930DA6D1A6D29272F1757753F696300E;
extern const uint32_t g_rgctx_List_1_AddRange_mEB4A25AECE3F4B00F00CD46D6454DFD6873B265A;
extern const uint32_t g_rgctx_List_1_tA64835FCB012F78DD1D319E8DBB49A3CA03DEA65;
extern const uint32_t g_rgctx_List_1_get_Count_mA42E51CD41B43452788FB19BF9B492AB692EC26B;
extern const uint32_t g_rgctx_List_1_get_Item_m6733373B77986A80C371575BCA157428263FEB35;
extern const uint32_t g_rgctx_List_1_GetEnumerator_m925A71AA988C63393CCDB0AF44C0A97E22A3BC04;
extern const uint32_t g_rgctx_ReadOnlyList_1_GetEnumerator_m6B138576210BB987348BFE51026CE0228EA8B832;
extern const uint32_t g_rgctx_Enumerator_t0F7008DA65E4FCBB0449CDC4793AA4A5FD901E86;
extern const uint32_t g_rgctx_List_1_tDC66CE5F371534B4DB915FCE61E3C7589A034B03;
extern const uint32_t g_rgctx_List_1__ctor_m636725B59E28BA411F437ACCBEFCA778B776EC60;
extern const uint32_t g_rgctx_Dictionary_2__ctor_m4ED0B046D52FF924A56B84C9B7F6F77D290B8A03;
extern const uint32_t g_rgctx_Dictionary_2_t96B5EDFD47F95BA6C75C9FC21A6CEA3146A5B598;
extern const uint32_t g_rgctx_Dictionary_2__ctor_m467401A2EEFAD8823F3D0F2E6484D8BE9A3C3E27;
extern const uint32_t g_rgctx_List_1_Clear_m878443D7A75EC4BAA99DDB93C1036E09DA8C738B;
extern const uint32_t g_rgctx_Dictionary_2_GetEnumerator_m5DEC4B0F0756DF999DA60A7470E8DE7D0FC2C46C;
extern const uint32_t g_rgctx_Enumerator_get_Current_m9206BC64870E4391D8FCB949A9B422082104F6E8;
extern const uint32_t g_rgctx_Enumerator_t4AC4DF90993853A36E15240F540B7AC414FA8EC7;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Key_m556C2CF54B0D7798D391B6060DA6284D26DF7B6F;
extern const uint32_t g_rgctx_KeyValuePair_2_t1EF6161C68A11B492F38FF4B220171486D381738;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Value_m3B26AB2840648192A85460EBADDF36F7173D1354;
extern const uint32_t g_rgctx_List_1_Add_m4ECD861A7800B9C08A5726087C2E4EE1602E545A;
extern const uint32_t g_rgctx_Enumerator_MoveNext_m95C3659038EA7FE2FDBEC2F9FF2471878C6D14B0;
extern const uint32_t g_rgctx_Enumerator_t4AC4DF90993853A36E15240F540B7AC414FA8EC7;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_t4AC4DF90993853A36E15240F540B7AC414FA8EC7_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_Dictionary_2_Clear_mC6E021C78AF5466EE9316FBB548E92D57498B827;
extern const uint32_t g_rgctx_List_1_GetEnumerator_mB61DCE959C2CF9F8C53F2D240DAA3C511F7916D9;
extern const uint32_t g_rgctx_Enumerator_get_Current_m343A7F355236C649D939011CA95AC1563CF9BD75;
extern const uint32_t g_rgctx_Enumerator_t06A383F28E018E711F48D4053C5CAAA1B427D437;
extern const uint32_t g_rgctx_Dictionary_2_ContainsKey_mA69B6A9F2A9CEA5E836227CC26E0B74D0B0B6DA0;
extern const uint32_t g_rgctx_TKey_tFDB0B619013036834AA059601F6DDD846928B92C;
extern const uint32_t g_rgctx_Dictionary_2_Add_m0E97794C7C2721704A090BAFBC6A6ACAF4EAC3F2;
extern const uint32_t g_rgctx_Enumerator_MoveNext_mC7688CBBD325BFE8CF76438AA108593892CF1010;
extern const uint32_t g_rgctx_Enumerator_t06A383F28E018E711F48D4053C5CAAA1B427D437;
extern const Il2CppRGCTXConstrainedData g_rgctx_Enumerator_t06A383F28E018E711F48D4053C5CAAA1B427D437_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_BindableVariableBase_1__ctor_m48F6B0ED243CEA22C00AA917CEC720FB97794FD7;
extern const uint32_t g_rgctx_BindableVariableBase_1_t6C0B42DAFEAF60913BD83DB760B3036C1D3219CC;
extern const uint32_t g_rgctx_BindableVariableBase_1_get_Value_m24227B5287DC8C815FBCA97313FEF26DE62A965B;
extern const uint32_t g_rgctx_T_t2E388FAF242B3B26E38E231F16C36FA4BAE96235;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t2E388FAF242B3B26E38E231F16C36FA4BAE96235_Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C;
extern const uint32_t g_rgctx_BindableVariableBase_1__ctor_m1A208098A988C79D2ACBB47B8908F015CED5B62B;
extern const uint32_t g_rgctx_BindableVariableBase_1_tA4C0C56DCD545B7CDF2C7DC943FCABD72109EEEE;
extern const uint32_t g_rgctx_BindableVariableBase_1_get_Value_mC474CC4DE83C51CF94D94EA80CDA969478DB4703;
extern const uint32_t g_rgctx_T_tFC098A102DB174D034AD22A65B9DEDD73C3D9BA4;
extern const uint32_t g_rgctx_IEquatable_1_t022BAC6F939D7C2C7F64915E2AF4DB41C76C3039;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tFC098A102DB174D034AD22A65B9DEDD73C3D9BA4_IEquatable_1_Equals_m88295AB73E7F268850591FFC6E6D44714CFE465E;
extern const uint32_t g_rgctx_BindableVariableBase_1__ctor_m91AAAB7630483CE7A79994D56B4C174635431E6A;
extern const uint32_t g_rgctx_BindableVariableBase_1_tEEDBB0024C71CF9BE3E7FDF9BC7E5A86BEA3E461;
extern const uint32_t g_rgctx_Action_1_tFD44D2F6F1839A0C531D3406B5E3B764651331E6;
extern const uint32_t g_rgctx_BindableVariableBase_1_SetValueWithoutNotify_m45E6488030C269BDB2E79637CB05573566B93399;
extern const uint32_t g_rgctx_BindableVariableBase_1_BroadcastValue_m974BEEC5F897FA4E54FD2DB215D7C212408998C3;
extern const uint32_t g_rgctx_BindableVariableBase_1_t83A8EF21151A5FEA82CCEAF4D56E772C5A79B14D;
extern const uint32_t g_rgctx_BindableVariableBase_1_ValueEquals_m451F6E8BC7A53BC5ED6A58EFFD56876D5B4F7A1F;
extern const uint32_t g_rgctx_Func_3_t3F404698EEB9900CA4B9833554EAC6758F5121BC;
extern const uint32_t g_rgctx_Func_3_Invoke_m6D0F7A2F2DD8CBB073F3C1F5988E4FCCBECBD220;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass14_0_tF8917E8D194B619C4A10F4C60C081371A1F0E95A;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass14_0__ctor_mA7B1AD2DF56ED10562A037B08ACCC9AF630280DB;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass14_0_U3CSubscribeU3Eb__0_mF26C73A17AC721AAF4E4B2CC74D9027C075203EA;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass14_0_U3CSubscribeU3Eb__1_m2F12A05D15D8429E2DD3280AE3D38363ECC33064;
extern const uint32_t g_rgctx_Action_1_Invoke_mD6E20A4FF380E05A50F69F59CDBC8008B4BF79E8;
extern const uint32_t g_rgctx_BindableVariableBase_1_Subscribe_mA8CE52A3D6CECDCA91038F128D5E6B06FD9D406E;
extern const uint32_t g_rgctx_BindableVariableBase_1_remove_valueUpdated_m557EA62D44C18BCCCDAFB0C7300DE2F95EC71C38;
extern const uint32_t g_rgctx_BindableVariableBase_1_DecrementReferenceCount_m45C30A48A19CC763393585F8231053C7F303D079;
extern const uint32_t g_rgctx_Func_2_tC4FCAE70F08EB2BA956F298ACBE4E6293DDDA244;
extern const uint32_t g_rgctx_Func_2_Invoke_mC2B0B7420352B6342866D8BB0CC2BD9B0A9659E8;
extern const uint32_t g_rgctx_Task_FromResult_TisT_t251EE36B1C491047F9B6D2029DA030A4C355DA9C_mAF8241052625C7053F6211399C917951CDCDD6FA;
extern const uint32_t g_rgctx_BindableVariableTaskPredicate_1_t6C0DA402980C94AE2AA475EA8D8D1A590B57FD00;
extern const uint32_t g_rgctx_BindableVariableTaskPredicate_1__ctor_mDD76E48F060CF469A521623D622D117C0E39BE52;
extern const uint32_t g_rgctx_BindableVariableTaskPredicate_1_get_Task_mB1CC4CF436452888216A9CFF4F7912693E94A5D1;
extern const uint32_t g_rgctx_BindableVariableTaskPredicate_1_t6C0DA402980C94AE2AA475EA8D8D1A590B57FD00;
extern const uint32_t g_rgctx_BindableVariableTaskState_1_t327DA68F192E1FE6F4EAF97D3ABA91D801EA8EA5;
extern const uint32_t g_rgctx_BindableVariableTaskState_1__ctor_mCC59F0863954A98D17F56D654CF07724CD710985;
extern const uint32_t g_rgctx_BindableVariableTaskState_1_get_task_mD4899A64DD04A0DB8006296D716D48E7B0811CF8;
extern const uint32_t g_rgctx_BindableVariableTaskState_1_t327DA68F192E1FE6F4EAF97D3ABA91D801EA8EA5;
extern const uint32_t g_rgctx_T_t251EE36B1C491047F9B6D2029DA030A4C355DA9C;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t251EE36B1C491047F9B6D2029DA030A4C355DA9C_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B;
extern const uint32_t g_rgctx_BindableVariableBase_1_add_valueUpdated_mC281247627F21591EA953B22A3B79949A3BC6E59;
extern const uint32_t g_rgctx_BindableVariableBase_1_IncrementReferenceCount_mE7526464BF4539F7349FC1EBA73985B985547968;
extern const uint32_t g_rgctx_BindableVariableBase_1_remove_valueUpdated_mCA6161E4EC93371ADCE6F62C5BC015DC0F427384;
extern const uint32_t g_rgctx_BindableVariableBase_1_DecrementReferenceCount_m46BC5C9F221EC921C727345FC7D0D59226D5C7D4;
extern const uint32_t g_rgctx_TaskCompletionSource_1_t4A699C6EF0012F843AAE5A1AE2FCB70A6721BC4D;
extern const uint32_t g_rgctx_TaskCompletionSource_1_get_Task_mFE68A7332CB597007FF3BD96395DD668FE5C0DD4;
extern const uint32_t g_rgctx_TaskCompletionSource_1__ctor_mEA8DF39F6B159F574D1195C18A0DE695E84B2070;
extern const uint32_t g_rgctx_IReadOnlyBindableVariable_1_tF6EDF120DA62BEB02A94FF8E76A7C92F8B924A6C;
extern const uint32_t g_rgctx_IReadOnlyBindableVariable_1_get_Value_mE4273F6D1DE6F2AC7E1EB65B4BBD73079011DBFA;
extern const uint32_t g_rgctx_Func_2_tEFE49757D028E6D49E9C674F184E7EE1DFD5ECA2;
extern const uint32_t g_rgctx_Func_2_Invoke_m369C676A493551856A364A3F281CF1307EF747C6;
extern const uint32_t g_rgctx_TaskCompletionSource_1_SetResult_m0D8B69F1A389E5C5C4693D1526165F3FE36D3181;
extern const uint32_t g_rgctx_BindableVariableTaskPredicate_1_t16005EC62ABA492613CADBA33630A6023E4FE581;
extern const uint32_t g_rgctx_BindableVariableTaskPredicate_1_Cancelled_m45D098F1418EE885569C1E2F077B85F2F6ACD115;
extern const uint32_t g_rgctx_BindableVariableTaskPredicate_1_Await_m862E6B438FDE42444DE91C7FDF8AF376510D75BB;
extern const uint32_t g_rgctx_Action_1_t2B262687F05794747D4B274E1B414B765198DC60;
extern const uint32_t g_rgctx_Action_1__ctor_mE0E88B325420B0625EAFF31FB816101468FF7266;
extern const uint32_t g_rgctx_IReadOnlyBindableVariable_1_Subscribe_mAB9735AE5BCB61ADDD5C0F3AD83E242FAE80DCDA;
extern const uint32_t g_rgctx_IReadOnlyBindableVariable_1_Unsubscribe_m7F2DBAB461B06BF0B91E52E94276336A011D5EA8;
extern const uint32_t g_rgctx_TaskCompletionSource_1_t15EDAF8E486A03C0357B7BC3CDCE958FF39A490C;
extern const uint32_t g_rgctx_TaskCompletionSource_1_get_Task_m92C2552905CAFA4FDB28E93628838E6554BFC18D;
extern const uint32_t g_rgctx_TaskCompletionSource_1__ctor_mE47AA374F78236DCC7279ADC3A1BE8ED4E18400A;
extern const uint32_t g_rgctx_IReadOnlyBindableVariable_1_t478F846D1108A58248A12A24580A09179B9AA4BB;
extern const uint32_t g_rgctx_IReadOnlyBindableVariable_1_ValueEquals_m4C4212705FDECA51C53CCE0CF4FACE8E75A24074;
extern const uint32_t g_rgctx_IReadOnlyBindableVariable_1_get_Value_m4B7EB50E3048CFD1BB166155268B0B4A1DF00516;
extern const uint32_t g_rgctx_TaskCompletionSource_1_SetResult_m18739E7C76EB8813C8BC63A02CE1C7895EEC262F;
extern const uint32_t g_rgctx_BindableVariableTaskState_1_t83E3BB5C34A7C8A205F1491415F9541EE2606160;
extern const uint32_t g_rgctx_BindableVariableTaskState_1_Cancelled_mDD4D5FEF413EC2D83DF7326DB0AABBEC93CD586C;
extern const uint32_t g_rgctx_BindableVariableTaskState_1_Await_m766FF5D896DD6D3AFB9E01E2163815A206870ECE;
extern const uint32_t g_rgctx_Action_1_tA641D7BFD03B35023DA38C1370E0B123B7D4B4A1;
extern const uint32_t g_rgctx_Action_1__ctor_m67E497CD20184274AAF20B144859447EF3EEFC0F;
extern const uint32_t g_rgctx_IReadOnlyBindableVariable_1_Subscribe_m2A7F46AC201FC8053ECE38E250D97EDA3EA86FE2;
extern const uint32_t g_rgctx_IReadOnlyBindableVariable_1_Unsubscribe_mE0314627EE7B05ADEF1266675AFFF750D0BEA3FC;
static const Il2CppRGCTXDefinition s_rgctxValues[342] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t0A4C576D72FA015EEC445B36DDA0B069D03B44CB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m37644178964C20177A506C57647E1B5BD74D9E02 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_m7BCA07FD30A02307DEBB130FD30BAA7AB3FAB533 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t98C9EAD757BE0361B8FC5EDAF954C1BDA2655567 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_GetEnumerator_m0E4052F21A013140E1307EC51954EB049410EC1F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m28BBC6C52F9878B28FC707352CFB66732137FCE3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tA9A159496BEB14387EF2DBC372C1A274F76705E0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_m63F33C65B9F7DC27B327B912EDFF38E20CF02561 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tA9A159496BEB14387EF2DBC372C1A274F76705E0 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_tA9A159496BEB14387EF2DBC372C1A274F76705E0_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionPool_2_GetCollection_m8731ED9CB409ABA6653CD9A3C309698FC971BF6B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CollectionPool_2_t2C0DCF5057B08AC0F5DB722C839462FEF51BFEAD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CachedComponentFilter_2_t9F8A204056C7ADFA3FEE14185E9809F71EF8A08D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t9AF37C3E2C8C126C11D7049DDC09B8E0BBAE9759 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_mACFA586E5760FDE83B64D12AAEF38CD91A728D3C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t61181A9947613C3F281DF80BA625509AA7E74F55 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_mAB8BDB60AB3CC53A60FD25D85F531BB8CB6D43C6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TRootType_t910BC4159BB09B9D00748EC9ADB2989D8716C49B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponents_TisTFilterType_t97AEC65DAC31E6678DDCC177826654855EACF00D_m6E9A3A3028BF0B0CDC8CD78D02ED6C82E826F1EE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponents_TisIComponentHost_1_tA12DEBF9CE24B4B00569BE0B05ACE7313BE48BE5_m81542E7C98D5FBEF31F86153A195BB0B354B8C8F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CachedComponentFilter_2_FilteredCopyToMaster_m2386A9F34B100F38467DE115873885ADD89EAD79 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponent_TisTRootType_t910BC4159BB09B9D00748EC9ADB2989D8716C49B_m83293055964329E704ED89A867E25E2106DE8E0A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponentsInChildren_TisTFilterType_t97AEC65DAC31E6678DDCC177826654855EACF00D_m3109DEB726B3576676D4EC51E97DC8FF8ABCC0F0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponentsInChildren_TisIComponentHost_1_tA12DEBF9CE24B4B00569BE0B05ACE7313BE48BE5_m29164B92B6E9D1EB5723ABA8B96875CF49C2C3BE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CachedComponentFilter_2_FilteredCopyToMaster_m7B5B4F055B9931A2C0C2AE0AEC982DFFBD34C3D8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_AddRange_mC80E41BA137AB9F0EC203DF0F940EC3C82987936 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_GetEnumerator_m679654554DB147BDFCF1E91E617B328F657A893D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m471D625245C210ED91FFF6AEDC3784E3EBD4ABB8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t7B0C9571E0436FAA1EA471B6C3A06646C63A5D7C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TFilterType_t97AEC65DAC31E6678DDCC177826654855EACF00D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_m3ABFE069A9E1558B09EEC9C0E2DA8308A51E1947 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t7B0C9571E0436FAA1EA471B6C3A06646C63A5D7C },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_t7B0C9571E0436FAA1EA471B6C3A06646C63A5D7C_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_GetEnumerator_m7B360F10AE8B6D5ED85E1F16DB3F7ADDE66E8E9C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_mE6349DD576C5B011ED61C6823E508CFD29A408C9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tB79AB7811E4322036E517CFDE47CECF915D264CB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IComponentHost_1_tA12DEBF9CE24B4B00569BE0B05ACE7313BE48BE5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IComponentHost_1_get_HostedComponents_m15DDAE0C1C748DC60E690170F3DB1E7D19AC31D4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_m7344872DED954C2545F737B598CB080BC42915C1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tB79AB7811E4322036E517CFDE47CECF915D264CB },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_tB79AB7811E4322036E517CFDE47CECF915D264CB_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_mCC8A03F7974CF02D55A967B083314EA97C96B0F0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponentInParent_TisTRootType_t910BC4159BB09B9D00748EC9ADB2989D8716C49B_m2057D84D1F3A9E019502E52A97CFB6EB63453183 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionPool_2_RecycleCollection_m9B82E22F9FBC27F9A62BF1F5671CD0F0692CD060 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CachedComponentFilter_2_t9F8A204056C7ADFA3FEE14185E9809F71EF8A08D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CachedComponentFilter_2_Dispose_m6985F60F6DA08FCAA1A8BDCA4175A7BA3BD5AB26 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_mFC93005A5929DF1A79B7E82E5FD2139B3C31F8CF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m984AD4D9554EDBD0107844807DCE4CD02A034CAE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TChildType_tFF64304C428176EEF3D952C5BF5B304E6C037C15 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tA09CE31C69889D2D9E3A38B5F93D6F009435D74C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m69CC62EBF39467FE829750C35A541CADE5621ED7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TChildType_tDB5A61C14076E62C581848EE0EF222299125AB48 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TChildTypeU5BU5D_t6E57A1410F370E60D591E10B591CCD7D8C9CE568 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CollectionPool_2_t68DF8F721A9F6123977FB5B6431DA91C34DFD0BB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Queue_1_t3D3BF26F8B19065723D95A73E53AD2CAE0AC4B63 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Queue_1_get_Count_m66B2C1C20E7AB4AA180ED67D71C0A287F3FA68FD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisTCollection_t3FA83A93ECEA0B573846FDDE8363A05B096ED4C9_m1D0F48413E2CD938697A0E1C689C848C7300B970 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Queue_1_Dequeue_m23E76365C777909D787ABB1250CD1FC4A8D38CD0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TCollection_t3FA83A93ECEA0B573846FDDE8363A05B096ED4C9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_tE2A5FFDF9E3C3532B4ED69AA4762C8CEF888EFDA },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TCollection_t3FA83A93ECEA0B573846FDDE8363A05B096ED4C9_ICollection_1_Clear_m6229909D33A9019717CFE81A4C3C14BA9C88FB94 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Queue_1_Enqueue_m85E59BEC1FAAF18CD2210EDAE91CE7B4B9B18A4C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Queue_1__ctor_mCB5166A5B4CE3A8BEFDD90E87D4EB107C2F2F241 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ComponentUtils_1_tAA8BF3405715E21D43CFF82D83EB7807F33E3FCC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponents_TisT_tFE377E9CC4A5268EDB3EE76BBD1B6D96EDA722CA_m74D025D8361FDA40C95A23BFE328BAA8DEB002AF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t208EB6C1A24250C383C9964B4FEEE5929A530429 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m013C936A0C8FA975ACFA6D40274B9AFAC3324D90 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_m43C2FE32C769744AC1B03610DAE93B4AE74B2E53 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponentsInChildren_TisT_tFE377E9CC4A5268EDB3EE76BBD1B6D96EDA722CA_m484E91C37A39BBF417EEEB6092F6CD6B6280C22F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m587DBDF688F570E926A3A31F59C250E33B14466F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponent_TisT_t62AE29F7832D5BD762C94868F168E8721B97DEAB_mB2D3CA8ADE589AFFF7511DDC1C1ED6A1981592A0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t62AE29F7832D5BD762C94868F168E8721B97DEAB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_AddComponent_TisT_t62AE29F7832D5BD762C94868F168E8721B97DEAB_m639780EA5AFBE3754E8DC2E84BC057C29B8A5ECD },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tDF94EB564F474BFE34322C8863A95353618C4056 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t51738BF475F285E3A65084DBF8471615191ED234 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EnumValues_1_tB582F27FFA983E40792E29F0E6C6286A981F941F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ICollection_1_t09FA51C4384724753356D9AF5B1496B95F458C6C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ICollection_1_get_Count_mB812F6537CF22E060176800A67E990CB94B9FD92 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t7C6C4593DA0500C886902B81CA649EB9CB95FBDC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_m6A67ECD5EAF42E0020533A6AA6FB636957C708D5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t6F88365103CBA77D0BAA8A915B0BF9EB7CA480DB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_m97E8FA59AB0D8F1D5F8BE74DB72546F8F42951BA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t32DD03B24BB35AB1F3E72814C290B3C16528774A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t1CA3FEBDB326DA22BDF686E87AA6B5701BD90DB2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_GetEnumerator_m76EC8E266CD3E6645E328E4B6B3B631695ADED19 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_mD7E3DC91229F834D543EE7555C31616084D36589 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t06370A3FB2CDF57B2252C104A804F18584928203 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m2DB59FFCE6AEFA56B2C9C2D8385F15A3A1457C3A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_Dispose_m5AAFE2649589BB63F4B4CC20279200CF9B8A171E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashSet_1_t2637E7FC917777E9AF5615FB0C434A69B2DDA58E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_GetEnumerator_mFB67B47B114FD894BE2EF9001DEB7B36C5B27133 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m5F16111BD6F570C3D34DF5C0F687B16241A76485 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t6EAB881885A85DFC5AC432D8F2073A8783597D3C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_Remove_mA5A3EF1FD96F5A1B76A609C32D609A014C76993A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_m981A8D17E71D4FFC7F5EE8E09CBAC207B61B03C1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t6EAB881885A85DFC5AC432D8F2073A8783597D3C },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_t6EAB881885A85DFC5AC432D8F2073A8783597D3C_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashSet_1_tA5C7D8278C4D98A65C8B9D39CCB70DA57A6BD1CA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_GetEnumerator_mED772169DAE31DB0390B293422BCCB0F280B652F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_m4CB24E15141772C937A020BF1614181616153425 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t62588449604C328D3BC039421424DECB73C1C4DC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m51BF001C625F749541D4B69D4380D93451F57918 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_Dispose_mBEFC4175648CAD071546AD333DA9145D1778E7D8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisT_tCAEF24D659CFA94F3B404417E929C1D8BBCEAE32_m1D9507E845DEFF3D9F86E24479C8B2CF7075199F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t0320FFF919A7E201F7C33EF925A83D3B25B0476F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m7A26EEE2753B73BE36C4BDD66274204E2284DA54 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tBFB2524062A27158F28773FC3B491ED2D797B955 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Capacity_mAF0500FD97423D50DADC61429337FC7C950E9FF0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_set_Capacity_m412328013E4A4E6FB162EF7A2379FE16D5CFF6B9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t3312455E4796EF5E2B69CCAD2BEB66C0C85E5F06 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_m46CFEA86DAA07BDB5E02BE6F70FB8132666B6E9A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_set_Item_m11EEAB32EE548076D3E5B79C2FEF530562B723C7 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TAttribute_t11E3864BBF093C3DFAB00E272A7EF48902BE8EF1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAttribute_t11E3864BBF093C3DFAB00E272A7EF48902BE8EF1 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TAttribute_t97501E0A34B2B83F02C51B19EEB8C2A4094D3D56 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponentInChildren_TisT_tEE8BBF0DF2212CCBCAF93DE6860FAEE8952A5050_mF1CBD13B7F70A35D7E94EEFE7BAC986402FD2DE4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tEE8BBF0DF2212CCBCAF93DE6860FAEE8952A5050 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Object_FindObjectOfType_TisT_tEE8BBF0DF2212CCBCAF93DE6860FAEE8952A5050_mAEC86C45DAE7D9E8B66A4E065A55FAB3409EE0DD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponentsInChildren_TisT_t78DD48F25C462AE3AC90D8DFCB75828158D5DAF0_mF745FE6D250E0C5163FFB6073812618FE221E3BB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t78DD48F25C462AE3AC90D8DFCB75828158D5DAF0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponent_TisT_t78DD48F25C462AE3AC90D8DFCB75828158D5DAF0_m9210EDDA3A05DEFA25DE01AF1DD5AD6E78A36FA0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Object_FindObjectOfType_TisT_t78DD48F25C462AE3AC90D8DFCB75828158D5DAF0_m9F3A4F4056257304956101E22CB60E75F1D4FA36 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponentInChildren_TisT_t35E1482DDC73A5CCB07222F99A200D98996AA784_mB4C29DBC5F838F29C8271A6DACC833332635BE9E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t35E1482DDC73A5CCB07222F99A200D98996AA784 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponentsInChildren_TisT_t02AB4B3910299221F81070243A2CB1817B7E200E_m0250D2FA77A49DA194785034D3202A0456EE15A6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tFEDC503254B883B128AEC09D7731B6E21452C32D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_AddRange_m064A6C4B562518D1F8D89D59AE52DF9E796D1659 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObjectUtils_GetComponentInScene_TisT_tB30B33D71CCB400B7768862AC168FC26869F6B1F_m01AFC3506ECFA6CBBA53CEDC81E989E20428E241 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObjectUtils_GetComponentsInScene_TisT_t5FD900F9B89043E43702496D1E18A60845499B92_m7D2B56C6661083C8C138AEC459F38742184CC2B9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObjectUtils_GetComponentsInScene_TisT_t584E06157D7D8F1849D340C32CEBD692E4FB6AF9_m557A2CF3A040A238E40F7C65A26CEEDC3A5E0B8C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t0198D15C15E1C860D921CDF9D0341668F576A514 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_IsCreated_m64D5ED155611D6D73D99F1FA3E1BA2C47C14FA5B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Dispose_mD02FDD6F7600EA3764E02D73DD0C6E20960F2C8B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t0198D15C15E1C860D921CDF9D0341668F576A514 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1__ctor_m45D6DC2A50C19347E777C32BD7670AA621F99C3F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Queue_1_t13A6260A2FD5C8157AB33D16FA42FCEB72240B06 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Queue_1_get_Count_m098FC3C80525E22BF08BBC7A5DFFAE46B2167907 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Queue_1_Dequeue_m51B578D46AECA36CF999B147DFDEED63CC77016C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisT_t6817021F4A7EAA4D352F23CF91779E4891FAC71D_mE685A4580CA92B0406A883B9A0ACD5B9CA482DB9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ObjectPool_1_tD19C2A9E2ED973F7475DA6B78193D5917B0DCE88 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ObjectPool_1_ClearInstance_m2FB8C52A61FCC751DC7F22FA968D7DCBA45DF29F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Queue_1_Enqueue_mD46AE6D92A1DCC609D60D353D71C80E297BDB8FE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Queue_1__ctor_m77EF736B080DD9B6616CCD6E44B468CD1DD2D593 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ScriptableSettingsBase_1_tCC1B8E644A002165B9D289E629ACBCC8B5204C02 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD75C904482F31E0C214186BAFACA01AAB4FBF363 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ScriptableSettings_1_CreateAndLoad_m174B694E02F94E8C532DCA12AB3AABE77E2E46D5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ScriptableSettings_1_tEBDCEA06A56D935B7439A14F8513ACB8C0A9324C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ScriptableSettingsBase_1_GetFilePath_m880B2C056E15937042F5477667F8EDAA728BB1F4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ScriptableObject_CreateInstance_TisT_tD75C904482F31E0C214186BAFACA01AAB4FBF363_m382CF5AD8CC05F1172F2C6636C77B11E9183AC92 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ScriptableSettingsBase_1_Save_m579556D0ADDEDD27267C37AFF0E66ACFA44241BE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ScriptableSettingsBase_1__ctor_m656DFE56F50E42B3AFC0966AF2DB4C079F87C944 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ScriptableSettingsBase_1_t38EEBDA86A8D7AAF469BF841A8F15D5C1B343D6C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2D4A2C3F601E1DE59938CB9FE4D7055ACC03CB49 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t2D4A2C3F601E1DE59938CB9FE4D7055ACC03CB49 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_AddComponent_TisT_t0573350EC878887841167DA02B13BF9E339E364F_m3CC0FBC349CDAE4079FB63629746D012DABBC633 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7467C6371FA4DFDB0FFE6E2E764E48993306EBD8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponent_TisT_t7467C6371FA4DFDB0FFE6E2E764E48993306EBD8_mA9986D0EDB379CA770BD86803DBDDFFBC5B10E98 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponent_TisT_t7467C6371FA4DFDB0FFE6E2E764E48993306EBD8_m5205C6F27861594C7207149B2FD530B506CD9934 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionPool_2_GetCollection_mD69CCD54CABAD931B3BA7AC86E36DC668C39245A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CollectionPool_2_tDA8221E9E034384B3086345506F38C4A7A6862D6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t82C2434488C5D74B35026906B7C5A3B959599CDA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_GetEnumerator_m4E2664783B367320FBC9E48D5207CA5BBD107892 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m3E3F3926F37EDBDBAF9F7B159DA04D6C58105DD8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t412F56D8C0FC5AD0C28A48B11682C3C443DB4E80 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t68411077569C25C87404DF73BC509CB415AEFAE7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_mD8B9BE3E7B0D9FF434E4670E5E96475F55250B92 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_m181FEA3BF04C8557826105F6C6D9D091D1E1AD48 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t412F56D8C0FC5AD0C28A48B11682C3C443DB4E80 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_t412F56D8C0FC5AD0C28A48B11682C3C443DB4E80_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Remove_m95922E3103FA038FF59609FAFB7A3D206B89C837 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionPool_2_RecycleCollection_m14A2828E9BEF2372BFCAE9655F062536DB3867A7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionPool_2_GetCollection_m765A81B863729C414472651F32193A53B8E49E86 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_CollectionPool_2_t07BA497160CA9E978A9D1B9E4B2FE2A4353E747A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_tC285B5AD1AFF370AEC488639598662644FA5A89D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_GetEnumerator_m47D2784A3AF987DED601665F150D6422736771C5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_mDBD466C298E6C70CDB7A4EB77BD74D0EA906C236 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t31B2C3AD126DA00A42645EFCC58D772D19A9BF0A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Key_m30A3B3CE6F4AAD293C9CDC243E5EB363F008947A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_tB2DEAE6D3EAA1B2D817444A365B67B3309BCB1C6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKey_t79EEFB13E0D8483E9FB33C19B517A7DEBCEB6526 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tDEE79E42BEA14F2A42EE17579560F4519D187993 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_mD889BB4354595D53BD83DDA7A6E3222DFDFD9173 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_mBF1BBE71BB4E92C49FD525BBDEEEBD865A2DE905 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t31B2C3AD126DA00A42645EFCC58D772D19A9BF0A },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_t31B2C3AD126DA00A42645EFCC58D772D19A9BF0A_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_GetEnumerator_m45A14C654A1DD3B5802746F2DE270DA6CA70ABD4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m6901658742FB9F709DDF553617D42550F778393F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t5A30F3ED5835EB5D0C878B675C7F8DAC48511FFD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Remove_mF8E09CC605DDFF5CFCC4F4FF25D6E6A0117A33EC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_mEE789A7A68588E5C874AC3730FCF0E03F717A7B9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t5A30F3ED5835EB5D0C878B675C7F8DAC48511FFD },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_t5A30F3ED5835EB5D0C878B675C7F8DAC48511FFD_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_CollectionPool_2_RecycleCollection_mC06B2C77B645407A1C6640A47E9A20F09090866A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_BindableVariableBase_1_tDA193480D7C716364C6B72AD822606EED48DE215 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableBase_1_set_Value_mFD7F5C39C10C9AD7CBB234C2630944E0F3C1074A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Datum_1_get_Value_m3A82B9E89ADB085543BA469610056F5DDE2C500C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_BindableVariableAlloc_1_t2B7893BB06B6CD3F1CAE351620252C8A146BC0C9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableAlloc_1__ctor_m1062F761A3A042C94B27A453052DDB30E3A643C0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DatumProperty_2_get_Datum_m437890CDFC443AD61F09F545D584EEE3B70D4845 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Datum_1_t0A2FE67C0725956A9918D7F71A70D08B807D6E20 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Datum_1_get_Value_mD30E8B1538270911DDC128C7F6131368B4835C03 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Datum_1_set_Value_mFDEBE9C1D5A59064BC608717859150EC91EDEDE6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TDatum_tC2CF7E2DE4BD2184E6690352C3CC7C29FF9CBEAD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DatumProperty_2_tE1E0F4E97A6C14ADB1C673AB22650BD4F29A5CF2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DatumProperty_2_get_Value_mA8F9D83C07DED9DD8E8B62AE1AF064C604841E2C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t7FEB9B37967F0C9FC5400C483C3821DB2CB398BE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m3A42C91EDB4D2A2D5D3B4E89C26C1D82D3939F23 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_mF716D857CADC789E97025251095DCCFE1B2E4F7D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_mEC563D2CF1D963865A53EF28A6467F6688B1BAE4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashSet_1_tBDB3E27D16342D273E2F6302CDF3B159BC282D05 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1__ctor_m32FCE7C7EC5B95BD486503C409348D4842B442D7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_GetEnumerator_m968EF1F73B8EC8E289BA4C49DFDA451EDBAA1CAF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t03E80689F9403B15A8F8DDE5957D116028BA4172 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSetList_1_GetEnumerator_mA71915410B30FCBE98280F6F3A096E4928BC49C2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_Add_m369051318EC0F6D57FC3003A33F63A81BCA7B96D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m3708E5EB9AF3E47867BE29295026AA7A10B98356 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_Remove_mAD3864A00F8E7AA1903A0FE2F3331DECB564AD83 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Remove_m6C052B49F44831C9DF1AA26ECDAAEB1A4AE05505 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_ExceptWith_mB628054E5347C2A5491673F64EAA8F1ECCC74CCF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSetList_1_RefreshList_m65B65E0F6C7E78551A072FE20DC51E9763034E15 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_IntersectWith_m82904D350E91BDF8011D25886737CF1FCA34ED1F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_IsProperSubsetOf_mE90FFD545CD2E52164B4636F75E392AEC132C5E3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_IsProperSupersetOf_m29469F0893EE35BEECAA7CCC2F60C99B583EF3C5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_IsSubsetOf_mF3CACCE5ADDD6E5A7107CDEA633F40A31BB2E67E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_IsSupersetOf_mA5E1A56C5DA107CEF3E8A08FD6099C53FDF6B680 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_Overlaps_m6538E2E9478E531A033CED36065DFEE8FD9C01DE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_SetEquals_m5C9D3C334DA6F2797747ADE1759BD2B67C97D5A5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_SymmetricExceptWith_m9C937233F502762E585662816F6A0B0F5775E7B6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_UnionWith_mB56C858DBE14BD0D81D816C336572C3170B6C6C1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_Clear_m9F5ACF5CF7CB1007B5B607895A866747F7283CB8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_m0ED5F8945CCD041B1D2AFC362F5105610158B5B4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_Contains_m300BBF90D9849BA36EA9DFC879FB737D0E0BA51B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_CopyTo_m62CEAF2C421C7409F42A46F81CA0C45E19A3E234 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_GetObjectData_m8AF8292B1CEA0C171C30FD96B668C2DF5E3249EB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_OnDeserialization_m13229C3B930DA6D1A6D29272F1757753F696300E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_AddRange_mEB4A25AECE3F4B00F00CD46D6454DFD6873B265A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tA64835FCB012F78DD1D319E8DBB49A3CA03DEA65 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_mA42E51CD41B43452788FB19BF9B492AB692EC26B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_m6733373B77986A80C371575BCA157428263FEB35 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_GetEnumerator_m925A71AA988C63393CCDB0AF44C0A97E22A3BC04 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReadOnlyList_1_GetEnumerator_m6B138576210BB987348BFE51026CE0228EA8B832 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t0F7008DA65E4FCBB0449CDC4793AA4A5FD901E86 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tDC66CE5F371534B4DB915FCE61E3C7589A034B03 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m636725B59E28BA411F437ACCBEFCA778B776EC60 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_m4ED0B046D52FF924A56B84C9B7F6F77D290B8A03 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t96B5EDFD47F95BA6C75C9FC21A6CEA3146A5B598 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_m467401A2EEFAD8823F3D0F2E6484D8BE9A3C3E27 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_m878443D7A75EC4BAA99DDB93C1036E09DA8C738B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_GetEnumerator_m5DEC4B0F0756DF999DA60A7470E8DE7D0FC2C46C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m9206BC64870E4391D8FCB949A9B422082104F6E8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t4AC4DF90993853A36E15240F540B7AC414FA8EC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Key_m556C2CF54B0D7798D391B6060DA6284D26DF7B6F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_t1EF6161C68A11B492F38FF4B220171486D381738 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Value_m3B26AB2840648192A85460EBADDF36F7173D1354 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m4ECD861A7800B9C08A5726087C2E4EE1602E545A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_m95C3659038EA7FE2FDBEC2F9FF2471878C6D14B0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t4AC4DF90993853A36E15240F540B7AC414FA8EC7 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_t4AC4DF90993853A36E15240F540B7AC414FA8EC7_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Clear_mC6E021C78AF5466EE9316FBB548E92D57498B827 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_GetEnumerator_mB61DCE959C2CF9F8C53F2D240DAA3C511F7916D9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_get_Current_m343A7F355236C649D939011CA95AC1563CF9BD75 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t06A383F28E018E711F48D4053C5CAAA1B427D437 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_ContainsKey_mA69B6A9F2A9CEA5E836227CC26E0B74D0B0B6DA0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKey_tFDB0B619013036834AA059601F6DDD846928B92C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Add_m0E97794C7C2721704A090BAFBC6A6ACAF4EAC3F2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator_MoveNext_mC7688CBBD325BFE8CF76438AA108593892CF1010 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t06A383F28E018E711F48D4053C5CAAA1B427D437 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_Enumerator_t06A383F28E018E711F48D4053C5CAAA1B427D437_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableBase_1__ctor_m48F6B0ED243CEA22C00AA917CEC720FB97794FD7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_BindableVariableBase_1_t6C0B42DAFEAF60913BD83DB760B3036C1D3219CC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableBase_1_get_Value_m24227B5287DC8C815FBCA97313FEF26DE62A965B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2E388FAF242B3B26E38E231F16C36FA4BAE96235 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t2E388FAF242B3B26E38E231F16C36FA4BAE96235_Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableBase_1__ctor_m1A208098A988C79D2ACBB47B8908F015CED5B62B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_BindableVariableBase_1_tA4C0C56DCD545B7CDF2C7DC943FCABD72109EEEE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableBase_1_get_Value_mC474CC4DE83C51CF94D94EA80CDA969478DB4703 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFC098A102DB174D034AD22A65B9DEDD73C3D9BA4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEquatable_1_t022BAC6F939D7C2C7F64915E2AF4DB41C76C3039 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tFC098A102DB174D034AD22A65B9DEDD73C3D9BA4_IEquatable_1_Equals_m88295AB73E7F268850591FFC6E6D44714CFE465E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableBase_1__ctor_m91AAAB7630483CE7A79994D56B4C174635431E6A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_BindableVariableBase_1_tEEDBB0024C71CF9BE3E7FDF9BC7E5A86BEA3E461 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tFD44D2F6F1839A0C531D3406B5E3B764651331E6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableBase_1_SetValueWithoutNotify_m45E6488030C269BDB2E79637CB05573566B93399 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableBase_1_BroadcastValue_m974BEEC5F897FA4E54FD2DB215D7C212408998C3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_BindableVariableBase_1_t83A8EF21151A5FEA82CCEAF4D56E772C5A79B14D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableBase_1_ValueEquals_m451F6E8BC7A53BC5ED6A58EFFD56876D5B4F7A1F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_3_t3F404698EEB9900CA4B9833554EAC6758F5121BC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_3_Invoke_m6D0F7A2F2DD8CBB073F3C1F5988E4FCCBECBD220 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass14_0_tF8917E8D194B619C4A10F4C60C081371A1F0E95A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass14_0__ctor_mA7B1AD2DF56ED10562A037B08ACCC9AF630280DB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass14_0_U3CSubscribeU3Eb__0_mF26C73A17AC721AAF4E4B2CC74D9027C075203EA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass14_0_U3CSubscribeU3Eb__1_m2F12A05D15D8429E2DD3280AE3D38363ECC33064 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_mD6E20A4FF380E05A50F69F59CDBC8008B4BF79E8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableBase_1_Subscribe_mA8CE52A3D6CECDCA91038F128D5E6B06FD9D406E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableBase_1_remove_valueUpdated_m557EA62D44C18BCCCDAFB0C7300DE2F95EC71C38 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableBase_1_DecrementReferenceCount_m45C30A48A19CC763393585F8231053C7F303D079 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tC4FCAE70F08EB2BA956F298ACBE4E6293DDDA244 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_mC2B0B7420352B6342866D8BB0CC2BD9B0A9659E8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Task_FromResult_TisT_t251EE36B1C491047F9B6D2029DA030A4C355DA9C_mAF8241052625C7053F6211399C917951CDCDD6FA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_BindableVariableTaskPredicate_1_t6C0DA402980C94AE2AA475EA8D8D1A590B57FD00 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableTaskPredicate_1__ctor_mDD76E48F060CF469A521623D622D117C0E39BE52 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableTaskPredicate_1_get_Task_mB1CC4CF436452888216A9CFF4F7912693E94A5D1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_BindableVariableTaskPredicate_1_t6C0DA402980C94AE2AA475EA8D8D1A590B57FD00 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_BindableVariableTaskState_1_t327DA68F192E1FE6F4EAF97D3ABA91D801EA8EA5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableTaskState_1__ctor_mCC59F0863954A98D17F56D654CF07724CD710985 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableTaskState_1_get_task_mD4899A64DD04A0DB8006296D716D48E7B0811CF8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_BindableVariableTaskState_1_t327DA68F192E1FE6F4EAF97D3ABA91D801EA8EA5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t251EE36B1C491047F9B6D2029DA030A4C355DA9C },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t251EE36B1C491047F9B6D2029DA030A4C355DA9C_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableBase_1_add_valueUpdated_mC281247627F21591EA953B22A3B79949A3BC6E59 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableBase_1_IncrementReferenceCount_mE7526464BF4539F7349FC1EBA73985B985547968 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableBase_1_remove_valueUpdated_mCA6161E4EC93371ADCE6F62C5BC015DC0F427384 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableBase_1_DecrementReferenceCount_m46BC5C9F221EC921C727345FC7D0D59226D5C7D4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskCompletionSource_1_t4A699C6EF0012F843AAE5A1AE2FCB70A6721BC4D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskCompletionSource_1_get_Task_mFE68A7332CB597007FF3BD96395DD668FE5C0DD4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskCompletionSource_1__ctor_mEA8DF39F6B159F574D1195C18A0DE695E84B2070 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IReadOnlyBindableVariable_1_tF6EDF120DA62BEB02A94FF8E76A7C92F8B924A6C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IReadOnlyBindableVariable_1_get_Value_mE4273F6D1DE6F2AC7E1EB65B4BBD73079011DBFA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tEFE49757D028E6D49E9C674F184E7EE1DFD5ECA2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_m369C676A493551856A364A3F281CF1307EF747C6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskCompletionSource_1_SetResult_m0D8B69F1A389E5C5C4693D1526165F3FE36D3181 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_BindableVariableTaskPredicate_1_t16005EC62ABA492613CADBA33630A6023E4FE581 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableTaskPredicate_1_Cancelled_m45D098F1418EE885569C1E2F077B85F2F6ACD115 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableTaskPredicate_1_Await_m862E6B438FDE42444DE91C7FDF8AF376510D75BB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t2B262687F05794747D4B274E1B414B765198DC60 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1__ctor_mE0E88B325420B0625EAFF31FB816101468FF7266 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IReadOnlyBindableVariable_1_Subscribe_mAB9735AE5BCB61ADDD5C0F3AD83E242FAE80DCDA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IReadOnlyBindableVariable_1_Unsubscribe_m7F2DBAB461B06BF0B91E52E94276336A011D5EA8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskCompletionSource_1_t15EDAF8E486A03C0357B7BC3CDCE958FF39A490C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskCompletionSource_1_get_Task_m92C2552905CAFA4FDB28E93628838E6554BFC18D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskCompletionSource_1__ctor_mE47AA374F78236DCC7279ADC3A1BE8ED4E18400A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IReadOnlyBindableVariable_1_t478F846D1108A58248A12A24580A09179B9AA4BB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IReadOnlyBindableVariable_1_ValueEquals_m4C4212705FDECA51C53CCE0CF4FACE8E75A24074 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IReadOnlyBindableVariable_1_get_Value_m4B7EB50E3048CFD1BB166155268B0B4A1DF00516 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskCompletionSource_1_SetResult_m18739E7C76EB8813C8BC63A02CE1C7895EEC262F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_BindableVariableTaskState_1_t83E3BB5C34A7C8A205F1491415F9541EE2606160 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableTaskState_1_Cancelled_mDD4D5FEF413EC2D83DF7326DB0AABBEC93CD586C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BindableVariableTaskState_1_Await_m766FF5D896DD6D3AFB9E01E2163815A206870ECE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tA641D7BFD03B35023DA38C1370E0B123B7D4B4A1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1__ctor_m67E497CD20184274AAF20B144859447EF3EEFC0F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IReadOnlyBindableVariable_1_Subscribe_m2A7F46AC201FC8053ECE38E250D97EDA3EA86FE2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IReadOnlyBindableVariable_1_Unsubscribe_mE0314627EE7B05ADEF1266675AFFF750D0BEA3FC },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_XR_CoreUtils_CodeGenModule;
const Il2CppCodeGenModule g_Unity_XR_CoreUtils_CodeGenModule = 
{
	"Unity.XR.CoreUtils.dll",
	439,
	s_methodPointers,
	23,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	44,
	s_rgctxIndices,
	342,
	s_rgctxValues,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
