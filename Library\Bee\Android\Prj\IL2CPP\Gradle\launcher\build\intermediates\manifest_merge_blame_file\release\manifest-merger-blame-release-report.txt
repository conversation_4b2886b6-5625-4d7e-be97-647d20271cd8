1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.DefaultCompany.VRAssembly"
4    android:installLocation="preferExternal"
5    android:versionCode="1"
6    android:versionName="0.1" >
7
8    <uses-sdk
9        android:minSdkVersion="29"
9-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
10        android:targetSdkVersion="29" />
10-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
11
12    <supports-screens
12-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:3-163
13        android:anyDensity="true"
13-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:135-160
14        android:largeScreens="true"
14-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:78-105
15        android:normalScreens="true"
15-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:49-77
16        android:smallScreens="true"
16-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:21-48
17        android:xlargeScreens="true" />
17-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:106-134
18
19    <uses-feature android:glEsVersion="0x00030000" />
19-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:5-54
19-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:19-51
20    <uses-feature
20-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:5-11:36
21        android:name="android.hardware.vulkan.version"
21-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:10:9-55
22        android:required="false" />
22-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:9-33
23
24    <supports-gl-texture android:name="GL_KHR_texture_compression_astc_ldr" />
24-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:5-79
24-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:26-76
25
26    <uses-permission android:name="android.permission.INTERNET" />
26-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:15:5-67
26-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:15:22-64
27
28    <uses-feature
28-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:17:5-19:36
29        android:name="android.hardware.touchscreen"
29-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:18:9-52
30        android:required="false" />
30-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:19:9-33
31    <uses-feature
31-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:20:5-22:36
32        android:name="android.hardware.touchscreen.multitouch"
32-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:21:9-63
33        android:required="false" />
33-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:22:9-33
34    <uses-feature
34-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:23:5-25:36
35        android:name="android.hardware.touchscreen.multitouch.distinct"
35-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:24:9-72
36        android:required="false" />
36-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:25:9-33
37
38    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
38-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:27:5-73
38-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:27:22-70
39    <!--
40  Copyright (c) 2020-2022, The Khronos Group Inc.
41  SPDX-License-Identifier: Apache-2.0
42    -->
43    <uses-permission android:name="org.khronos.openxr.permission.OPENXR" />
43-->[:loader-1.0.5.ForUnitySDK:] C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\AndroidManifest.xml:13:5-76
43-->[:loader-1.0.5.ForUnitySDK:] C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\AndroidManifest.xml:13:22-73
44    <uses-permission android:name="org.khronos.openxr.permission.OPENXR_SYSTEM" />
44-->[:loader-1.0.5.ForUnitySDK:] C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\AndroidManifest.xml:14:5-83
44-->[:loader-1.0.5.ForUnitySDK:] C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\AndroidManifest.xml:14:22-80
45
46    <queries>
46-->[:loader-1.0.5.ForUnitySDK:] C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\AndroidManifest.xml:16:5-18:15
47        <provider android:authorities="org.khronos.openxr.runtime_broker;org.khronos.openxr.system_runtime_broker" />
47-->[:loader-1.0.5.ForUnitySDK:] C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\AndroidManifest.xml:17:9-118
47-->[:loader-1.0.5.ForUnitySDK:] C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\AndroidManifest.xml:17:19-115
48    </queries>
49
50    <uses-permission android:name="com.pvr.tobactivate.permission.AUTH_CHECK" />
50-->[:BAuthLib-1.0.0:] C:\Users\<USER>\.gradle\caches\transforms-3\2de471c9c2cbf5697eeff1427be18f9f\transformed\jetified-BAuthLib-1.0.0\AndroidManifest.xml:11:5-81
50-->[:BAuthLib-1.0.0:] C:\Users\<USER>\.gradle\caches\transforms-3\2de471c9c2cbf5697eeff1427be18f9f\transformed\jetified-BAuthLib-1.0.0\AndroidManifest.xml:11:22-78
51    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
52    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
53    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
54
55    <application
55-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:3-83
56        android:extractNativeLibs="true"
56-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:30:9-41
57        android:icon="@mipmap/app_icon"
57-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:49-80
58        android:label="@string/app_name"
58-->D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:16-48
59        android:requestLegacyExternalStorage="true" >
59-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:31:9-52
60        <activity
60-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:32:9-52:20
61            android:name="com.unity3d.player.UnityPlayerActivity"
61-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:33:13-66
62            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
62-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:34:13-194
63            android:hardwareAccelerated="false"
63-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:35:13-48
64            android:launchMode="singleTask"
64-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:36:13-44
65            android:resizeableActivity="false"
65-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:37:13-47
66            android:screenOrientation="fullUser"
66-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:38:13-49
67            android:theme="@style/UnityThemeSelector" >
67-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:39:13-54
68            <intent-filter>
68-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:40:13-44:29
69                <action android:name="android.intent.action.MAIN" />
69-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:41:17-69
69-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:41:25-66
70
71                <category android:name="android.intent.category.LAUNCHER" />
71-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:43:17-77
71-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:43:27-74
72            </intent-filter>
73
74            <meta-data
74-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:46:13-48:40
75                android:name="unityplayer.UnityActivity"
75-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:47:17-57
76                android:value="true" />
76-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:48:17-37
77            <meta-data
77-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:49:13-51:40
78                android:name="android.notch_support"
78-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:50:17-53
79                android:value="true" />
79-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:51:17-37
80        </activity>
81
82        <meta-data
82-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:54:9-56:33
83            android:name="unity.splash-mode"
83-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:55:13-45
84            android:value="0" />
84-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:56:13-30
85        <meta-data
85-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:57:9-59:36
86            android:name="unity.splash-enable"
86-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:58:13-47
87            android:value="True" />
87-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:59:13-33
88        <meta-data
88-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:60:9-62:36
89            android:name="unity.launch-fullscreen"
89-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:61:13-51
90            android:value="True" />
90-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:62:13-33
91        <meta-data
91-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:63:9-65:50
92            android:name="notch.config"
92-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:64:13-40
93            android:value="portrait|landscape" />
93-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:65:13-47
94        <meta-data
94-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:66:9-68:36
95            android:name="unity.auto-report-fully-drawn"
95-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:67:13-57
96            android:value="true" />
96-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:68:13-33
97        <meta-data
97-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:69:9-71:34
98            android:name="pvr.app.type"
98-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:70:13-40
99            android:value="vr" />
99-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:71:13-31
100        <meta-data
100-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:72:9-74:36
101            android:name="pxr.sdk.version_code"
101-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:73:13-48
102            android:value="5130" />
102-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:74:13-33
103        <meta-data
103-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:75:9-77:49
104            android:name="pvr.sdk.version"
104-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:76:13-43
105            android:value="XR Platform_3.2.4" />
105-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:77:13-46
106        <meta-data
106-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:78:9-80:33
107            android:name="enable_cpt"
107-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:79:13-38
108            android:value="0" />
108-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:80:13-30
109        <meta-data
109-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:81:9-83:33
110            android:name="Enable_AdaptiveHandModel"
110-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:82:13-52
111            android:value="0" />
111-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:83:13-30
112        <meta-data
112-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:84:9-86:33
113            android:name="Hand_Tracking_HighFrequency"
113-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:85:13-55
114            android:value="0" />
114-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:86:13-30
115        <meta-data
115-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:87:9-89:33
116            android:name="rendering_mode"
116-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:88:13-42
117            android:value="0" />
117-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:89:13-30
118        <meta-data
118-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:90:9-92:33
119            android:name="display_rate"
119-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:91:13-40
120            android:value="0" />
120-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:92:13-30
121        <meta-data
121-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:93:9-95:33
122            android:name="color_Space"
122-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:94:13-39
123            android:value="1" />
123-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:95:13-30
124        <meta-data
124-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:96:9-98:33
125            android:name="MRCsupport"
125-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:97:13-38
126            android:value="1" />
126-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:98:13-30
127        <meta-data
127-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:99:9-101:33
128            android:name="pvr.LateLatching"
128-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:100:13-44
129            android:value="0" />
129-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:101:13-30
130        <meta-data
130-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:102:9-104:33
131            android:name="pvr.LateLatchingDebug"
131-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:103:13-49
132            android:value="0" />
132-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:104:13-30
133        <meta-data
133-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:105:9-107:33
134            android:name="pvr.app.splash"
134-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:106:13-42
135            android:value="0" />
135-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:107:13-30
136        <meta-data
136-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:108:9-110:33
137            android:name="PICO.swift.feature"
137-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:109:13-46
138            android:value="0" />
138-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:110:13-30
139        <meta-data
139-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:111:9-113:33
140            android:name="adaptive_resolution"
140-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:112:13-47
141            android:value="0" />
141-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:113:13-30
142        <meta-data
142-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:114:9-116:33
143            android:name="enable_mr_safeguard"
143-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:115:13-47
144            android:value="0" />
144-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:116:13-30
145        <meta-data
145-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:117:9-119:33
146            android:name="enable_vst"
146-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:118:13-38
147            android:value="0" />
147-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:119:13-30
148        <meta-data
148-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:120:9-122:33
149            android:name="enable_anchor"
149-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:121:13-41
150            android:value="0" />
150-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:122:13-30
151        <meta-data
151-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:123:9-125:33
152            android:name="mr_map_mgr_auto_start"
152-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:124:13-49
153            android:value="0" />
153-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:125:13-30
154        <meta-data
154-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:126:9-128:33
155            android:name="enable_spatial_anchor"
155-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:127:13-49
156            android:value="0" />
156-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:128:13-30
157        <meta-data
157-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:129:9-131:33
158            android:name="enable_cloud_anchor"
158-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:130:13-47
159            android:value="0" />
159-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:131:13-30
160        <meta-data
160-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:132:9-134:33
161            android:name="enable_mesh_anchor"
161-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:133:13-46
162            android:value="0" />
162-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:134:13-30
163        <meta-data
163-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:135:9-137:33
164            android:name="enable_scene_anchor"
164-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:136:13-47
165            android:value="0" />
165-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:137:13-30
166        <meta-data
166-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:138:9-140:33
167            android:name="pvr.SuperResolution"
167-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:139:13-47
168            android:value="0" />
168-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:140:13-30
169        <meta-data
169-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:141:9-143:33
170            android:name="pvr.NormalSharpening"
170-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:142:13-48
171            android:value="0" />
171-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:143:13-30
172        <meta-data
172-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:144:9-146:33
173            android:name="pvr.QualitySharpening"
173-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:145:13-49
174            android:value="0" />
174-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:146:13-30
175        <meta-data
175-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:147:9-149:33
176            android:name="pvr.FixedFoveatedSharpening"
176-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:148:13-55
177            android:value="0" />
177-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:149:13-30
178        <meta-data
178-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:150:9-152:33
179            android:name="pvr.SelfAdaptiveSharpening"
179-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:151:13-54
180            android:value="0" />
180-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:152:13-30
181        <meta-data
181-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:153:9-155:33
182            android:name="pvr.app.secure_mr"
182-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:154:13-45
183            android:value="0" />
183-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:155:13-30
184        <meta-data
184-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:156:9-158:33
185            android:name="controller"
185-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:157:13-38
186            android:value="1" />
186-->[:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:158:13-30
187    </application>
188
189</manifest>
