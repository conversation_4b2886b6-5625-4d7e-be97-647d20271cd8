<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.DefaultCompany.VRAssembly"
    android:installLocation="preferExternal"
    android:versionCode="1"
    android:versionName="0.1" >

    <uses-sdk
        android:minSdkVersion="29"
        android:targetSdkVersion="29" />

    <supports-screens
        android:anyDensity="true"
        android:largeScreens="true"
        android:normalScreens="true"
        android:smallScreens="true"
        android:xlargeScreens="true" />

    <uses-feature android:glEsVersion="0x00030000" />
    <uses-feature
        android:name="android.hardware.vulkan.version"
        android:required="false" />

    <supports-gl-texture android:name="GL_KHR_texture_compression_astc_ldr" />

    <uses-permission android:name="android.permission.INTERNET" />

    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen.multitouch"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen.multitouch.distinct"
        android:required="false" />

    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <!--
  Copyright (c) 2020-2022, The Khronos Group Inc.
  SPDX-License-Identifier: Apache-2.0
    -->
    <uses-permission android:name="org.khronos.openxr.permission.OPENXR" />
    <uses-permission android:name="org.khronos.openxr.permission.OPENXR_SYSTEM" />

    <queries>
        <provider android:authorities="org.khronos.openxr.runtime_broker;org.khronos.openxr.system_runtime_broker" />
    </queries>

    <uses-permission android:name="com.pvr.tobactivate.permission.AUTH_CHECK" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <application
        android:extractNativeLibs="true"
        android:icon="@mipmap/app_icon"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true" >
        <activity
            android:name="com.unity3d.player.UnityPlayerActivity"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density"
            android:hardwareAccelerated="false"
            android:launchMode="singleTask"
            android:resizeableActivity="false"
            android:screenOrientation="fullUser"
            android:theme="@style/UnityThemeSelector" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <meta-data
                android:name="unityplayer.UnityActivity"
                android:value="true" />
            <meta-data
                android:name="android.notch_support"
                android:value="true" />
        </activity>

        <meta-data
            android:name="unity.splash-mode"
            android:value="0" />
        <meta-data
            android:name="unity.splash-enable"
            android:value="True" />
        <meta-data
            android:name="unity.launch-fullscreen"
            android:value="True" />
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape" />
        <meta-data
            android:name="unity.auto-report-fully-drawn"
            android:value="true" />
        <meta-data
            android:name="pvr.app.type"
            android:value="vr" />
        <meta-data
            android:name="pxr.sdk.version_code"
            android:value="5130" />
        <meta-data
            android:name="pvr.sdk.version"
            android:value="XR Platform_3.2.4" />
        <meta-data
            android:name="enable_cpt"
            android:value="0" />
        <meta-data
            android:name="Enable_AdaptiveHandModel"
            android:value="0" />
        <meta-data
            android:name="Hand_Tracking_HighFrequency"
            android:value="0" />
        <meta-data
            android:name="rendering_mode"
            android:value="0" />
        <meta-data
            android:name="display_rate"
            android:value="0" />
        <meta-data
            android:name="color_Space"
            android:value="1" />
        <meta-data
            android:name="MRCsupport"
            android:value="1" />
        <meta-data
            android:name="pvr.LateLatching"
            android:value="0" />
        <meta-data
            android:name="pvr.LateLatchingDebug"
            android:value="0" />
        <meta-data
            android:name="pvr.app.splash"
            android:value="0" />
        <meta-data
            android:name="PICO.swift.feature"
            android:value="0" />
        <meta-data
            android:name="adaptive_resolution"
            android:value="0" />
        <meta-data
            android:name="enable_mr_safeguard"
            android:value="0" />
        <meta-data
            android:name="enable_vst"
            android:value="0" />
        <meta-data
            android:name="enable_anchor"
            android:value="0" />
        <meta-data
            android:name="mr_map_mgr_auto_start"
            android:value="0" />
        <meta-data
            android:name="enable_spatial_anchor"
            android:value="0" />
        <meta-data
            android:name="enable_cloud_anchor"
            android:value="0" />
        <meta-data
            android:name="enable_mesh_anchor"
            android:value="0" />
        <meta-data
            android:name="enable_scene_anchor"
            android:value="0" />
        <meta-data
            android:name="pvr.SuperResolution"
            android:value="0" />
        <meta-data
            android:name="pvr.NormalSharpening"
            android:value="0" />
        <meta-data
            android:name="pvr.QualitySharpening"
            android:value="0" />
        <meta-data
            android:name="pvr.FixedFoveatedSharpening"
            android:value="0" />
        <meta-data
            android:name="pvr.SelfAdaptiveSharpening"
            android:value="0" />
        <meta-data
            android:name="pvr.app.secure_mr"
            android:value="0" />
        <meta-data
            android:name="controller"
            android:value="1" />
    </application>

</manifest>