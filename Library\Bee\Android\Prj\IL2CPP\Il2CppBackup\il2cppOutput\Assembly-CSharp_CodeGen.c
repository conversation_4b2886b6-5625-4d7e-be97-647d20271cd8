﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Void AlignmentTest::Start()
extern void AlignmentTest_Start_m2992385C4EC00DF4CBD504AFE7C4733AB74EA27C (void);
// 0x00000002 System.Void AlignmentTest::Update()
extern void AlignmentTest_Update_mC549F80E002E8792FC35718DB75096B24417796C (void);
// 0x00000003 System.Void AlignmentTest::ValidateReferencesPoints()
extern void AlignmentTest_ValidateReferencesPoints_m204A4E87E164E0B9A7BF3EF65CF1A40DF03C9FD7 (void);
// 0x00000004 System.Void AlignmentTest::PrintReferencePointAxes(AssemblyPart)
extern void AlignmentTest_PrintReferencePointAxes_mC8B51A9797C0BAB6F31DF8B4856DBCCFD6DF07E5 (void);
// 0x00000005 System.Void AlignmentTest::TestAlignment()
extern void AlignmentTest_TestAlignment_m0FE6AF80AE013ABF0311E74A818A1739C362664B (void);
// 0x00000006 System.Int32 AlignmentTest::GetReferencePointIndex(AssemblyPart,UnityEngine.Transform)
extern void AlignmentTest_GetReferencePointIndex_mD953C570EF17CBE25C6A6FCEEAC7F098213346B6 (void);
// 0x00000007 System.Void AlignmentTest::ResetSourcePart()
extern void AlignmentTest_ResetSourcePart_mD9BD350634284BC34A214C90A9097D928BA02877 (void);
// 0x00000008 System.Void AlignmentTest::AnalyzeRotationConstraints()
extern void AlignmentTest_AnalyzeRotationConstraints_m6F5468E3CD3F9C5FF79861F198486D2FCF442C06 (void);
// 0x00000009 System.Void AlignmentTest::OnDrawGizmos()
extern void AlignmentTest_OnDrawGizmos_m3B6D469A036F4294CCB23D0FC7ED12B4370CD139 (void);
// 0x0000000A System.Void AlignmentTest::.ctor()
extern void AlignmentTest__ctor_m03EB6CA5E2A81935E8B7633CF50EC4512D82D9E4 (void);
// 0x0000000B System.Void AssemblyAnimation::Start()
extern void AssemblyAnimation_Start_m6B156AEC545BABFC9E83268A7242AB3E6AAD57A0 (void);
// 0x0000000C System.Collections.IEnumerator AssemblyAnimation::AssembleAnimation()
extern void AssemblyAnimation_AssembleAnimation_mD23C831759F0A86521787D49EB39059B229A87DF (void);
// 0x0000000D UnityEngine.Quaternion AssemblyAnimation::CalculateTargetRotation()
extern void AssemblyAnimation_CalculateTargetRotation_mA6508A3B3E9CD605EDDF12699318800E66B5A0D6 (void);
// 0x0000000E UnityEngine.Vector3 AssemblyAnimation::GetAxisDirection(UnityEngine.Transform)
extern void AssemblyAnimation_GetAxisDirection_m8AFF54066643C0EC9792A5D5060431F772F5F983 (void);
// 0x0000000F System.Collections.IEnumerator AssemblyAnimation::MoveAndRotate(UnityEngine.Transform,UnityEngine.Vector3,UnityEngine.Quaternion,System.Single,System.Boolean)
extern void AssemblyAnimation_MoveAndRotate_mBF7B193EA8886BD53D4AF584C7CC0226BFF99D49 (void);
// 0x00000010 System.Void AssemblyAnimation::UpdateMountPointPosition(UnityEngine.Transform,UnityEngine.Transform)
extern void AssemblyAnimation_UpdateMountPointPosition_mEF4CC16237F1B31ECDC5989FCAC099D13AE9F060 (void);
// 0x00000011 System.Collections.IEnumerator AssemblyAnimation::Move(UnityEngine.Transform,UnityEngine.Vector3,System.Single)
extern void AssemblyAnimation_Move_mDD30F014C6D6EB3049C99A57EEDA225B59E675FA (void);
// 0x00000012 System.Collections.IEnumerator AssemblyAnimation::RotateAndMoveNut(UnityEngine.Transform,System.Single,UnityEngine.Vector3,System.Single)
extern void AssemblyAnimation_RotateAndMoveNut_m2A5A259FCF9DB2A7C975DC329853C3C237218F2A (void);
// 0x00000013 System.Collections.IEnumerator AssemblyAnimation::RotateAroundPivot(UnityEngine.Transform,UnityEngine.Vector3,UnityEngine.Vector3,System.Single,System.Single)
extern void AssemblyAnimation_RotateAroundPivot_m11F4F892FE211459F0A8FE6E7E26A3D16020ED9C (void);
// 0x00000014 System.Void AssemblyAnimation::OnDrawGizmos()
extern void AssemblyAnimation_OnDrawGizmos_m9C46EA1309C3833F2A60966C729161043E990B0C (void);
// 0x00000015 System.Void AssemblyAnimation::PrintDebugInfo()
extern void AssemblyAnimation_PrintDebugInfo_m886797F456E00CE82212CA80B857BED824C4382E (void);
// 0x00000016 System.Void AssemblyAnimation::Update()
extern void AssemblyAnimation_Update_m4775FEC5063A69CB7B4062F471A710822952F9FF (void);
// 0x00000017 System.Collections.IEnumerator AssemblyAnimation::MoveScrewImproved(UnityEngine.Vector3)
extern void AssemblyAnimation_MoveScrewImproved_m34A71C209750820105B716048F82429A90D11EA8 (void);
// 0x00000018 System.Collections.IEnumerator AssemblyAnimation::RotateInPlace(UnityEngine.Transform,UnityEngine.Transform,UnityEngine.Quaternion,System.Single)
extern void AssemblyAnimation_RotateInPlace_mC016BA5F2E9EFCF628B4E57E3299B095D0087D82 (void);
// 0x00000019 System.Collections.IEnumerator AssemblyAnimation::MoveNutImproved(UnityEngine.Vector3)
extern void AssemblyAnimation_MoveNutImproved_m3787B1F890FBC2A73697F7FC99833FFC5360C68E (void);
// 0x0000001A System.Collections.IEnumerator AssemblyAnimation::MoveAndRotateByMountPoint(UnityEngine.Transform,UnityEngine.Transform,UnityEngine.Vector3,UnityEngine.Quaternion,System.Single)
extern void AssemblyAnimation_MoveAndRotateByMountPoint_m0490CD567207CBB769708FEC8C33887C1F0EDA3D (void);
// 0x0000001B System.Collections.IEnumerator AssemblyAnimation::MoveByMountPoint(UnityEngine.Transform,UnityEngine.Transform,UnityEngine.Vector3,System.Single)
extern void AssemblyAnimation_MoveByMountPoint_m3F9E1AB39B0010533A6D51206BA700BC14168F57 (void);
// 0x0000001C System.Void AssemblyAnimation::AlignTransformPosition(UnityEngine.Transform,UnityEngine.Vector3)
extern void AssemblyAnimation_AlignTransformPosition_m3D151DC8759C7E823C727DFD49ED7E446ADDF9F7 (void);
// 0x0000001D System.Void AssemblyAnimation::.ctor()
extern void AssemblyAnimation__ctor_m3B6ADFF0903CB9927E995FE63DC4FEDCE5AEE408 (void);
// 0x0000001E System.Void AssemblyAnimation/<AssembleAnimation>d__21::.ctor(System.Int32)
extern void U3CAssembleAnimationU3Ed__21__ctor_m486944FD17AD41EF0EB65721E434EDD0C859FBC0 (void);
// 0x0000001F System.Void AssemblyAnimation/<AssembleAnimation>d__21::System.IDisposable.Dispose()
extern void U3CAssembleAnimationU3Ed__21_System_IDisposable_Dispose_mC982219E4D12769F0121C6B9F5BC0A547268F74C (void);
// 0x00000020 System.Boolean AssemblyAnimation/<AssembleAnimation>d__21::MoveNext()
extern void U3CAssembleAnimationU3Ed__21_MoveNext_m9195D7E71EFAA2CD414BCE34293DD023C25543BA (void);
// 0x00000021 System.Object AssemblyAnimation/<AssembleAnimation>d__21::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAssembleAnimationU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDCA1BD8238361DF92C2206969BA75929D5886379 (void);
// 0x00000022 System.Void AssemblyAnimation/<AssembleAnimation>d__21::System.Collections.IEnumerator.Reset()
extern void U3CAssembleAnimationU3Ed__21_System_Collections_IEnumerator_Reset_m6E8DD4B28AB37EE167F18FB88D936E3295AAF610 (void);
// 0x00000023 System.Object AssemblyAnimation/<AssembleAnimation>d__21::System.Collections.IEnumerator.get_Current()
extern void U3CAssembleAnimationU3Ed__21_System_Collections_IEnumerator_get_Current_m0C9027DEDA977EF87AD0476301DAC9FAA27D0B67 (void);
// 0x00000024 System.Void AssemblyAnimation/<MoveAndRotate>d__24::.ctor(System.Int32)
extern void U3CMoveAndRotateU3Ed__24__ctor_m14BA0EF9D087AA07D1FA44644D8D76EE7B3C1EAC (void);
// 0x00000025 System.Void AssemblyAnimation/<MoveAndRotate>d__24::System.IDisposable.Dispose()
extern void U3CMoveAndRotateU3Ed__24_System_IDisposable_Dispose_m122DFE0E8B6098A97535CBDE8823AB03ECE2275F (void);
// 0x00000026 System.Boolean AssemblyAnimation/<MoveAndRotate>d__24::MoveNext()
extern void U3CMoveAndRotateU3Ed__24_MoveNext_mFA6F675DE65A4AA8970D97C434AEBA3849B55BEE (void);
// 0x00000027 System.Object AssemblyAnimation/<MoveAndRotate>d__24::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveAndRotateU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m252FE712A52F09C50A074FB2D672EEB600BFB317 (void);
// 0x00000028 System.Void AssemblyAnimation/<MoveAndRotate>d__24::System.Collections.IEnumerator.Reset()
extern void U3CMoveAndRotateU3Ed__24_System_Collections_IEnumerator_Reset_mEE4599C8344317F907FAFE052180C8CAC745C1E8 (void);
// 0x00000029 System.Object AssemblyAnimation/<MoveAndRotate>d__24::System.Collections.IEnumerator.get_Current()
extern void U3CMoveAndRotateU3Ed__24_System_Collections_IEnumerator_get_Current_mEB6C6624554E112D7CB1C745504DC9ECC5D01589 (void);
// 0x0000002A System.Void AssemblyAnimation/<Move>d__26::.ctor(System.Int32)
extern void U3CMoveU3Ed__26__ctor_m9716B47FCF49AC1DB55C0D018EBC2A9E888E1351 (void);
// 0x0000002B System.Void AssemblyAnimation/<Move>d__26::System.IDisposable.Dispose()
extern void U3CMoveU3Ed__26_System_IDisposable_Dispose_m13AAE265F854DD6C74470BA76FB235FC2A3AE5BD (void);
// 0x0000002C System.Boolean AssemblyAnimation/<Move>d__26::MoveNext()
extern void U3CMoveU3Ed__26_MoveNext_m09017BC9C35757AEB0E89872C7623A4ED9CA3205 (void);
// 0x0000002D System.Object AssemblyAnimation/<Move>d__26::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveU3Ed__26_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m184E395C74F4E6536C76E8F549D0102279BD21EA (void);
// 0x0000002E System.Void AssemblyAnimation/<Move>d__26::System.Collections.IEnumerator.Reset()
extern void U3CMoveU3Ed__26_System_Collections_IEnumerator_Reset_m48E2711DAA629D40F110620E7C00F3DF1F28234E (void);
// 0x0000002F System.Object AssemblyAnimation/<Move>d__26::System.Collections.IEnumerator.get_Current()
extern void U3CMoveU3Ed__26_System_Collections_IEnumerator_get_Current_m971AC46F3CF6750BBE7F0C586BD91ACC97DCD68E (void);
// 0x00000030 System.Void AssemblyAnimation/<RotateAndMoveNut>d__27::.ctor(System.Int32)
extern void U3CRotateAndMoveNutU3Ed__27__ctor_m0AA3BFBE304E14564FA817E68A63F9E529D8C6DB (void);
// 0x00000031 System.Void AssemblyAnimation/<RotateAndMoveNut>d__27::System.IDisposable.Dispose()
extern void U3CRotateAndMoveNutU3Ed__27_System_IDisposable_Dispose_m9CCAE6E643EA4FC7C0EADAF67D1357BC131FAE0A (void);
// 0x00000032 System.Boolean AssemblyAnimation/<RotateAndMoveNut>d__27::MoveNext()
extern void U3CRotateAndMoveNutU3Ed__27_MoveNext_mB1AAAF5FEBAF1D2B290AB05349BF2683CB52FA17 (void);
// 0x00000033 System.Object AssemblyAnimation/<RotateAndMoveNut>d__27::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRotateAndMoveNutU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7417189A45F030516B044C8A83724507E296718C (void);
// 0x00000034 System.Void AssemblyAnimation/<RotateAndMoveNut>d__27::System.Collections.IEnumerator.Reset()
extern void U3CRotateAndMoveNutU3Ed__27_System_Collections_IEnumerator_Reset_m8368D49D750EF526B25970EDAF45917ADC48FA54 (void);
// 0x00000035 System.Object AssemblyAnimation/<RotateAndMoveNut>d__27::System.Collections.IEnumerator.get_Current()
extern void U3CRotateAndMoveNutU3Ed__27_System_Collections_IEnumerator_get_Current_mC164F69E0B2620D4B773E91FDD95E76EDE2183D5 (void);
// 0x00000036 System.Void AssemblyAnimation/<RotateAroundPivot>d__28::.ctor(System.Int32)
extern void U3CRotateAroundPivotU3Ed__28__ctor_m7514BDD06AE5CBE0394539FF5B388116E252AF59 (void);
// 0x00000037 System.Void AssemblyAnimation/<RotateAroundPivot>d__28::System.IDisposable.Dispose()
extern void U3CRotateAroundPivotU3Ed__28_System_IDisposable_Dispose_mFFC5549CC8E641BDC1D8D67D1899ED2DF9F577C9 (void);
// 0x00000038 System.Boolean AssemblyAnimation/<RotateAroundPivot>d__28::MoveNext()
extern void U3CRotateAroundPivotU3Ed__28_MoveNext_m7049D492430B44C01E36B6966FCBF05AE1CA7487 (void);
// 0x00000039 System.Object AssemblyAnimation/<RotateAroundPivot>d__28::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRotateAroundPivotU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD87D0B599C83DB3030C042558E7BC11DF134029F (void);
// 0x0000003A System.Void AssemblyAnimation/<RotateAroundPivot>d__28::System.Collections.IEnumerator.Reset()
extern void U3CRotateAroundPivotU3Ed__28_System_Collections_IEnumerator_Reset_m8F009E68606B9434FF94AEA9025D3D0220F95A0D (void);
// 0x0000003B System.Object AssemblyAnimation/<RotateAroundPivot>d__28::System.Collections.IEnumerator.get_Current()
extern void U3CRotateAroundPivotU3Ed__28_System_Collections_IEnumerator_get_Current_m995BEB903FD7837F6FECCE90B73058479F3E57F3 (void);
// 0x0000003C System.Void AssemblyAnimation/<MoveScrewImproved>d__32::.ctor(System.Int32)
extern void U3CMoveScrewImprovedU3Ed__32__ctor_mCDE0CDA7508BE39FB54800AF3DB2A2F399815EC6 (void);
// 0x0000003D System.Void AssemblyAnimation/<MoveScrewImproved>d__32::System.IDisposable.Dispose()
extern void U3CMoveScrewImprovedU3Ed__32_System_IDisposable_Dispose_mC1BD0D23CC393EBECFCEB68B629D2BB8900C11D2 (void);
// 0x0000003E System.Boolean AssemblyAnimation/<MoveScrewImproved>d__32::MoveNext()
extern void U3CMoveScrewImprovedU3Ed__32_MoveNext_mE0EFAD28F37BFBBD90687BAA2F449DC7136C5459 (void);
// 0x0000003F System.Object AssemblyAnimation/<MoveScrewImproved>d__32::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveScrewImprovedU3Ed__32_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mE6549B0292EA7BBD8E33A7CA1AA62171C351CC4A (void);
// 0x00000040 System.Void AssemblyAnimation/<MoveScrewImproved>d__32::System.Collections.IEnumerator.Reset()
extern void U3CMoveScrewImprovedU3Ed__32_System_Collections_IEnumerator_Reset_mCC6F396AE06E5BCA37EBB1F5F43EDDB5EF0AFFA7 (void);
// 0x00000041 System.Object AssemblyAnimation/<MoveScrewImproved>d__32::System.Collections.IEnumerator.get_Current()
extern void U3CMoveScrewImprovedU3Ed__32_System_Collections_IEnumerator_get_Current_m822E2E6F2C3DC8D60CDFA7D759B9588A8B793FDE (void);
// 0x00000042 System.Void AssemblyAnimation/<RotateInPlace>d__33::.ctor(System.Int32)
extern void U3CRotateInPlaceU3Ed__33__ctor_mC8C4126A76A5AFA2BED0922A9A560AEBA42FD4C4 (void);
// 0x00000043 System.Void AssemblyAnimation/<RotateInPlace>d__33::System.IDisposable.Dispose()
extern void U3CRotateInPlaceU3Ed__33_System_IDisposable_Dispose_m4D19E780BD89A5BE3FD6033C757793FAAED1EB0C (void);
// 0x00000044 System.Boolean AssemblyAnimation/<RotateInPlace>d__33::MoveNext()
extern void U3CRotateInPlaceU3Ed__33_MoveNext_mAFB49C696CAC83C5682FBBEDD560A018337E4A1F (void);
// 0x00000045 System.Object AssemblyAnimation/<RotateInPlace>d__33::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRotateInPlaceU3Ed__33_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m966453D359708348AD2877B2B65FC557002CC921 (void);
// 0x00000046 System.Void AssemblyAnimation/<RotateInPlace>d__33::System.Collections.IEnumerator.Reset()
extern void U3CRotateInPlaceU3Ed__33_System_Collections_IEnumerator_Reset_mA9E151254C98D3129A5608CF72644B7A44523D7F (void);
// 0x00000047 System.Object AssemblyAnimation/<RotateInPlace>d__33::System.Collections.IEnumerator.get_Current()
extern void U3CRotateInPlaceU3Ed__33_System_Collections_IEnumerator_get_Current_mA2EF39F00D69519A69EB032BBAB65571E639FA92 (void);
// 0x00000048 System.Void AssemblyAnimation/<MoveNutImproved>d__34::.ctor(System.Int32)
extern void U3CMoveNutImprovedU3Ed__34__ctor_mAE734DEEF29803125421859097231EB174A0A816 (void);
// 0x00000049 System.Void AssemblyAnimation/<MoveNutImproved>d__34::System.IDisposable.Dispose()
extern void U3CMoveNutImprovedU3Ed__34_System_IDisposable_Dispose_m81E68AF574B8DD98DCECC368AE6EDCDF4239C945 (void);
// 0x0000004A System.Boolean AssemblyAnimation/<MoveNutImproved>d__34::MoveNext()
extern void U3CMoveNutImprovedU3Ed__34_MoveNext_m3D73BBEA683BC5404B7865852255F53FBC907B94 (void);
// 0x0000004B System.Object AssemblyAnimation/<MoveNutImproved>d__34::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveNutImprovedU3Ed__34_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m118403148DDF0A023CDF1CFC10FD389EE84E0285 (void);
// 0x0000004C System.Void AssemblyAnimation/<MoveNutImproved>d__34::System.Collections.IEnumerator.Reset()
extern void U3CMoveNutImprovedU3Ed__34_System_Collections_IEnumerator_Reset_m6766341120AA0057CA9A4A2D67B3FFB6F6950613 (void);
// 0x0000004D System.Object AssemblyAnimation/<MoveNutImproved>d__34::System.Collections.IEnumerator.get_Current()
extern void U3CMoveNutImprovedU3Ed__34_System_Collections_IEnumerator_get_Current_m3D758031FBF1166270AB8B33D8ECE2BD134C9802 (void);
// 0x0000004E System.Void AssemblyAnimation/<MoveAndRotateByMountPoint>d__35::.ctor(System.Int32)
extern void U3CMoveAndRotateByMountPointU3Ed__35__ctor_mBA636007EAF50870880909CB73BE2FB864AD4914 (void);
// 0x0000004F System.Void AssemblyAnimation/<MoveAndRotateByMountPoint>d__35::System.IDisposable.Dispose()
extern void U3CMoveAndRotateByMountPointU3Ed__35_System_IDisposable_Dispose_mF8426BDEA58C677F89E48D79F73EFA8CBFCB524A (void);
// 0x00000050 System.Boolean AssemblyAnimation/<MoveAndRotateByMountPoint>d__35::MoveNext()
extern void U3CMoveAndRotateByMountPointU3Ed__35_MoveNext_m04B0FA0693C400947042F490CFF4F4B06C920270 (void);
// 0x00000051 System.Object AssemblyAnimation/<MoveAndRotateByMountPoint>d__35::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveAndRotateByMountPointU3Ed__35_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9321065DDE127745BAFF0090A1529BE7A4D10A3E (void);
// 0x00000052 System.Void AssemblyAnimation/<MoveAndRotateByMountPoint>d__35::System.Collections.IEnumerator.Reset()
extern void U3CMoveAndRotateByMountPointU3Ed__35_System_Collections_IEnumerator_Reset_mE4D4C4AA40D64F22503CEC2EBEF702B06A4593B7 (void);
// 0x00000053 System.Object AssemblyAnimation/<MoveAndRotateByMountPoint>d__35::System.Collections.IEnumerator.get_Current()
extern void U3CMoveAndRotateByMountPointU3Ed__35_System_Collections_IEnumerator_get_Current_mC27596FEB950ECF7C6BD79B33462B221947244EF (void);
// 0x00000054 System.Void AssemblyAnimation/<MoveByMountPoint>d__36::.ctor(System.Int32)
extern void U3CMoveByMountPointU3Ed__36__ctor_m1F54AE1061A0248056DF5DD5999A009334899B3B (void);
// 0x00000055 System.Void AssemblyAnimation/<MoveByMountPoint>d__36::System.IDisposable.Dispose()
extern void U3CMoveByMountPointU3Ed__36_System_IDisposable_Dispose_m97D03CFD8318D2EA71F3DA9ADAA0243214A851D1 (void);
// 0x00000056 System.Boolean AssemblyAnimation/<MoveByMountPoint>d__36::MoveNext()
extern void U3CMoveByMountPointU3Ed__36_MoveNext_mA51856E916803374B00A52F35F194BBCA0356FA2 (void);
// 0x00000057 System.Object AssemblyAnimation/<MoveByMountPoint>d__36::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveByMountPointU3Ed__36_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF9266A75C9C4C9A8B0B0F64E4F7AD3C5E478D2D7 (void);
// 0x00000058 System.Void AssemblyAnimation/<MoveByMountPoint>d__36::System.Collections.IEnumerator.Reset()
extern void U3CMoveByMountPointU3Ed__36_System_Collections_IEnumerator_Reset_mAFA5F0C0C5A727A53F1189E6195C432A9890B47E (void);
// 0x00000059 System.Object AssemblyAnimation/<MoveByMountPoint>d__36::System.Collections.IEnumerator.get_Current()
extern void U3CMoveByMountPointU3Ed__36_System_Collections_IEnumerator_get_Current_mC81D9F1CB442057CF329B637CF00BACF54CA3CE4 (void);
// 0x0000005A System.Single AssemblyAnimationManager::get_AnimationSpeedRate()
extern void AssemblyAnimationManager_get_AnimationSpeedRate_mDDBE70FD2FAD13DD46C69E376953215CF3B33E68 (void);
// 0x0000005B System.Void AssemblyAnimationManager::set_AnimationSpeedRate(System.Single)
extern void AssemblyAnimationManager_set_AnimationSpeedRate_m40A5C4BD1F9B4DF2AB78FE5932B160E32FE67292 (void);
// 0x0000005C System.Boolean AssemblyAnimationManager::ValidatePart(IAssemblyPart,System.String)
extern void AssemblyAnimationManager_ValidatePart_m46F20A6F8EC1C83A4BA17E29EEE6938FA86433DA (void);
// 0x0000005D System.Collections.IEnumerator AssemblyAnimationManager::AnimateOverTime(System.Single,System.Action`1<System.Single>,System.Action)
extern void AssemblyAnimationManager_AnimateOverTime_m81DD5756B67A920512A58FF3DE976E2B1512249F (void);
// 0x0000005E System.Collections.IEnumerator AssemblyAnimationManager::MovePart(IAssemblyPart,UnityEngine.Vector3,System.Nullable`1<UnityEngine.Quaternion>,System.Single,System.Boolean)
extern void AssemblyAnimationManager_MovePart_m09C69C191E8C81AA6DAC05CD7EE6A7509B2ABA29 (void);
// 0x0000005F System.Collections.IEnumerator AssemblyAnimationManager::RotatePartInPlace(IAssemblyPart,UnityEngine.Quaternion,System.Single)
extern void AssemblyAnimationManager_RotatePartInPlace_m9347E51B925042884D46A5C1908FD9E911D59D46 (void);
// 0x00000060 System.Collections.IEnumerator AssemblyAnimationManager::RotatePartAroundAxis(IAssemblyPart,UnityEngine.Vector3,System.Single,System.Nullable`1<UnityEngine.Vector3>,System.Single)
extern void AssemblyAnimationManager_RotatePartAroundAxis_m63484611346ED485115C86577179D0DF3B21470A (void);
// 0x00000061 System.Collections.IEnumerator AssemblyAnimationManager::MoveAndRotateByMountPoint(IAssemblyPart,UnityEngine.Vector3,UnityEngine.Quaternion,System.Single)
extern void AssemblyAnimationManager_MoveAndRotateByMountPoint_m8DD0338BCBBD7B54E03416BE6B2532177419EA73 (void);
// 0x00000062 System.Collections.IEnumerator AssemblyAnimationManager::MoveByMountPoint(IAssemblyPart,UnityEngine.Vector3,System.Single)
extern void AssemblyAnimationManager_MoveByMountPoint_m81F522832892F3795A0E865113CAD3800B7DCC25 (void);
// 0x00000063 System.Void AssemblyAnimationManager::AlignTransformPosition(UnityEngine.Transform,UnityEngine.Vector3)
extern void AssemblyAnimationManager_AlignTransformPosition_m9D6C3792968DBDB142757118D3C0298A1E2C9391 (void);
// 0x00000064 System.Collections.IEnumerator AssemblyAnimationManager::AlignParts(AssemblyPart,AssemblyPart,System.Int32,System.Int32,System.Single)
extern void AssemblyAnimationManager_AlignParts_m7A6EA927C278B2855C41C4AA9534EAB306868E78 (void);
// 0x00000065 UnityEngine.Quaternion AssemblyAnimationManager::CalculateAlignmentRotation(UnityEngine.Transform,UnityEngine.Transform,AssemblyPart)
extern void AssemblyAnimationManager_CalculateAlignmentRotation_m11CBD762346D5454230FD794143B9994E37F4CA9 (void);
// 0x00000066 UnityEngine.Vector3 AssemblyAnimationManager::GetAxisDirection(UnityEngine.Transform)
extern void AssemblyAnimationManager_GetAxisDirection_m4D43AF8864F81864ACCC4EC249DB55B8B9A82DB4 (void);
// 0x00000067 System.Collections.IEnumerator AssemblyAnimationManager::InstallScrew(AssemblyPart,UnityEngine.Transform,UnityEngine.Vector3,System.Single)
extern void AssemblyAnimationManager_InstallScrew_mD607DC25321427D9DF79BD546E1EEE269D5CC6E1 (void);
// 0x00000068 System.Collections.IEnumerator AssemblyAnimationManager::InstallNut(AssemblyPart,UnityEngine.Transform,UnityEngine.Vector3,System.Single)
extern void AssemblyAnimationManager_InstallNut_m402E3A46E8C657C86E6C3B7465E58B09F682D35F (void);
// 0x00000069 System.Void AssemblyAnimationManager::AlignPartsAsync(AssemblyPart,AssemblyPart,System.Int32,System.Int32)
extern void AssemblyAnimationManager_AlignPartsAsync_m5E585B7D17D642E4074BFD40D17713910AB09CBE (void);
// 0x0000006A System.Collections.IEnumerator AssemblyAnimationManager::AlignPartsSync(AssemblyPart,AssemblyPart,System.Int32,System.Int32,System.Single)
extern void AssemblyAnimationManager_AlignPartsSync_m688E20F88A62998DAF49DDB8D7EBFD2A62F982E4 (void);
// 0x0000006B System.Collections.IEnumerator AssemblyAnimationManager::CompleteAssemblySequence(AssemblyPart,AssemblyPart,AssemblyPart,AssemblyPart,UnityEngine.Transform,UnityEngine.Vector3,System.Int32,System.Int32,System.Single)
extern void AssemblyAnimationManager_CompleteAssemblySequence_m2D88E4AFFF901634E3B1D159D3AC9C12CB12C03D (void);
// 0x0000006C System.Void AssemblyAnimationManager::.ctor()
extern void AssemblyAnimationManager__ctor_mBEC5D5A7F30D782D77777D225EC4EB2BE97E1531 (void);
// 0x0000006D System.Void AssemblyAnimationManager/<AnimateOverTime>d__10::.ctor(System.Int32)
extern void U3CAnimateOverTimeU3Ed__10__ctor_m6FCD3B0A5AF03A24AF7855090ECB2A3599AD626F (void);
// 0x0000006E System.Void AssemblyAnimationManager/<AnimateOverTime>d__10::System.IDisposable.Dispose()
extern void U3CAnimateOverTimeU3Ed__10_System_IDisposable_Dispose_mF4C77CC94ED80E76C76F571E6811ADB7ADE3454F (void);
// 0x0000006F System.Boolean AssemblyAnimationManager/<AnimateOverTime>d__10::MoveNext()
extern void U3CAnimateOverTimeU3Ed__10_MoveNext_m195A1FC6B97364310709D23CE85B1D4F86854370 (void);
// 0x00000070 System.Object AssemblyAnimationManager/<AnimateOverTime>d__10::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAnimateOverTimeU3Ed__10_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0B993B2148D70AAD993C1717D53A5784D27ECACA (void);
// 0x00000071 System.Void AssemblyAnimationManager/<AnimateOverTime>d__10::System.Collections.IEnumerator.Reset()
extern void U3CAnimateOverTimeU3Ed__10_System_Collections_IEnumerator_Reset_mB732BBA347180FD8B94FBEA662120F00D8786111 (void);
// 0x00000072 System.Object AssemblyAnimationManager/<AnimateOverTime>d__10::System.Collections.IEnumerator.get_Current()
extern void U3CAnimateOverTimeU3Ed__10_System_Collections_IEnumerator_get_Current_m4F0F9A98AAEFDEEB272405E86F8B02A5335C4F60 (void);
// 0x00000073 System.Void AssemblyAnimationManager/<>c__DisplayClass11_0::.ctor()
extern void U3CU3Ec__DisplayClass11_0__ctor_m967251DBE7B3B10F213ADD91883755FADA548AB4 (void);
// 0x00000074 System.Void AssemblyAnimationManager/<>c__DisplayClass11_0::<MovePart>b__0(System.Single)
extern void U3CU3Ec__DisplayClass11_0_U3CMovePartU3Eb__0_mFE9AE39CD908349C0EAEE49C2A8FFF428D94C85F (void);
// 0x00000075 System.Void AssemblyAnimationManager/<>c__DisplayClass11_0::<MovePart>b__1()
extern void U3CU3Ec__DisplayClass11_0_U3CMovePartU3Eb__1_m17628096485283FE54E09C9F938CD11ED9CC6E06 (void);
// 0x00000076 System.Void AssemblyAnimationManager/<MovePart>d__11::.ctor(System.Int32)
extern void U3CMovePartU3Ed__11__ctor_mFA3733646E1A3F848FE6AF0D2FCEEC8D68E50693 (void);
// 0x00000077 System.Void AssemblyAnimationManager/<MovePart>d__11::System.IDisposable.Dispose()
extern void U3CMovePartU3Ed__11_System_IDisposable_Dispose_mA079DA89C6672151C2CD1314F5765C125AF96C57 (void);
// 0x00000078 System.Boolean AssemblyAnimationManager/<MovePart>d__11::MoveNext()
extern void U3CMovePartU3Ed__11_MoveNext_m21218F444A2D0047B188088AB460646A93D9305B (void);
// 0x00000079 System.Object AssemblyAnimationManager/<MovePart>d__11::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMovePartU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m55FC00A208A5DB3DD89DA1674D16386029538640 (void);
// 0x0000007A System.Void AssemblyAnimationManager/<MovePart>d__11::System.Collections.IEnumerator.Reset()
extern void U3CMovePartU3Ed__11_System_Collections_IEnumerator_Reset_m5F3B4BD4B900E1216395E69DF7DA981CA1A1B549 (void);
// 0x0000007B System.Object AssemblyAnimationManager/<MovePart>d__11::System.Collections.IEnumerator.get_Current()
extern void U3CMovePartU3Ed__11_System_Collections_IEnumerator_get_Current_m69793D09EC560C4DD5F3515011D0D4A2D5D06386 (void);
// 0x0000007C System.Void AssemblyAnimationManager/<>c__DisplayClass12_0::.ctor()
extern void U3CU3Ec__DisplayClass12_0__ctor_mB6F29EF3938BE271918C03B9E60F474FCB91F1A0 (void);
// 0x0000007D System.Void AssemblyAnimationManager/<>c__DisplayClass12_0::<RotatePartInPlace>b__0(System.Single)
extern void U3CU3Ec__DisplayClass12_0_U3CRotatePartInPlaceU3Eb__0_m72522C877F6F4AEFE8203DADB1C07D2BBFC61115 (void);
// 0x0000007E System.Void AssemblyAnimationManager/<>c__DisplayClass12_0::<RotatePartInPlace>b__1()
extern void U3CU3Ec__DisplayClass12_0_U3CRotatePartInPlaceU3Eb__1_m44ACA0310BCB59F931DCB5B64F963C9D23CA1C85 (void);
// 0x0000007F System.Void AssemblyAnimationManager/<RotatePartInPlace>d__12::.ctor(System.Int32)
extern void U3CRotatePartInPlaceU3Ed__12__ctor_mBEAA0D6912A77EF3CA7345E3F8536984DFA9BD7E (void);
// 0x00000080 System.Void AssemblyAnimationManager/<RotatePartInPlace>d__12::System.IDisposable.Dispose()
extern void U3CRotatePartInPlaceU3Ed__12_System_IDisposable_Dispose_m447EE95281F6777373242F2B98710E144AEFEC9F (void);
// 0x00000081 System.Boolean AssemblyAnimationManager/<RotatePartInPlace>d__12::MoveNext()
extern void U3CRotatePartInPlaceU3Ed__12_MoveNext_mA8B419B80BF09EF8959DCF95FEE2B073EB740F1C (void);
// 0x00000082 System.Object AssemblyAnimationManager/<RotatePartInPlace>d__12::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRotatePartInPlaceU3Ed__12_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mFB47B3D54C6AB9FF50DE010DAB96D2C8AEA412ED (void);
// 0x00000083 System.Void AssemblyAnimationManager/<RotatePartInPlace>d__12::System.Collections.IEnumerator.Reset()
extern void U3CRotatePartInPlaceU3Ed__12_System_Collections_IEnumerator_Reset_m02A1EA77E16E756AC9EC6DC86F8B7E856065BE46 (void);
// 0x00000084 System.Object AssemblyAnimationManager/<RotatePartInPlace>d__12::System.Collections.IEnumerator.get_Current()
extern void U3CRotatePartInPlaceU3Ed__12_System_Collections_IEnumerator_get_Current_m06B320F837DFD7E7B8CDCA391D2A97C3CE263DB7 (void);
// 0x00000085 System.Void AssemblyAnimationManager/<>c__DisplayClass13_0::.ctor()
extern void U3CU3Ec__DisplayClass13_0__ctor_mBCC63144A53BF221E89F58AF6381A540E801EB51 (void);
// 0x00000086 System.Void AssemblyAnimationManager/<>c__DisplayClass13_0::<RotatePartAroundAxis>b__0(System.Single)
extern void U3CU3Ec__DisplayClass13_0_U3CRotatePartAroundAxisU3Eb__0_mA262C9AAA7259FB504A9C0603F0FAC70549F5C6F (void);
// 0x00000087 System.Void AssemblyAnimationManager/<>c__DisplayClass13_0::<RotatePartAroundAxis>b__1()
extern void U3CU3Ec__DisplayClass13_0_U3CRotatePartAroundAxisU3Eb__1_mE14982D28A9AA313AE0092D73E6FAAC5B6950B25 (void);
// 0x00000088 System.Void AssemblyAnimationManager/<RotatePartAroundAxis>d__13::.ctor(System.Int32)
extern void U3CRotatePartAroundAxisU3Ed__13__ctor_mBB1D7E0F7826D2919AA3B89AB3BE3A8D739A92D7 (void);
// 0x00000089 System.Void AssemblyAnimationManager/<RotatePartAroundAxis>d__13::System.IDisposable.Dispose()
extern void U3CRotatePartAroundAxisU3Ed__13_System_IDisposable_Dispose_m2AE5F0A9BE26DA938FBC3E7D2CDE3CBCA540ADC4 (void);
// 0x0000008A System.Boolean AssemblyAnimationManager/<RotatePartAroundAxis>d__13::MoveNext()
extern void U3CRotatePartAroundAxisU3Ed__13_MoveNext_m850999FBC7D938BAB9B0DD1819C0F7600F56ADE9 (void);
// 0x0000008B System.Object AssemblyAnimationManager/<RotatePartAroundAxis>d__13::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRotatePartAroundAxisU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mFC1A4041D2B788A31244F310786491D89C1ADAFE (void);
// 0x0000008C System.Void AssemblyAnimationManager/<RotatePartAroundAxis>d__13::System.Collections.IEnumerator.Reset()
extern void U3CRotatePartAroundAxisU3Ed__13_System_Collections_IEnumerator_Reset_mA646940B486F8DF2ABDC54D9BB4A522D6DF474D2 (void);
// 0x0000008D System.Object AssemblyAnimationManager/<RotatePartAroundAxis>d__13::System.Collections.IEnumerator.get_Current()
extern void U3CRotatePartAroundAxisU3Ed__13_System_Collections_IEnumerator_get_Current_mC12DCAF15357B258D61791DDD82B51736D590D71 (void);
// 0x0000008E System.Void AssemblyAnimationManager/<>c__DisplayClass14_0::.ctor()
extern void U3CU3Ec__DisplayClass14_0__ctor_m5B7FEE20A5C07A27340298EAE5D539571DF7A66C (void);
// 0x0000008F System.Void AssemblyAnimationManager/<>c__DisplayClass14_0::<MoveAndRotateByMountPoint>b__0(System.Single)
extern void U3CU3Ec__DisplayClass14_0_U3CMoveAndRotateByMountPointU3Eb__0_m7EC2C1E485309DC5260F9CE9562BEF09F7B54D85 (void);
// 0x00000090 System.Void AssemblyAnimationManager/<>c__DisplayClass14_0::<MoveAndRotateByMountPoint>b__1()
extern void U3CU3Ec__DisplayClass14_0_U3CMoveAndRotateByMountPointU3Eb__1_mC71660A82CF4BC0BDE3B78AE5DF10942D7F105EA (void);
// 0x00000091 System.Void AssemblyAnimationManager/<MoveAndRotateByMountPoint>d__14::.ctor(System.Int32)
extern void U3CMoveAndRotateByMountPointU3Ed__14__ctor_m9A080D9627117975CA8C32F3604679177706FDD0 (void);
// 0x00000092 System.Void AssemblyAnimationManager/<MoveAndRotateByMountPoint>d__14::System.IDisposable.Dispose()
extern void U3CMoveAndRotateByMountPointU3Ed__14_System_IDisposable_Dispose_m3D9FCEC1E9F76AA3208DE678E6DF2DC0DB8A6D91 (void);
// 0x00000093 System.Boolean AssemblyAnimationManager/<MoveAndRotateByMountPoint>d__14::MoveNext()
extern void U3CMoveAndRotateByMountPointU3Ed__14_MoveNext_m9BF3CBA940C3AE5D8149D70FD9B41515E2C3274A (void);
// 0x00000094 System.Object AssemblyAnimationManager/<MoveAndRotateByMountPoint>d__14::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveAndRotateByMountPointU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB8382E78AF30CFC27ACF7CE8D7C003BF4BB807D4 (void);
// 0x00000095 System.Void AssemblyAnimationManager/<MoveAndRotateByMountPoint>d__14::System.Collections.IEnumerator.Reset()
extern void U3CMoveAndRotateByMountPointU3Ed__14_System_Collections_IEnumerator_Reset_m0910B6ADAB14DD9159A00C7F31AB2DED22F786F9 (void);
// 0x00000096 System.Object AssemblyAnimationManager/<MoveAndRotateByMountPoint>d__14::System.Collections.IEnumerator.get_Current()
extern void U3CMoveAndRotateByMountPointU3Ed__14_System_Collections_IEnumerator_get_Current_m1487B11E1F693248CEC406CA5AC9C2DB0D325389 (void);
// 0x00000097 System.Void AssemblyAnimationManager/<>c__DisplayClass15_0::.ctor()
extern void U3CU3Ec__DisplayClass15_0__ctor_mF6E8D22BC5FFBFB118E420CC73A0C72A976B8107 (void);
// 0x00000098 System.Void AssemblyAnimationManager/<>c__DisplayClass15_0::<MoveByMountPoint>b__0(System.Single)
extern void U3CU3Ec__DisplayClass15_0_U3CMoveByMountPointU3Eb__0_mB05EF60579F08183A9971542487DC38E3615E699 (void);
// 0x00000099 System.Void AssemblyAnimationManager/<>c__DisplayClass15_0::<MoveByMountPoint>b__1()
extern void U3CU3Ec__DisplayClass15_0_U3CMoveByMountPointU3Eb__1_m0F56C3D3B73ACFAE4A74FAF6945D2277689980D7 (void);
// 0x0000009A System.Void AssemblyAnimationManager/<MoveByMountPoint>d__15::.ctor(System.Int32)
extern void U3CMoveByMountPointU3Ed__15__ctor_m982295494133219B3C01226A883BEA8EC569A4D6 (void);
// 0x0000009B System.Void AssemblyAnimationManager/<MoveByMountPoint>d__15::System.IDisposable.Dispose()
extern void U3CMoveByMountPointU3Ed__15_System_IDisposable_Dispose_m8EEC45AECB0F1B2462BD6F6476952424DA8F3A6E (void);
// 0x0000009C System.Boolean AssemblyAnimationManager/<MoveByMountPoint>d__15::MoveNext()
extern void U3CMoveByMountPointU3Ed__15_MoveNext_mCDBA24FC2646E2FCD400F35F974795C9F1E71FC7 (void);
// 0x0000009D System.Object AssemblyAnimationManager/<MoveByMountPoint>d__15::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveByMountPointU3Ed__15_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1BC767D8A33C5A375BDF4ECE75E0C38D3E1B2B95 (void);
// 0x0000009E System.Void AssemblyAnimationManager/<MoveByMountPoint>d__15::System.Collections.IEnumerator.Reset()
extern void U3CMoveByMountPointU3Ed__15_System_Collections_IEnumerator_Reset_mD64E62DB133113EFD531A989ECB1EF4731F19BEB (void);
// 0x0000009F System.Object AssemblyAnimationManager/<MoveByMountPoint>d__15::System.Collections.IEnumerator.get_Current()
extern void U3CMoveByMountPointU3Ed__15_System_Collections_IEnumerator_get_Current_m1581E82FD713A836C717677202408056D67DDAEC (void);
// 0x000000A0 System.Void AssemblyAnimationManager/<AlignParts>d__17::.ctor(System.Int32)
extern void U3CAlignPartsU3Ed__17__ctor_mB5D8DDBCF1971CBDF22EF7C5809AE214E88DE8ED (void);
// 0x000000A1 System.Void AssemblyAnimationManager/<AlignParts>d__17::System.IDisposable.Dispose()
extern void U3CAlignPartsU3Ed__17_System_IDisposable_Dispose_m40C91ED59A35B8A76984C5CD07D3FCB2788BDA6C (void);
// 0x000000A2 System.Boolean AssemblyAnimationManager/<AlignParts>d__17::MoveNext()
extern void U3CAlignPartsU3Ed__17_MoveNext_mA1B59E17C37E16B8B82BA8BA15F105A0B08FF6FD (void);
// 0x000000A3 System.Object AssemblyAnimationManager/<AlignParts>d__17::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAlignPartsU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF518E7555FF4B2DCF918B67E313FDC7CAC4AA2CC (void);
// 0x000000A4 System.Void AssemblyAnimationManager/<AlignParts>d__17::System.Collections.IEnumerator.Reset()
extern void U3CAlignPartsU3Ed__17_System_Collections_IEnumerator_Reset_m65CC8619FC89A9B803813020F70F0C87A6B1C3EB (void);
// 0x000000A5 System.Object AssemblyAnimationManager/<AlignParts>d__17::System.Collections.IEnumerator.get_Current()
extern void U3CAlignPartsU3Ed__17_System_Collections_IEnumerator_get_Current_m215BB3886B1F0545F172FBEC19FA6DE0E8BF0C6E (void);
// 0x000000A6 System.Void AssemblyAnimationManager/<InstallScrew>d__20::.ctor(System.Int32)
extern void U3CInstallScrewU3Ed__20__ctor_mFFF5D2B23896C8D28E7DE9404D1B6FEC58AB0DAF (void);
// 0x000000A7 System.Void AssemblyAnimationManager/<InstallScrew>d__20::System.IDisposable.Dispose()
extern void U3CInstallScrewU3Ed__20_System_IDisposable_Dispose_mD805219881F7C390067A14B1E9613DE9E44DE154 (void);
// 0x000000A8 System.Boolean AssemblyAnimationManager/<InstallScrew>d__20::MoveNext()
extern void U3CInstallScrewU3Ed__20_MoveNext_m11E25DE6C83D14583BB5BF047D814D128578A261 (void);
// 0x000000A9 System.Object AssemblyAnimationManager/<InstallScrew>d__20::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CInstallScrewU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mADFCEB5C97976EDD36BFE11DD4F52875C8A3298F (void);
// 0x000000AA System.Void AssemblyAnimationManager/<InstallScrew>d__20::System.Collections.IEnumerator.Reset()
extern void U3CInstallScrewU3Ed__20_System_Collections_IEnumerator_Reset_m002E0B711EBA27F381052CD9A368205A8793FCDE (void);
// 0x000000AB System.Object AssemblyAnimationManager/<InstallScrew>d__20::System.Collections.IEnumerator.get_Current()
extern void U3CInstallScrewU3Ed__20_System_Collections_IEnumerator_get_Current_m34CF5377FB92312DC437E444E2F20043D48BAEB4 (void);
// 0x000000AC System.Void AssemblyAnimationManager/<InstallNut>d__21::.ctor(System.Int32)
extern void U3CInstallNutU3Ed__21__ctor_m33D0919297A18444AA527119F6AF425631EE8F58 (void);
// 0x000000AD System.Void AssemblyAnimationManager/<InstallNut>d__21::System.IDisposable.Dispose()
extern void U3CInstallNutU3Ed__21_System_IDisposable_Dispose_m442604572C4481A33B63F08FDDD8231B86AE8803 (void);
// 0x000000AE System.Boolean AssemblyAnimationManager/<InstallNut>d__21::MoveNext()
extern void U3CInstallNutU3Ed__21_MoveNext_m7B3938DCEF64AA43B11A0AE6C0528729F57A6FD1 (void);
// 0x000000AF System.Object AssemblyAnimationManager/<InstallNut>d__21::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CInstallNutU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m574645F338EBBD73499835737B97CBC00F9D0582 (void);
// 0x000000B0 System.Void AssemblyAnimationManager/<InstallNut>d__21::System.Collections.IEnumerator.Reset()
extern void U3CInstallNutU3Ed__21_System_Collections_IEnumerator_Reset_m3B4DBFF0D24901B7C680AB35FB771F4AF80422DF (void);
// 0x000000B1 System.Object AssemblyAnimationManager/<InstallNut>d__21::System.Collections.IEnumerator.get_Current()
extern void U3CInstallNutU3Ed__21_System_Collections_IEnumerator_get_Current_m4F217576A9159CEA37515A906EED62555AF6979C (void);
// 0x000000B2 System.Void AssemblyAnimationManager/<AlignPartsSync>d__23::.ctor(System.Int32)
extern void U3CAlignPartsSyncU3Ed__23__ctor_m89125A48D794F5FCE14384806D911DA878A8E1D9 (void);
// 0x000000B3 System.Void AssemblyAnimationManager/<AlignPartsSync>d__23::System.IDisposable.Dispose()
extern void U3CAlignPartsSyncU3Ed__23_System_IDisposable_Dispose_m44C3F9883751EFD57D16BD3A4344FC439C9F5054 (void);
// 0x000000B4 System.Boolean AssemblyAnimationManager/<AlignPartsSync>d__23::MoveNext()
extern void U3CAlignPartsSyncU3Ed__23_MoveNext_m702695B033F90E38B6C660E6300DA10F66B81D19 (void);
// 0x000000B5 System.Object AssemblyAnimationManager/<AlignPartsSync>d__23::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAlignPartsSyncU3Ed__23_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF1984E7DCA1A253A1BE2FE00D871366DFA7B9C87 (void);
// 0x000000B6 System.Void AssemblyAnimationManager/<AlignPartsSync>d__23::System.Collections.IEnumerator.Reset()
extern void U3CAlignPartsSyncU3Ed__23_System_Collections_IEnumerator_Reset_mDDFD01B78693121FD30943BD678F0738412C2892 (void);
// 0x000000B7 System.Object AssemblyAnimationManager/<AlignPartsSync>d__23::System.Collections.IEnumerator.get_Current()
extern void U3CAlignPartsSyncU3Ed__23_System_Collections_IEnumerator_get_Current_m930CCA9D1CFD7BFB03B5F5A32E3EF53C5980D745 (void);
// 0x000000B8 System.Void AssemblyAnimationManager/<CompleteAssemblySequence>d__24::.ctor(System.Int32)
extern void U3CCompleteAssemblySequenceU3Ed__24__ctor_m6873ECA5E7A1B716F0EBC7343F2352A0BFEA116B (void);
// 0x000000B9 System.Void AssemblyAnimationManager/<CompleteAssemblySequence>d__24::System.IDisposable.Dispose()
extern void U3CCompleteAssemblySequenceU3Ed__24_System_IDisposable_Dispose_mBFA49DF4E24E707B41CB3E9103F535A909F97718 (void);
// 0x000000BA System.Boolean AssemblyAnimationManager/<CompleteAssemblySequence>d__24::MoveNext()
extern void U3CCompleteAssemblySequenceU3Ed__24_MoveNext_mF07AF18D2B7FE9D8FDAEC0CB35E06D72BEA061CE (void);
// 0x000000BB System.Object AssemblyAnimationManager/<CompleteAssemblySequence>d__24::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CCompleteAssemblySequenceU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6A0F91B1BA366E512665D752B47AB37752D22CBF (void);
// 0x000000BC System.Void AssemblyAnimationManager/<CompleteAssemblySequence>d__24::System.Collections.IEnumerator.Reset()
extern void U3CCompleteAssemblySequenceU3Ed__24_System_Collections_IEnumerator_Reset_m4E60FE5DEFC1708BF1B59877D3C882F2689A7F10 (void);
// 0x000000BD System.Object AssemblyAnimationManager/<CompleteAssemblySequence>d__24::System.Collections.IEnumerator.get_Current()
extern void U3CCompleteAssemblySequenceU3Ed__24_System_Collections_IEnumerator_get_Current_mE1E14AAB8061094B63E88C56F85DB287B36BFF7D (void);
// 0x000000BE AssemblyAnimationManager AssemblyAnimationService::get_AnimationManager()
extern void AssemblyAnimationService_get_AnimationManager_m2390D3B47AAE7E2447E4C67572CB7C27DC1B3AC5 (void);
// 0x000000BF System.Void AssemblyAnimationService::Awake()
extern void AssemblyAnimationService_Awake_mB7E5899A0B2D0BAFC2C5CB6A6A0A47FE12DAAC93 (void);
// 0x000000C0 System.Void AssemblyAnimationService::Start()
extern void AssemblyAnimationService_Start_m2F6680EF6EF0EF87313B5852350D6CD2AA65A9E4 (void);
// 0x000000C1 System.Void AssemblyAnimationService::AlignPartsPublic(AssemblyPart,AssemblyPart,System.Int32,System.Int32,System.Single)
extern void AssemblyAnimationService_AlignPartsPublic_mA8042FD975498F9879E139EE96089CBA5CADB774 (void);
// 0x000000C2 System.Collections.IEnumerator AssemblyAnimationService::MoveScrewImprovedPublic(AssemblyPart,UnityEngine.Transform,UnityEngine.Vector3,System.Int32)
extern void AssemblyAnimationService_MoveScrewImprovedPublic_m60D285E03B83A1FB2FD0A5D91BCCFECBC55EFB9B (void);
// 0x000000C3 System.Collections.IEnumerator AssemblyAnimationService::MoveNutImprovedPublic(AssemblyPart,UnityEngine.Transform,UnityEngine.Vector3,System.Int32)
extern void AssemblyAnimationService_MoveNutImprovedPublic_mDE246AD0253F5EB806E42CB521680F08C3C06AE4 (void);
// 0x000000C4 UnityEngine.Vector3 AssemblyAnimationService::GetAxisDirection(UnityEngine.Transform)
extern void AssemblyAnimationService_GetAxisDirection_m16F7FB7DF40731F203B4972B7013BEC0AABEB1D5 (void);
// 0x000000C5 System.Collections.IEnumerator AssemblyAnimationService::MoveScrewImproved(AssemblyPart,UnityEngine.Transform,UnityEngine.Vector3,System.Int32)
extern void AssemblyAnimationService_MoveScrewImproved_m84B33DE65006D0CC6399AFFAC3DD9DC7333C1E35 (void);
// 0x000000C6 System.Collections.IEnumerator AssemblyAnimationService::MoveNutImproved(AssemblyPart,UnityEngine.Transform,UnityEngine.Vector3,System.Int32)
extern void AssemblyAnimationService_MoveNutImproved_m6DF3B64C6188156C89239B005BBC0AECB6EFCCB8 (void);
// 0x000000C7 System.Void AssemblyAnimationService::.ctor()
extern void AssemblyAnimationService__ctor_mFE7BB99BDB3CC7DA6229F2F87F925E7C8378C41B (void);
// 0x000000C8 System.Void AssemblyAnimationService/<MoveScrewImproved>d__14::.ctor(System.Int32)
extern void U3CMoveScrewImprovedU3Ed__14__ctor_mF7CAD27EE50C47CE7D9B100CA08BBD1818CDFA17 (void);
// 0x000000C9 System.Void AssemblyAnimationService/<MoveScrewImproved>d__14::System.IDisposable.Dispose()
extern void U3CMoveScrewImprovedU3Ed__14_System_IDisposable_Dispose_m8C2780039594A2BA7CABE0108A0BDDA2738D6EE6 (void);
// 0x000000CA System.Boolean AssemblyAnimationService/<MoveScrewImproved>d__14::MoveNext()
extern void U3CMoveScrewImprovedU3Ed__14_MoveNext_mA0D4AA02C62875E2B30B4B68AE49BD83FEDDB258 (void);
// 0x000000CB System.Object AssemblyAnimationService/<MoveScrewImproved>d__14::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveScrewImprovedU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5E42E26C3E4B6EB3606DC959E3B2ED1F247538F9 (void);
// 0x000000CC System.Void AssemblyAnimationService/<MoveScrewImproved>d__14::System.Collections.IEnumerator.Reset()
extern void U3CMoveScrewImprovedU3Ed__14_System_Collections_IEnumerator_Reset_mB3378F9CAB2D4FBD28B57B19AB2E4A09552342EA (void);
// 0x000000CD System.Object AssemblyAnimationService/<MoveScrewImproved>d__14::System.Collections.IEnumerator.get_Current()
extern void U3CMoveScrewImprovedU3Ed__14_System_Collections_IEnumerator_get_Current_mCA66715AE837146DF72DEEA7DDA288A029C70BFD (void);
// 0x000000CE System.Void AssemblyAnimationService/<MoveNutImproved>d__15::.ctor(System.Int32)
extern void U3CMoveNutImprovedU3Ed__15__ctor_mDD0643FFE94EA0DE1C955C8BF1A4E3C6A5A40355 (void);
// 0x000000CF System.Void AssemblyAnimationService/<MoveNutImproved>d__15::System.IDisposable.Dispose()
extern void U3CMoveNutImprovedU3Ed__15_System_IDisposable_Dispose_mDADC295F6FF625100DC91523F1B1D93D9A05CD28 (void);
// 0x000000D0 System.Boolean AssemblyAnimationService/<MoveNutImproved>d__15::MoveNext()
extern void U3CMoveNutImprovedU3Ed__15_MoveNext_m7ACA9DC3B5514A5366E06476CA100C79F95070CF (void);
// 0x000000D1 System.Object AssemblyAnimationService/<MoveNutImproved>d__15::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveNutImprovedU3Ed__15_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m81FDD57950948E47486F944A75DB02644169422C (void);
// 0x000000D2 System.Void AssemblyAnimationService/<MoveNutImproved>d__15::System.Collections.IEnumerator.Reset()
extern void U3CMoveNutImprovedU3Ed__15_System_Collections_IEnumerator_Reset_mDB3E2043E8BEF95FF79784AA22A37105DF1CB65B (void);
// 0x000000D3 System.Object AssemblyAnimationService/<MoveNutImproved>d__15::System.Collections.IEnumerator.get_Current()
extern void U3CMoveNutImprovedU3Ed__15_System_Collections_IEnumerator_get_Current_m532C2F3BA34DD38F05EBF7C256BABA6533E37531 (void);
// 0x000000D4 UnityEngine.Transform IAssemblyPart::get_PartTransform()
// 0x000000D5 System.String IAssemblyPart::get_PartName()
// 0x000000D6 UnityEngine.Transform[] IAssemblyPart::get_ReferencePoints()
// 0x000000D7 UnityEngine.Transform IAssemblyPart::GetReferencePoint(System.Int32)
// 0x000000D8 System.Int32 IAssemblyPart::get_ReferencePointCount()
// 0x000000D9 System.Boolean IAssemblyPart::get_HasReferencePoints()
// 0x000000DA UnityEngine.Transform IAssemblyPart::get_MountPoint()
// 0x000000DB UnityEngine.Transform IAssemblyPart::GetMountPoint(System.Int32)
// 0x000000DC UnityEngine.Transform AssemblyPart::get_PartTransform()
extern void AssemblyPart_get_PartTransform_m4DBAD54DBC01EA8AA0B9CE3F213F16BF39F4185B (void);
// 0x000000DD System.String AssemblyPart::get_PartName()
extern void AssemblyPart_get_PartName_m6214ABEAAF046BDED82DEF234113EC5242D85403 (void);
// 0x000000DE PartType AssemblyPart::get_Type()
extern void AssemblyPart_get_Type_m7F1E8F177BCDAF6F36A237322F53A5B0923E4461 (void);
// 0x000000DF UnityEngine.Transform[] AssemblyPart::get_ReferencePoints()
extern void AssemblyPart_get_ReferencePoints_m78FB08E4A8A88DB9C21CE9D33063B7186F578282 (void);
// 0x000000E0 System.Boolean AssemblyPart::get_HasReferencePoints()
extern void AssemblyPart_get_HasReferencePoints_m4101AA83A26052106AD881D2DC804625891542F5 (void);
// 0x000000E1 System.Int32 AssemblyPart::get_ReferencePointCount()
extern void AssemblyPart_get_ReferencePointCount_mF33DFB60760BFAF332ABD9E985F0CD490B7C9B3B (void);
// 0x000000E2 UnityEngine.Transform AssemblyPart::get_MountPoint()
extern void AssemblyPart_get_MountPoint_mD5D437BBFF699DBD34FB735B8A5C2A9B873CDBEC (void);
// 0x000000E3 UnityEngine.Transform AssemblyPart::GetReferencePoint(System.Int32)
extern void AssemblyPart_GetReferencePoint_mA201236E27083AF2890401B12D8DD8EAE59A319A (void);
// 0x000000E4 UnityEngine.Transform AssemblyPart::GetReferencePointByName(System.String)
extern void AssemblyPart_GetReferencePointByName_mD7F8DDB6004E03A542114CC8A0741B3516BC5237 (void);
// 0x000000E5 UnityEngine.Transform AssemblyPart::GetMountPoint(System.Int32)
extern void AssemblyPart_GetMountPoint_m2222F2090704F0CDC758B755808AEEC450942A74 (void);
// 0x000000E6 System.Void AssemblyPart::OnValidate()
extern void AssemblyPart_OnValidate_m38D200EFECAD3B6FF377AC38D18AAC318BDE752E (void);
// 0x000000E7 System.Void AssemblyPart::ValidateReferencePoints()
extern void AssemblyPart_ValidateReferencePoints_m89909ADC56847528A6D64C919908BA4011F9D627 (void);
// 0x000000E8 System.Void AssemblyPart::OnDrawGizmosSelected()
extern void AssemblyPart_OnDrawGizmosSelected_mA4CDB6064D0D756A45BA6A6803030B952DCABBF9 (void);
// 0x000000E9 System.Void AssemblyPart::DrawReferencePointGizmo(UnityEngine.Transform,UnityEngine.Color,System.Int32)
extern void AssemblyPart_DrawReferencePointGizmo_m1CCA44422B01AFC722B02C7A9032345CC8264F44 (void);
// 0x000000EA System.Void AssemblyPart::.ctor()
extern void AssemblyPart__ctor_m3A868BA0F7EC280E2368E4F589546FDD3695782A (void);
// 0x000000EB AssemblyStep AssemblyStep::CreateDirect(System.String,System.String,System.String,System.String)
extern void AssemblyStep_CreateDirect_m12C41BB8FF5AC02BEF0B44BD7CDFBD717F12E0E8 (void);
// 0x000000EC AssemblyStep AssemblyStep::CreateWithScrew(System.String,System.String,System.String,System.String,System.String,System.String)
extern void AssemblyStep_CreateWithScrew_m34C4D028BD6F7243EA8B441F9A66089668760BA4 (void);
// 0x000000ED AssemblyStep AssemblyStep::Create(System.String,System.String,System.String,System.String,System.String,AssemblyStep/FastenerInfo)
extern void AssemblyStep_Create_m6DB78A29CAB7CEA6B3615B08A2B35907A4165D7A (void);
// 0x000000EE System.Void AssemblyStep::AddMountPoint(System.String,System.String,System.String,System.String)
extern void AssemblyStep_AddMountPoint_m194EA8814100C8F34D6BB078603A47010B7B707D (void);
// 0x000000EF System.String AssemblyStep::ToString()
extern void AssemblyStep_ToString_m320E7A2E2902A63202043DA88272743C043F82EB (void);
// 0x000000F0 System.Boolean AssemblyStep::IsValid()
extern void AssemblyStep_IsValid_mF9FCF7AB253203EA0B1C0D39E09B71CE913F6A41 (void);
// 0x000000F1 System.Boolean AssemblyStep::RequiresFastener()
extern void AssemblyStep_RequiresFastener_mFD703603DDAD3E14B20E48D70E9EE9A5E3988974 (void);
// 0x000000F2 System.Boolean AssemblyStep::HasAdditionalMountPoints()
extern void AssemblyStep_HasAdditionalMountPoints_m5341C821D4FE304D28E15E715126EF409BEA18E3 (void);
// 0x000000F3 AssemblyStep/FastenerInfo AssemblyStep/FastenerInfo::get_Empty()
extern void FastenerInfo_get_Empty_mB6E6BDF922B577CDD26EF4EAFE797397DB6D0F09 (void);
// 0x000000F4 AssemblyStep/FastenerInfo AssemblyStep/FastenerInfo::CreateScrew(System.String,System.String)
extern void FastenerInfo_CreateScrew_m81C9F9116D2CFC59BA2634D6D3365B9927E530C1 (void);
// 0x000000F5 System.String AssemblyStep/FastenerInfo::GetScrewPrefabName(System.String)
extern void FastenerInfo_GetScrewPrefabName_mF8FF0A3357E0D93D0E92BEAB58C43B420954CC6F (void);
// 0x000000F6 System.String AssemblyStep/FastenerInfo::GetNutPrefabName(System.String)
extern void FastenerInfo_GetNutPrefabName_mE1B530F590FD507BC8E6580B643F4362D2853EAE (void);
// 0x000000F7 AssemblyStep/AdditionalMountPoint AssemblyStep/AdditionalMountPoint::Create(System.String,System.String,System.String,AssemblyStep/FastenerInfo)
extern void AdditionalMountPoint_Create_m599AEDDB62836E0EE9104830BF83B4AE2D3D96C0 (void);
// 0x000000F8 System.Collections.IEnumerator IAssemblyDataProvider::LoadAssemblySteps(System.String,System.Action`1<System.Collections.Generic.List`1<AssemblyStepData>>)
// 0x000000F9 System.Collections.IEnumerator IAssemblyDataProvider::TestConnection(System.Action`1<System.Boolean>)
// 0x000000FA System.String IAssemblyDataProvider::get_DataSourceType()
// 0x000000FB System.Boolean IAssemblyDataProvider::get_IsConnected()
// 0x000000FC System.String AssemblyStepData::ToString()
extern void AssemblyStepData_ToString_mE8C413BE6001E1478285B8AA28545AB3EBC65360 (void);
// 0x000000FD System.String ExternalSystemDataProvider::get_DataSourceType()
extern void ExternalSystemDataProvider_get_DataSourceType_m14CDFE335CB35D4AD1AC1DE3BBB0B2E25D4977DA (void);
// 0x000000FE System.Boolean ExternalSystemDataProvider::get_IsConnected()
extern void ExternalSystemDataProvider_get_IsConnected_m428A4A6807AE65917050F1D4BD1FDE5384743B94 (void);
// 0x000000FF System.Void ExternalSystemDataProvider::set_IsConnected(System.Boolean)
extern void ExternalSystemDataProvider_set_IsConnected_m6D819239718C5DCC0C40836B3077E444E3EC2178 (void);
// 0x00000100 System.Void ExternalSystemDataProvider::Start()
extern void ExternalSystemDataProvider_Start_mC5926556AE446A1DC315041738E77D2C3977BF05 (void);
// 0x00000101 System.Collections.IEnumerator ExternalSystemDataProvider::LoadAssemblySteps(System.String,System.Action`1<System.Collections.Generic.List`1<AssemblyStepData>>)
extern void ExternalSystemDataProvider_LoadAssemblySteps_m1472E0480FA42AFF7360021D4F824FB8C3D5A452 (void);
// 0x00000102 System.Collections.IEnumerator ExternalSystemDataProvider::TestConnection(System.Action`1<System.Boolean>)
extern void ExternalSystemDataProvider_TestConnection_mDB39828320D3F62AB5BCBC56EB6EF8248B5CE976 (void);
// 0x00000103 System.Collections.IEnumerator ExternalSystemDataProvider::RequestAssemblyDataFromExternalSystem(System.String,System.Action`2<System.Boolean,System.Collections.Generic.List`1<AssemblyStepData>>)
extern void ExternalSystemDataProvider_RequestAssemblyDataFromExternalSystem_mC61C99CC5DB66CBE28CB449494EAF4F4E8AB73EE (void);
// 0x00000104 System.Collections.Generic.List`1<AssemblyStepData> ExternalSystemDataProvider::ParseAssemblyDataFromJson(System.String)
extern void ExternalSystemDataProvider_ParseAssemblyDataFromJson_m8DCB25C7EAC9468A6EE2E03D4C0250320A667893 (void);
// 0x00000105 System.Collections.Generic.List`1<AssemblyStepData> ExternalSystemDataProvider::LoadFallbackData(System.String)
extern void ExternalSystemDataProvider_LoadFallbackData_m1B98A53A671D82EA503AE632E1B5415C5DDE1AC9 (void);
// 0x00000106 System.Void ExternalSystemDataProvider::SetAssemblyData(System.String,System.Collections.Generic.List`1<AssemblyStepData>)
extern void ExternalSystemDataProvider_SetAssemblyData_m3B14BDC51384FD61843D7F9DC1BF8CF9F7B8A166 (void);
// 0x00000107 System.Void ExternalSystemDataProvider::ClearCache()
extern void ExternalSystemDataProvider_ClearCache_m9D6121B0F4F95CBF9C21A89CB25B173D131E83C7 (void);
// 0x00000108 System.Void ExternalSystemDataProvider::.ctor()
extern void ExternalSystemDataProvider__ctor_m9590CBEF733E2D7E55508B2FB9B25C5DBFD152BD (void);
// 0x00000109 System.Void ExternalSystemDataProvider::<Start>b__11_0(System.Boolean)
extern void ExternalSystemDataProvider_U3CStartU3Eb__11_0_mA2C973F8D30BE3B3D3D798DCCC3516D0BCFDDCCB (void);
// 0x0000010A System.Void ExternalSystemDataProvider/<>c__DisplayClass12_0::.ctor()
extern void U3CU3Ec__DisplayClass12_0__ctor_mC73E03CEA17878AD793BE79F4E4B3CAE982D06D2 (void);
// 0x0000010B System.Void ExternalSystemDataProvider/<>c__DisplayClass12_0::<LoadAssemblySteps>b__0(System.Boolean,System.Collections.Generic.List`1<AssemblyStepData>)
extern void U3CU3Ec__DisplayClass12_0_U3CLoadAssemblyStepsU3Eb__0_m16AF587FC247F8F9393586C243453E0879517F8D (void);
// 0x0000010C System.Void ExternalSystemDataProvider/<LoadAssemblySteps>d__12::.ctor(System.Int32)
extern void U3CLoadAssemblyStepsU3Ed__12__ctor_m3C4467A00F1A52FA235CC197F8154C46821806AF (void);
// 0x0000010D System.Void ExternalSystemDataProvider/<LoadAssemblySteps>d__12::System.IDisposable.Dispose()
extern void U3CLoadAssemblyStepsU3Ed__12_System_IDisposable_Dispose_m823495F27CE6FA0DDB5A212DE42F0B86008943C8 (void);
// 0x0000010E System.Boolean ExternalSystemDataProvider/<LoadAssemblySteps>d__12::MoveNext()
extern void U3CLoadAssemblyStepsU3Ed__12_MoveNext_mE99C821ED07EFE7AF1BBB874E479E5033CABF493 (void);
// 0x0000010F System.Object ExternalSystemDataProvider/<LoadAssemblySteps>d__12::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CLoadAssemblyStepsU3Ed__12_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0E4CE5851A7B106D55ADAD5F7E9F1C6D597A441D (void);
// 0x00000110 System.Void ExternalSystemDataProvider/<LoadAssemblySteps>d__12::System.Collections.IEnumerator.Reset()
extern void U3CLoadAssemblyStepsU3Ed__12_System_Collections_IEnumerator_Reset_mAD9121A9CE9C097EE3A7F42FD4D274661A6BC2A3 (void);
// 0x00000111 System.Object ExternalSystemDataProvider/<LoadAssemblySteps>d__12::System.Collections.IEnumerator.get_Current()
extern void U3CLoadAssemblyStepsU3Ed__12_System_Collections_IEnumerator_get_Current_m04E3677891C88CDBD5394C91BD4E70518891BCC8 (void);
// 0x00000112 System.Void ExternalSystemDataProvider/<TestConnection>d__13::.ctor(System.Int32)
extern void U3CTestConnectionU3Ed__13__ctor_mED859D27EA19C44ED202C9A522933A077C6B2122 (void);
// 0x00000113 System.Void ExternalSystemDataProvider/<TestConnection>d__13::System.IDisposable.Dispose()
extern void U3CTestConnectionU3Ed__13_System_IDisposable_Dispose_m64E944147C46A76E1472CEE05499759C04CF0925 (void);
// 0x00000114 System.Boolean ExternalSystemDataProvider/<TestConnection>d__13::MoveNext()
extern void U3CTestConnectionU3Ed__13_MoveNext_m17B3C0EFCABCFD6194690E8D5E1EA25D24900CDC (void);
// 0x00000115 System.Void ExternalSystemDataProvider/<TestConnection>d__13::<>m__Finally1()
extern void U3CTestConnectionU3Ed__13_U3CU3Em__Finally1_mF77D5135A870834B987AED604F7F619AD1DC645B (void);
// 0x00000116 System.Object ExternalSystemDataProvider/<TestConnection>d__13::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CTestConnectionU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m57C0DAED84E9128B87049D4DB24DC7FFEF2FF4CF (void);
// 0x00000117 System.Void ExternalSystemDataProvider/<TestConnection>d__13::System.Collections.IEnumerator.Reset()
extern void U3CTestConnectionU3Ed__13_System_Collections_IEnumerator_Reset_m0109DE10A1A3A1B19A8D19D94B86B6CA750FF7F7 (void);
// 0x00000118 System.Object ExternalSystemDataProvider/<TestConnection>d__13::System.Collections.IEnumerator.get_Current()
extern void U3CTestConnectionU3Ed__13_System_Collections_IEnumerator_get_Current_m0A71964B7E2DB3173D0C01DCF06CE10A79F61A59 (void);
// 0x00000119 System.Void ExternalSystemDataProvider/<RequestAssemblyDataFromExternalSystem>d__14::.ctor(System.Int32)
extern void U3CRequestAssemblyDataFromExternalSystemU3Ed__14__ctor_m646BB5DCB05AFE7A08F5EA4D5666CF4E426C0FC4 (void);
// 0x0000011A System.Void ExternalSystemDataProvider/<RequestAssemblyDataFromExternalSystem>d__14::System.IDisposable.Dispose()
extern void U3CRequestAssemblyDataFromExternalSystemU3Ed__14_System_IDisposable_Dispose_mD38B1871DF3B73A3E411F2E513100C439DA7CBF2 (void);
// 0x0000011B System.Boolean ExternalSystemDataProvider/<RequestAssemblyDataFromExternalSystem>d__14::MoveNext()
extern void U3CRequestAssemblyDataFromExternalSystemU3Ed__14_MoveNext_m9011DA563EDA71964BD5306F8F12A8549F6E7956 (void);
// 0x0000011C System.Void ExternalSystemDataProvider/<RequestAssemblyDataFromExternalSystem>d__14::<>m__Finally1()
extern void U3CRequestAssemblyDataFromExternalSystemU3Ed__14_U3CU3Em__Finally1_mDBFECEC7CE9D75CCBE4A55DABA1B94ADF841E02D (void);
// 0x0000011D System.Object ExternalSystemDataProvider/<RequestAssemblyDataFromExternalSystem>d__14::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRequestAssemblyDataFromExternalSystemU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7CF71093660F98A17DFCDB1CF90366D9783825BC (void);
// 0x0000011E System.Void ExternalSystemDataProvider/<RequestAssemblyDataFromExternalSystem>d__14::System.Collections.IEnumerator.Reset()
extern void U3CRequestAssemblyDataFromExternalSystemU3Ed__14_System_Collections_IEnumerator_Reset_m5A7B8010C71A615ED9E3C3B6F59146359ECEBC9D (void);
// 0x0000011F System.Object ExternalSystemDataProvider/<RequestAssemblyDataFromExternalSystem>d__14::System.Collections.IEnumerator.get_Current()
extern void U3CRequestAssemblyDataFromExternalSystemU3Ed__14_System_Collections_IEnumerator_get_Current_m73D5DF270A82D9EBDCAD575060CFED95C809EF74 (void);
// 0x00000120 System.Void Neo4jAssemblyController::add_OnPartSelected(System.Action`1<System.String>)
extern void Neo4jAssemblyController_add_OnPartSelected_m4D16A619571CE65FD08CFEDDBE34D00E3469E194 (void);
// 0x00000121 System.Void Neo4jAssemblyController::remove_OnPartSelected(System.Action`1<System.String>)
extern void Neo4jAssemblyController_remove_OnPartSelected_m35A3259406D73B2A30DCA1699D81B0D376B7426F (void);
// 0x00000122 System.Void Neo4jAssemblyController::add_OnAssemblyStepsLoaded(System.Action`1<System.Int32>)
extern void Neo4jAssemblyController_add_OnAssemblyStepsLoaded_mEF161A9592C509DEC5BEE31A2DAB43AA2FE177E3 (void);
// 0x00000123 System.Void Neo4jAssemblyController::remove_OnAssemblyStepsLoaded(System.Action`1<System.Int32>)
extern void Neo4jAssemblyController_remove_OnAssemblyStepsLoaded_mB8CAA0AEC52E6FFED644C55D5B8FC8FF6A513C2C (void);
// 0x00000124 System.Void Neo4jAssemblyController::add_OnStepExecuted(System.Action`3<System.Int32,System.Int32,System.String>)
extern void Neo4jAssemblyController_add_OnStepExecuted_mA0F70E2031DA9BE90755EEB10F1CC206475F157F (void);
// 0x00000125 System.Void Neo4jAssemblyController::remove_OnStepExecuted(System.Action`3<System.Int32,System.Int32,System.String>)
extern void Neo4jAssemblyController_remove_OnStepExecuted_mFF08276D8FE610DB524C365FD2D26D3DA6D2293E (void);
// 0x00000126 System.Void Neo4jAssemblyController::add_OnAssemblyCompleted(System.Action)
extern void Neo4jAssemblyController_add_OnAssemblyCompleted_m2BAC78501536C56C9D59EBD9AD15360EE42BE25C (void);
// 0x00000127 System.Void Neo4jAssemblyController::remove_OnAssemblyCompleted(System.Action)
extern void Neo4jAssemblyController_remove_OnAssemblyCompleted_m6124236E0195C0B5275A79A7094326324CE87755 (void);
// 0x00000128 System.Single Neo4jAssemblyController::get_AnimationDuration()
extern void Neo4jAssemblyController_get_AnimationDuration_mD65987078E3F5628F74EDBC6FA7FB8D1AC9A7FA6 (void);
// 0x00000129 System.Boolean Neo4jAssemblyController::HasRemainingSteps()
extern void Neo4jAssemblyController_HasRemainingSteps_mA3C07C4AA8D348AF02248CDFCA02C4B860395570 (void);
// 0x0000012A System.Single Neo4jAssemblyController::get_AnimationSpeedRate()
extern void Neo4jAssemblyController_get_AnimationSpeedRate_m4F8403F7507EF50FFC1AF78331AAD835FBCE46FC (void);
// 0x0000012B System.Void Neo4jAssemblyController::set_AnimationSpeedRate(System.Single)
extern void Neo4jAssemblyController_set_AnimationSpeedRate_mEC2065E1CE70858518AACC29AF215DAD353C069F (void);
// 0x0000012C System.Void Neo4jAssemblyController::Start()
extern void Neo4jAssemblyController_Start_m3AECDA8E796F8C997D6FCBF00A3854965D62EE8D (void);
// 0x0000012D System.Void Neo4jAssemblyController::InitializeDataSource()
extern void Neo4jAssemblyController_InitializeDataSource_m4A02EBD71B98D99A666978180F80837642FB5544 (void);
// 0x0000012E System.Void Neo4jAssemblyController::InitializeVRInput()
extern void Neo4jAssemblyController_InitializeVRInput_mF5AAAE73C17E0A6C8AE2B105BDE77F59AE76DB23 (void);
// 0x0000012F System.Void Neo4jAssemblyController::Update()
extern void Neo4jAssemblyController_Update_m55852F29A8A74946F9E1C94B8F7772DDBAECA3A5 (void);
// 0x00000130 System.Void Neo4jAssemblyController::HandleVRPartSelection(AssemblyPart)
extern void Neo4jAssemblyController_HandleVRPartSelection_m8D20EC8D130555634039D85720CC60A9C2500E88 (void);
// 0x00000131 System.Collections.IEnumerator Neo4jAssemblyController::LoadAssemblyStepsFromExternalSource(System.String)
extern void Neo4jAssemblyController_LoadAssemblyStepsFromExternalSource_m220FD7EEB23B401993043C74AAB99DD339DB643E (void);
// 0x00000132 System.Void Neo4jAssemblyController::ReceiveExternalAssemblyData(System.String,System.String)
extern void Neo4jAssemblyController_ReceiveExternalAssemblyData_mADA684E475D76B1FB3BDD135696FAA1AB1F5E521 (void);
// 0x00000133 System.Collections.Generic.List`1<AssemblyStepData> Neo4jAssemblyController::ParseExternalAssemblyData(System.String)
extern void Neo4jAssemblyController_ParseExternalAssemblyData_m539B7AC68C9A8C7C0F0DC85ECCC0E54AD54500D1 (void);
// 0x00000134 System.Void Neo4jAssemblyController::ProcessExternalAssemblySteps(System.Collections.Generic.List`1<AssemblyStepData>)
extern void Neo4jAssemblyController_ProcessExternalAssemblySteps_m9E85D256E06AB803A13D72A63B39F08DEB1BE0CF (void);
// 0x00000135 System.Void Neo4jAssemblyController::RegisterEvents()
extern void Neo4jAssemblyController_RegisterEvents_m25E701F38AB55F43F3C27000F68194C07E2A5296 (void);
// 0x00000136 System.Void Neo4jAssemblyController::UnregisterEvents()
extern void Neo4jAssemblyController_UnregisterEvents_mFAA72A59582464448EC861E7F37EA49AD36B925E (void);
// 0x00000137 System.Void Neo4jAssemblyController::InitializeVRSystem()
extern void Neo4jAssemblyController_InitializeVRSystem_mAC7B055F97C12134DB0841F1A985BE7D00FA3AC8 (void);
// 0x00000138 System.Collections.IEnumerator Neo4jAssemblyController::WaitForVRPreparation(AssemblyPart,AssemblyPart,System.String)
extern void Neo4jAssemblyController_WaitForVRPreparation_m54E9E7B24E841DD3B151F5CD649A72FE3D5C1BDA (void);
// 0x00000139 System.Void Neo4jAssemblyController::OnDestroy()
extern void Neo4jAssemblyController_OnDestroy_mAEAF19F1505C989BC014E45A134099F7BAD6C13A (void);
// 0x0000013A System.Void Neo4jAssemblyController::InitializePartMapping()
extern void Neo4jAssemblyController_InitializePartMapping_m682509B14C0F5C5E53EC3D2C7AE87D9BF4988C1D (void);
// 0x0000013B System.Void Neo4jAssemblyController::SaveAllPartsInitialState()
extern void Neo4jAssemblyController_SaveAllPartsInitialState_mF8E0AE3F5A1EF7EB7D69D072BEAD9583623F8FD2 (void);
// 0x0000013C System.Void Neo4jAssemblyController::SaveAllFastenersInitialState()
extern void Neo4jAssemblyController_SaveAllFastenersInitialState_m76BBA96BD40D615670BF2C04E489AEA4811E8582 (void);
// 0x0000013D System.Void Neo4jAssemblyController::RestoreAllPartsInitialState()
extern void Neo4jAssemblyController_RestoreAllPartsInitialState_mF422227C574FAE5053B7A1D338846B44E6208405 (void);
// 0x0000013E System.Void Neo4jAssemblyController::RestoreAllFastenersInitialState()
extern void Neo4jAssemblyController_RestoreAllFastenersInitialState_mAA05BEFC1C4A70827ECEA501FE997AC0D8C4D959 (void);
// 0x0000013F System.Void Neo4jAssemblyController::SaveStepPartsInitialState(Neo4jAssemblyController/AssemblyStep)
extern void Neo4jAssemblyController_SaveStepPartsInitialState_mC896D7D191AD4AF4378380D20610E7740D4AD93A (void);
// 0x00000140 System.Void Neo4jAssemblyController::RestoreStepPartsInitialState()
extern void Neo4jAssemblyController_RestoreStepPartsInitialState_m4E32E569376ECCA5FA209A3CDC143684E4FD7FB1 (void);
// 0x00000141 System.Void Neo4jAssemblyController::SelectPartWithRaycast()
extern void Neo4jAssemblyController_SelectPartWithRaycast_m81F9DEE660CCC6206D273753209E8A25D69F0AAA (void);
// 0x00000142 System.Void Neo4jAssemblyController::ResetAssemblySteps()
extern void Neo4jAssemblyController_ResetAssemblySteps_mC66B1EE8CE40B790B643BAB96909241CC8B03381 (void);
// 0x00000143 System.Collections.IEnumerator Neo4jAssemblyController::QueryAssemblyRelationships(System.String)
extern void Neo4jAssemblyController_QueryAssemblyRelationships_m510B66812C23BBC224DA63FF670E640873B240A7 (void);
// 0x00000144 System.Void Neo4jAssemblyController::ProcessAssemblyRelationships(System.String)
extern void Neo4jAssemblyController_ProcessAssemblyRelationships_mFF290E89F175D17B687084ACDE6CA8EDAF88641F (void);
// 0x00000145 System.Void Neo4jAssemblyController::ExecuteNextStep()
extern void Neo4jAssemblyController_ExecuteNextStep_mACFB3484B07642D7E1AB5DD2FCEF0BF28EFD0C5A (void);
// 0x00000146 System.Void Neo4jAssemblyController::ReplayLastStep()
extern void Neo4jAssemblyController_ReplayLastStep_mE733ACCC650508A829BD8BC1B4B6583E4D03195D (void);
// 0x00000147 System.Collections.IEnumerator Neo4jAssemblyController::ExecuteNextAssemblyStepCoroutine()
extern void Neo4jAssemblyController_ExecuteNextAssemblyStepCoroutine_m3CF8AF855E6F76DF59E55F173D5D9A09B96035EA (void);
// 0x00000148 System.Collections.IEnumerator Neo4jAssemblyController::ReplayAssemblyStepCoroutine(Neo4jAssemblyController/AssemblyStep)
extern void Neo4jAssemblyController_ReplayAssemblyStepCoroutine_m3297ABAA72F89163A828E1D6124F9FC1A330AF5E (void);
// 0x00000149 System.Void Neo4jAssemblyController::ResetAssembly()
extern void Neo4jAssemblyController_ResetAssembly_mD59C8122A2FB6FB3EFEC64F7CD6269AC2E6EFE23 (void);
// 0x0000014A System.Collections.IEnumerator Neo4jAssemblyController::InstallFastener(Neo4jAssemblyController/AssemblyStep/FastenerInfo,AssemblyPart,System.Int32,System.Int32)
extern void Neo4jAssemblyController_InstallFastener_m9359B647D3FE9CA58FA7E9178FEACEF6EEC6B353 (void);
// 0x0000014B System.String Neo4jAssemblyController::GetScrewPrefabName(System.String)
extern void Neo4jAssemblyController_GetScrewPrefabName_m11120109918806CCD7A9C806AEA1B0FDCF201DC3 (void);
// 0x0000014C System.String Neo4jAssemblyController::GetNutPrefabName(System.String)
extern void Neo4jAssemblyController_GetNutPrefabName_m5E19A285FC2691F9273945519D3D4AF0ECA55D25 (void);
// 0x0000014D AssemblyPart Neo4jAssemblyController::GetOrCreateFastener(System.String,System.Int32)
extern void Neo4jAssemblyController_GetOrCreateFastener_m5668C21E677AD73F08DB03CD0BB4A446D65B32D1 (void);
// 0x0000014E AssemblyPart Neo4jAssemblyController::GetFastenerFromScene(System.String,System.Int32)
extern void Neo4jAssemblyController_GetFastenerFromScene_m71CCB551C77E2F8132E2178C1E00F39E20ABB059 (void);
// 0x0000014F System.Void Neo4jAssemblyController::CleanupFasteners()
extern void Neo4jAssemblyController_CleanupFasteners_m2082E46B24B9678AB1A28D93A0BECE9EA2BA2C9C (void);
// 0x00000150 System.Void Neo4jAssemblyController::ValidateReferencePointMappings()
extern void Neo4jAssemblyController_ValidateReferencePointMappings_mD89452E7D5999153B80C25E96FC5F98824180A0A (void);
// 0x00000151 System.Int32 Neo4jAssemblyController::GetReferencePointIndex(System.String,System.String)
extern void Neo4jAssemblyController_GetReferencePointIndex_m0D83B603692A1137C3BC03DC391D1CC85EE8B728 (void);
// 0x00000152 System.Void Neo4jAssemblyController::.ctor()
extern void Neo4jAssemblyController__ctor_m4ADC9376EA623CE58D22058DA0C60245D51BBA9F (void);
// 0x00000153 System.Void Neo4jAssemblyController/AssemblyStep::.ctor(System.String,System.String,System.String,System.String)
extern void AssemblyStep__ctor_m40EF831184A0632EF7E7D625F994BB6378CEE04E (void);
// 0x00000154 System.Void Neo4jAssemblyController/AssemblyStep::.ctor(System.String,System.String,System.String,System.String,System.String,Neo4jAssemblyController/AssemblyStep/FastenerInfo)
extern void AssemblyStep__ctor_m9E10466D3C2D06685921F7B5B8A0C782FF812EFE (void);
// 0x00000155 System.String Neo4jAssemblyController/AssemblyStep::ToString()
extern void AssemblyStep_ToString_mF97FA0F6A8BF46F54FA596AB0A2C5C7F43719B1B (void);
// 0x00000156 System.Void Neo4jAssemblyController/<>c__DisplayClass59_0::.ctor()
extern void U3CU3Ec__DisplayClass59_0__ctor_m674F3B8084624F71F29620390EBB50983E196BA8 (void);
// 0x00000157 System.Void Neo4jAssemblyController/<>c__DisplayClass59_0::<LoadAssemblyStepsFromExternalSource>b__0(System.Collections.Generic.List`1<AssemblyStepData>)
extern void U3CU3Ec__DisplayClass59_0_U3CLoadAssemblyStepsFromExternalSourceU3Eb__0_m4F0C3DBE566EB3831FD70B0AEAEB01AC61D340A5 (void);
// 0x00000158 System.Void Neo4jAssemblyController/<LoadAssemblyStepsFromExternalSource>d__59::.ctor(System.Int32)
extern void U3CLoadAssemblyStepsFromExternalSourceU3Ed__59__ctor_m2C1F4EFC774BF14272FF4C1DE89DE90681AD6C55 (void);
// 0x00000159 System.Void Neo4jAssemblyController/<LoadAssemblyStepsFromExternalSource>d__59::System.IDisposable.Dispose()
extern void U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_System_IDisposable_Dispose_m319F5BB430D02162EF3D195381B7E0DE3470B642 (void);
// 0x0000015A System.Boolean Neo4jAssemblyController/<LoadAssemblyStepsFromExternalSource>d__59::MoveNext()
extern void U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_MoveNext_m4C664DDEE23B0F7DB2C62D6A1D9AD7D437EF41EF (void);
// 0x0000015B System.Object Neo4jAssemblyController/<LoadAssemblyStepsFromExternalSource>d__59::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9E75C1167D32508D6D41500462CB256C3E65D4F5 (void);
// 0x0000015C System.Void Neo4jAssemblyController/<LoadAssemblyStepsFromExternalSource>d__59::System.Collections.IEnumerator.Reset()
extern void U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_System_Collections_IEnumerator_Reset_m08F35C22B001B0C70C07A90DAFD20092C2B8B704 (void);
// 0x0000015D System.Object Neo4jAssemblyController/<LoadAssemblyStepsFromExternalSource>d__59::System.Collections.IEnumerator.get_Current()
extern void U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_System_Collections_IEnumerator_get_Current_m6300756178A711F4FDAA4DD544522FEEC89132A3 (void);
// 0x0000015E System.Void Neo4jAssemblyController/<WaitForVRPreparation>d__66::.ctor(System.Int32)
extern void U3CWaitForVRPreparationU3Ed__66__ctor_mA26F8D438CFBCC764A3973DF91BB079E5FC86820 (void);
// 0x0000015F System.Void Neo4jAssemblyController/<WaitForVRPreparation>d__66::System.IDisposable.Dispose()
extern void U3CWaitForVRPreparationU3Ed__66_System_IDisposable_Dispose_m5490BA5AB8662E4178645EA3F755E64B9B077266 (void);
// 0x00000160 System.Boolean Neo4jAssemblyController/<WaitForVRPreparation>d__66::MoveNext()
extern void U3CWaitForVRPreparationU3Ed__66_MoveNext_mCA451A1C7C47A420465B31D6CB316F6958D32F42 (void);
// 0x00000161 System.Object Neo4jAssemblyController/<WaitForVRPreparation>d__66::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CWaitForVRPreparationU3Ed__66_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD036D6150E98DC02DE3A8F510FDC7A8FDAFD89F0 (void);
// 0x00000162 System.Void Neo4jAssemblyController/<WaitForVRPreparation>d__66::System.Collections.IEnumerator.Reset()
extern void U3CWaitForVRPreparationU3Ed__66_System_Collections_IEnumerator_Reset_m5F772293B065B07DF676A82107C0BBB0A5F92428 (void);
// 0x00000163 System.Object Neo4jAssemblyController/<WaitForVRPreparation>d__66::System.Collections.IEnumerator.get_Current()
extern void U3CWaitForVRPreparationU3Ed__66_System_Collections_IEnumerator_get_Current_m52AC34BA7D5DFF18B219A7BD2CD4EBE0BDF352B3 (void);
// 0x00000164 System.Void Neo4jAssemblyController/<QueryAssemblyRelationships>d__77::.ctor(System.Int32)
extern void U3CQueryAssemblyRelationshipsU3Ed__77__ctor_m63FF621B2605D1CE12644713FAD955EF8AF70741 (void);
// 0x00000165 System.Void Neo4jAssemblyController/<QueryAssemblyRelationships>d__77::System.IDisposable.Dispose()
extern void U3CQueryAssemblyRelationshipsU3Ed__77_System_IDisposable_Dispose_mF250A1D07A074FFBD2179196CB4C4A9A5D39F144 (void);
// 0x00000166 System.Boolean Neo4jAssemblyController/<QueryAssemblyRelationships>d__77::MoveNext()
extern void U3CQueryAssemblyRelationshipsU3Ed__77_MoveNext_m68B7BEE9C9852EC055960456DEB832C85417BDA4 (void);
// 0x00000167 System.Object Neo4jAssemblyController/<QueryAssemblyRelationships>d__77::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CQueryAssemblyRelationshipsU3Ed__77_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m14ACC263AD0586D408DAF65D36ECC65EC0301CBA (void);
// 0x00000168 System.Void Neo4jAssemblyController/<QueryAssemblyRelationships>d__77::System.Collections.IEnumerator.Reset()
extern void U3CQueryAssemblyRelationshipsU3Ed__77_System_Collections_IEnumerator_Reset_m37782519971FBCD9FBD71AF51DF5E8441868B9B2 (void);
// 0x00000169 System.Object Neo4jAssemblyController/<QueryAssemblyRelationships>d__77::System.Collections.IEnumerator.get_Current()
extern void U3CQueryAssemblyRelationshipsU3Ed__77_System_Collections_IEnumerator_get_Current_m6A7ABA42EC552ED61B2FA04C827210B6B6B1887D (void);
// 0x0000016A System.Void Neo4jAssemblyController/<ExecuteNextAssemblyStepCoroutine>d__81::.ctor(System.Int32)
extern void U3CExecuteNextAssemblyStepCoroutineU3Ed__81__ctor_m5316EDF961735308B4F4DE6657C8705AE634486C (void);
// 0x0000016B System.Void Neo4jAssemblyController/<ExecuteNextAssemblyStepCoroutine>d__81::System.IDisposable.Dispose()
extern void U3CExecuteNextAssemblyStepCoroutineU3Ed__81_System_IDisposable_Dispose_mFA8525A8DD6ABC5567010D9ABD1A101F54B7A5FD (void);
// 0x0000016C System.Boolean Neo4jAssemblyController/<ExecuteNextAssemblyStepCoroutine>d__81::MoveNext()
extern void U3CExecuteNextAssemblyStepCoroutineU3Ed__81_MoveNext_m75E73CA60ABC40F906CA9BD24676B7375D36AE37 (void);
// 0x0000016D System.Void Neo4jAssemblyController/<ExecuteNextAssemblyStepCoroutine>d__81::<>m__Finally1()
extern void U3CExecuteNextAssemblyStepCoroutineU3Ed__81_U3CU3Em__Finally1_m0AC89C812FC7EBC2D1CE5369314AE31F4AEFB13A (void);
// 0x0000016E System.Object Neo4jAssemblyController/<ExecuteNextAssemblyStepCoroutine>d__81::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CExecuteNextAssemblyStepCoroutineU3Ed__81_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m795F6DC87ECFB5D7634AB43CA5B8A81967CED80B (void);
// 0x0000016F System.Void Neo4jAssemblyController/<ExecuteNextAssemblyStepCoroutine>d__81::System.Collections.IEnumerator.Reset()
extern void U3CExecuteNextAssemblyStepCoroutineU3Ed__81_System_Collections_IEnumerator_Reset_m90271590611B26E1C589AA1B03B223B7125CFD80 (void);
// 0x00000170 System.Object Neo4jAssemblyController/<ExecuteNextAssemblyStepCoroutine>d__81::System.Collections.IEnumerator.get_Current()
extern void U3CExecuteNextAssemblyStepCoroutineU3Ed__81_System_Collections_IEnumerator_get_Current_m5AA9A1AA8D9E4AE14CECA949F637D68E2690EE69 (void);
// 0x00000171 System.Void Neo4jAssemblyController/<ReplayAssemblyStepCoroutine>d__82::.ctor(System.Int32)
extern void U3CReplayAssemblyStepCoroutineU3Ed__82__ctor_m683C4A2977ACE95F8A4B3212D8B74DCDFDF9985F (void);
// 0x00000172 System.Void Neo4jAssemblyController/<ReplayAssemblyStepCoroutine>d__82::System.IDisposable.Dispose()
extern void U3CReplayAssemblyStepCoroutineU3Ed__82_System_IDisposable_Dispose_m5C94E4437FC7601FD1A3EB687903BC868F3D84FB (void);
// 0x00000173 System.Boolean Neo4jAssemblyController/<ReplayAssemblyStepCoroutine>d__82::MoveNext()
extern void U3CReplayAssemblyStepCoroutineU3Ed__82_MoveNext_mEEF55F7F8BE7B275D426DB98664325A9C0750FFA (void);
// 0x00000174 System.Void Neo4jAssemblyController/<ReplayAssemblyStepCoroutine>d__82::<>m__Finally1()
extern void U3CReplayAssemblyStepCoroutineU3Ed__82_U3CU3Em__Finally1_m4D496C152DE3BBA6D1A114155AB7325053288EE7 (void);
// 0x00000175 System.Object Neo4jAssemblyController/<ReplayAssemblyStepCoroutine>d__82::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CReplayAssemblyStepCoroutineU3Ed__82_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0D0A92E990BE7D4B853008F555BF55C1C0DA9914 (void);
// 0x00000176 System.Void Neo4jAssemblyController/<ReplayAssemblyStepCoroutine>d__82::System.Collections.IEnumerator.Reset()
extern void U3CReplayAssemblyStepCoroutineU3Ed__82_System_Collections_IEnumerator_Reset_mE6F070F38D905A98889F0349BD79FADA66F63596 (void);
// 0x00000177 System.Object Neo4jAssemblyController/<ReplayAssemblyStepCoroutine>d__82::System.Collections.IEnumerator.get_Current()
extern void U3CReplayAssemblyStepCoroutineU3Ed__82_System_Collections_IEnumerator_get_Current_mCEBE7A15C471402777371677167DE1FC168AD000 (void);
// 0x00000178 System.Void Neo4jAssemblyController/<InstallFastener>d__84::.ctor(System.Int32)
extern void U3CInstallFastenerU3Ed__84__ctor_m4FD9BF41A9BFE32F2D0B379420BF7D3B47AC6532 (void);
// 0x00000179 System.Void Neo4jAssemblyController/<InstallFastener>d__84::System.IDisposable.Dispose()
extern void U3CInstallFastenerU3Ed__84_System_IDisposable_Dispose_m415B97167EC0304AE3FAEA6F4A8F5EC96B942B9E (void);
// 0x0000017A System.Boolean Neo4jAssemblyController/<InstallFastener>d__84::MoveNext()
extern void U3CInstallFastenerU3Ed__84_MoveNext_m83532E4BE67AD72FF1A99FF40711360D039B7E40 (void);
// 0x0000017B System.Object Neo4jAssemblyController/<InstallFastener>d__84::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CInstallFastenerU3Ed__84_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m86C08B0FEEBD5B379113B97BD31D19E8D1184862 (void);
// 0x0000017C System.Void Neo4jAssemblyController/<InstallFastener>d__84::System.Collections.IEnumerator.Reset()
extern void U3CInstallFastenerU3Ed__84_System_Collections_IEnumerator_Reset_m6B5303B72F2DA1B747CE3A057C9BE9D5E73A69EF (void);
// 0x0000017D System.Object Neo4jAssemblyController/<InstallFastener>d__84::System.Collections.IEnumerator.get_Current()
extern void U3CInstallFastenerU3Ed__84_System_Collections_IEnumerator_get_Current_mB6CB41312E67EFED9789A3AABB5CD51B452A4B45 (void);
// 0x0000017E System.Void Neo4jAssemblyUI::Start()
extern void Neo4jAssemblyUI_Start_mCDBF12C4FCBB62A6578482CB3130B39E429DB8B1 (void);
// 0x0000017F System.Void Neo4jAssemblyUI::InitializeUI()
extern void Neo4jAssemblyUI_InitializeUI_m05FCB4604C7F9AFC19E8C254447704E0A83F8158 (void);
// 0x00000180 System.Void Neo4jAssemblyUI::UpdateStatusText(System.String)
extern void Neo4jAssemblyUI_UpdateStatusText_m073B99500339696C19FF9D540DFD383B45EFD509 (void);
// 0x00000181 System.Void Neo4jAssemblyUI::UpdateSelectedPartText(System.String)
extern void Neo4jAssemblyUI_UpdateSelectedPartText_m42F4B327A9CDE05D64706788A964637867EE1C9F (void);
// 0x00000182 System.Void Neo4jAssemblyUI::UpdateStepsCountText(System.Int32,System.Int32)
extern void Neo4jAssemblyUI_UpdateStepsCountText_m767BFA50F60CDD8717A5CB624DA986A4C484D3F5 (void);
// 0x00000183 System.Void Neo4jAssemblyUI::OnNextStepButtonClicked()
extern void Neo4jAssemblyUI_OnNextStepButtonClicked_m89A46D40A824A84DB2D5BCED0694AF59297F74C9 (void);
// 0x00000184 System.Void Neo4jAssemblyUI::OnResetButtonClicked()
extern void Neo4jAssemblyUI_OnResetButtonClicked_mC352298942248A3EAB6C13E668BDD2A640DEF32E (void);
// 0x00000185 System.Void Neo4jAssemblyUI::OnAutoPlayToggleChanged(System.Boolean)
extern void Neo4jAssemblyUI_OnAutoPlayToggleChanged_mD6E10ECFDD2336CE79F14D364972F22ECEE14BD5 (void);
// 0x00000186 System.Void Neo4jAssemblyUI::StartAutoPlay()
extern void Neo4jAssemblyUI_StartAutoPlay_m14DC99B95C55D759A7C8C9F09391D105DB4E4161 (void);
// 0x00000187 System.Void Neo4jAssemblyUI::StopAutoPlay()
extern void Neo4jAssemblyUI_StopAutoPlay_mEA3969A97E2AE15517BDDCF3CABA44AAD0B221B8 (void);
// 0x00000188 System.Collections.IEnumerator Neo4jAssemblyUI::AutoPlayCoroutine()
extern void Neo4jAssemblyUI_AutoPlayCoroutine_mEC3062D30DE1321E94E76FB8A224816EB264C6B1 (void);
// 0x00000189 System.Void Neo4jAssemblyUI::EnableNextStepButton(System.Boolean)
extern void Neo4jAssemblyUI_EnableNextStepButton_m5F22F1526B195F1CACBF8E394D3D4E706C77E18F (void);
// 0x0000018A System.Void Neo4jAssemblyUI::EnableReplayButton(System.Boolean)
extern void Neo4jAssemblyUI_EnableReplayButton_mE8BAB509106ECCB7533C84A7402E6BF5A4E25846 (void);
// 0x0000018B System.Void Neo4jAssemblyUI::OnReplayButtonClicked()
extern void Neo4jAssemblyUI_OnReplayButtonClicked_m59F31CA63E639C3FDD53F42CCAA3E3CA030769A8 (void);
// 0x0000018C System.Void Neo4jAssemblyUI::OnSpeedSliderChanged(System.Single)
extern void Neo4jAssemblyUI_OnSpeedSliderChanged_m92803AE875B80617418DF329FB59CC4816F9AF59 (void);
// 0x0000018D System.Void Neo4jAssemblyUI::UpdateSpeedValueText(System.Single)
extern void Neo4jAssemblyUI_UpdateSpeedValueText_m204D5414571DCA126E0FC211D05C7309FF849FA8 (void);
// 0x0000018E System.Void Neo4jAssemblyUI::OnPartSelected(System.String)
extern void Neo4jAssemblyUI_OnPartSelected_m24FF709F553104F87BF3472EEBD5E7B4BC484A21 (void);
// 0x0000018F System.Void Neo4jAssemblyUI::OnAssemblyStepsLoaded(System.Int32)
extern void Neo4jAssemblyUI_OnAssemblyStepsLoaded_mF3A4DDE5C3A0C4605A57BBFF65655881EA4EA879 (void);
// 0x00000190 System.Void Neo4jAssemblyUI::OnStepExecuted(System.Int32,System.Int32,System.String)
extern void Neo4jAssemblyUI_OnStepExecuted_m5B708537395770CEA2BA9E44A16EA6F625EB5CF3 (void);
// 0x00000191 System.Void Neo4jAssemblyUI::OnAssemblyCompleted()
extern void Neo4jAssemblyUI_OnAssemblyCompleted_m5C7B8529034EF917149CDA74A7F93D781A7FBA88 (void);
// 0x00000192 System.Void Neo4jAssemblyUI::.ctor()
extern void Neo4jAssemblyUI__ctor_m6F3E7FB649ADDE8690BC38471688F5EBBA39A66C (void);
// 0x00000193 System.Void Neo4jAssemblyUI/<AutoPlayCoroutine>d__25::.ctor(System.Int32)
extern void U3CAutoPlayCoroutineU3Ed__25__ctor_mB81CF56B677430F174AEC46D49483F3A5010388E (void);
// 0x00000194 System.Void Neo4jAssemblyUI/<AutoPlayCoroutine>d__25::System.IDisposable.Dispose()
extern void U3CAutoPlayCoroutineU3Ed__25_System_IDisposable_Dispose_m23D8CD4DED386C38ED7E2EA7F9A79E4C141A13D3 (void);
// 0x00000195 System.Boolean Neo4jAssemblyUI/<AutoPlayCoroutine>d__25::MoveNext()
extern void U3CAutoPlayCoroutineU3Ed__25_MoveNext_mA9B99C152698F51E9517394AE85A0813E335A301 (void);
// 0x00000196 System.Object Neo4jAssemblyUI/<AutoPlayCoroutine>d__25::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAutoPlayCoroutineU3Ed__25_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m57C1D39FF1804321046B4A2BB2CEBAC609EA2EF1 (void);
// 0x00000197 System.Void Neo4jAssemblyUI/<AutoPlayCoroutine>d__25::System.Collections.IEnumerator.Reset()
extern void U3CAutoPlayCoroutineU3Ed__25_System_Collections_IEnumerator_Reset_m8A4F92BE6F420A837C0CB7F05B4D398CDEBB8575 (void);
// 0x00000198 System.Object Neo4jAssemblyUI/<AutoPlayCoroutine>d__25::System.Collections.IEnumerator.get_Current()
extern void U3CAutoPlayCoroutineU3Ed__25_System_Collections_IEnumerator_get_Current_mB7BE77491FD1D8719447C92AB86E7EBE8F87A174 (void);
// 0x00000199 System.Collections.IEnumerator Neo4jConnector::ExecuteQuery(System.String,System.Action`1<System.String>)
extern void Neo4jConnector_ExecuteQuery_mEAAA9076CCEACB71D39D0CB2B2C435F2482E902A (void);
// 0x0000019A System.String Neo4jConnector::get_DatabaseUrl()
extern void Neo4jConnector_get_DatabaseUrl_m818FFB7AB8FBCCBF2B20198871C4BAF4F3A87406 (void);
// 0x0000019B System.String Neo4jConnector::get_DatabaseUsername()
extern void Neo4jConnector_get_DatabaseUsername_m3A33077D70412DDA80A989E4ED3A80EA0988AADC (void);
// 0x0000019C System.String Neo4jConnector::get_DatabasePassword()
extern void Neo4jConnector_get_DatabasePassword_m5FDE24B2B8E1ACF7487AFCEC1B3C77EE026C0AE9 (void);
// 0x0000019D System.Collections.IEnumerator Neo4jConnector::TestConnection(System.Action`1<System.Boolean>)
extern void Neo4jConnector_TestConnection_m35B27310317D2B22ADA87DE18797F6680A670975 (void);
// 0x0000019E System.Collections.IEnumerator Neo4jConnector::QueryConnections(System.String,System.Action`1<System.Collections.Generic.List`1<System.ValueTuple`4<System.String,System.String,System.String,System.String>>>)
extern void Neo4jConnector_QueryConnections_m20F0AB449482A817B28427DA4182C5AF63780ADA (void);
// 0x0000019F System.Void Neo4jConnector::.ctor()
extern void Neo4jConnector__ctor_m3A334A82D94E2CCDF167E43D7A2F762E6BCC9934 (void);
// 0x000001A0 System.Void Neo4jConnector/<ExecuteQuery>d__3::.ctor(System.Int32)
extern void U3CExecuteQueryU3Ed__3__ctor_mF382C62277E30FCD9C5529C0F149ACDBD2ED51B3 (void);
// 0x000001A1 System.Void Neo4jConnector/<ExecuteQuery>d__3::System.IDisposable.Dispose()
extern void U3CExecuteQueryU3Ed__3_System_IDisposable_Dispose_m987DD58EE3222A4AF70C7FE9CC9D6EA214487C59 (void);
// 0x000001A2 System.Boolean Neo4jConnector/<ExecuteQuery>d__3::MoveNext()
extern void U3CExecuteQueryU3Ed__3_MoveNext_m6C2962BA6BFD5B1A97CA394F19EF50DC6C6F71AE (void);
// 0x000001A3 System.Void Neo4jConnector/<ExecuteQuery>d__3::<>m__Finally1()
extern void U3CExecuteQueryU3Ed__3_U3CU3Em__Finally1_mCDA4EE30E34C7960A59D93912BDF64C1EABE8927 (void);
// 0x000001A4 System.Object Neo4jConnector/<ExecuteQuery>d__3::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CExecuteQueryU3Ed__3_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA0A3756E3BDFC5451D48651394FD8F5B729CD492 (void);
// 0x000001A5 System.Void Neo4jConnector/<ExecuteQuery>d__3::System.Collections.IEnumerator.Reset()
extern void U3CExecuteQueryU3Ed__3_System_Collections_IEnumerator_Reset_m20E488C05406BAFE351959C407556F69D5EE09D5 (void);
// 0x000001A6 System.Object Neo4jConnector/<ExecuteQuery>d__3::System.Collections.IEnumerator.get_Current()
extern void U3CExecuteQueryU3Ed__3_System_Collections_IEnumerator_get_Current_m40360A3B3B6ADB0982FB0CCB874DB747A142F5D3 (void);
// 0x000001A7 System.Void Neo4jConnector/<>c__DisplayClass10_0::.ctor()
extern void U3CU3Ec__DisplayClass10_0__ctor_m2D6F0E70743D756A75C9B0210FDF2B01DCEA797C (void);
// 0x000001A8 System.Void Neo4jConnector/<>c__DisplayClass10_0::<TestConnection>b__0(System.String)
extern void U3CU3Ec__DisplayClass10_0_U3CTestConnectionU3Eb__0_mD98388D4B0828C0E8454B39CE13470D8E20D9B3D (void);
// 0x000001A9 System.Void Neo4jConnector/<TestConnection>d__10::.ctor(System.Int32)
extern void U3CTestConnectionU3Ed__10__ctor_m04FE15BC317A8298369BEAD645E40BFD404767D4 (void);
// 0x000001AA System.Void Neo4jConnector/<TestConnection>d__10::System.IDisposable.Dispose()
extern void U3CTestConnectionU3Ed__10_System_IDisposable_Dispose_mCA99EF51488D04A2E93023FCC08B2DA489A05EE7 (void);
// 0x000001AB System.Boolean Neo4jConnector/<TestConnection>d__10::MoveNext()
extern void U3CTestConnectionU3Ed__10_MoveNext_m96A27E3B5C8CB1E4819A172195EB3FA0F8607C4A (void);
// 0x000001AC System.Object Neo4jConnector/<TestConnection>d__10::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CTestConnectionU3Ed__10_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB6722C108C3DF5120114EF33DABBB98028A477A0 (void);
// 0x000001AD System.Void Neo4jConnector/<TestConnection>d__10::System.Collections.IEnumerator.Reset()
extern void U3CTestConnectionU3Ed__10_System_Collections_IEnumerator_Reset_m47CA6D10F214B3C95F0C47E3385A560B639FE44C (void);
// 0x000001AE System.Object Neo4jConnector/<TestConnection>d__10::System.Collections.IEnumerator.get_Current()
extern void U3CTestConnectionU3Ed__10_System_Collections_IEnumerator_get_Current_mA687283225AABB53C7277F963BE5FF3EC2F763E4 (void);
// 0x000001AF System.Void Neo4jConnector/<>c__DisplayClass11_0::.ctor()
extern void U3CU3Ec__DisplayClass11_0__ctor_m71B8992D0FB0926ACE415E53A4684A2DF6714732 (void);
// 0x000001B0 System.Void Neo4jConnector/<>c__DisplayClass11_0::<QueryConnections>b__0(System.String)
extern void U3CU3Ec__DisplayClass11_0_U3CQueryConnectionsU3Eb__0_mB885CD288657C90B46973ED4A83370E2EB43F1E1 (void);
// 0x000001B1 System.Void Neo4jConnector/<QueryConnections>d__11::.ctor(System.Int32)
extern void U3CQueryConnectionsU3Ed__11__ctor_m4901A0BADFEAAD19E68A799040D9219B08A784B8 (void);
// 0x000001B2 System.Void Neo4jConnector/<QueryConnections>d__11::System.IDisposable.Dispose()
extern void U3CQueryConnectionsU3Ed__11_System_IDisposable_Dispose_m9B6A9EAFA1DF81B82CA4736BF8C5CDDAEDEE53C4 (void);
// 0x000001B3 System.Boolean Neo4jConnector/<QueryConnections>d__11::MoveNext()
extern void U3CQueryConnectionsU3Ed__11_MoveNext_m9C9706538C675FFC6BBAFF18A15E02B14F0F87B0 (void);
// 0x000001B4 System.Object Neo4jConnector/<QueryConnections>d__11::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CQueryConnectionsU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2AE7E242201CF1333C7E094F5478EF48E1FF84FE (void);
// 0x000001B5 System.Void Neo4jConnector/<QueryConnections>d__11::System.Collections.IEnumerator.Reset()
extern void U3CQueryConnectionsU3Ed__11_System_Collections_IEnumerator_Reset_mEC7096B0DC3ECFCBD13C0E0172E69C12B2AA13B1 (void);
// 0x000001B6 System.Object Neo4jConnector/<QueryConnections>d__11::System.Collections.IEnumerator.get_Current()
extern void U3CQueryConnectionsU3Ed__11_System_Collections_IEnumerator_get_Current_mF188E3532A258CACD997C4E0CEDA25586E84D3CA (void);
// 0x000001B7 System.Void PICODeploymentManager::Awake()
extern void PICODeploymentManager_Awake_m398A9B609C4E8098A44BA316F9B6F9F4892CF687 (void);
// 0x000001B8 System.Void PICODeploymentManager::Start()
extern void PICODeploymentManager_Start_m513829B8D2BF62E541D3FE0AF705EFCB2F5E488D (void);
// 0x000001B9 System.Void PICODeploymentManager::ConfigureEnvironment()
extern void PICODeploymentManager_ConfigureEnvironment_mC5E0B52F460FB39BAD2CC61A7755B3D764B2BBF5 (void);
// 0x000001BA System.Void PICODeploymentManager::DetectPlatform()
extern void PICODeploymentManager_DetectPlatform_m4BCBE611C15930811EC55D0CA3898BB30BA2AD25 (void);
// 0x000001BB System.Void PICODeploymentManager::FindComponents()
extern void PICODeploymentManager_FindComponents_m2AC3FB0DA8784A5D6C521CA5D0B039EB3432E492 (void);
// 0x000001BC System.Void PICODeploymentManager::ConfigureForPICO()
extern void PICODeploymentManager_ConfigureForPICO_m756046194F9532F301AE2B2855F2C7F73B88F6E8 (void);
// 0x000001BD System.Void PICODeploymentManager::ConfigureForDevelopment()
extern void PICODeploymentManager_ConfigureForDevelopment_mCC48CBBFF4897FCFF273D76E47EB9086CE94E527 (void);
// 0x000001BE System.Void PICODeploymentManager::OptimizeForPICO()
extern void PICODeploymentManager_OptimizeForPICO_m590101ECBEA7FA00F93B945F73D89115837764F1 (void);
// 0x000001BF System.Void PICODeploymentManager::SetPrivateField(System.Object,System.String,System.Object)
extern void PICODeploymentManager_SetPrivateField_m782293148798ED51D338230F031D468CA3A51006 (void);
// 0x000001C0 System.Void PICODeploymentManager::ValidateConfiguration()
extern void PICODeploymentManager_ValidateConfiguration_mA06D72971650E2CF927865E2D89F9DB4CD379578 (void);
// 0x000001C1 System.Void PICODeploymentManager::LogCurrentConfiguration()
extern void PICODeploymentManager_LogCurrentConfiguration_m4654CFA8462DDCE883E072EA523387DD7490DB03 (void);
// 0x000001C2 System.Void PICODeploymentManager::SwitchToPICOMode()
extern void PICODeploymentManager_SwitchToPICOMode_mB6A30668470129DBAB3854E37345B0191D21710E (void);
// 0x000001C3 System.Void PICODeploymentManager::SwitchToDevelopmentMode()
extern void PICODeploymentManager_SwitchToDevelopmentMode_mE31EA7C035E616FB8C204469B7448EA4AB2B2C6A (void);
// 0x000001C4 System.Boolean PICODeploymentManager::get_IsPICODeployment()
extern void PICODeploymentManager_get_IsPICODeployment_m499D3B221CB2EBFE1FD38A93A875C6EDA71F9859 (void);
// 0x000001C5 System.String PICODeploymentManager::GetConfigurationInfo()
extern void PICODeploymentManager_GetConfigurationInfo_m991D723DCB734ECFB9CD217B0DA8F099D88B31E7 (void);
// 0x000001C6 System.Void PICODeploymentManager::.ctor()
extern void PICODeploymentManager__ctor_m96ABD81A1A0A99CA26934178D14E45362E75D80F (void);
// 0x000001C7 System.Void PICOVRInputAdapter::Start()
extern void PICOVRInputAdapter_Start_m871A5B4EFAE0F6D3DF7460A683D070FAE7D3FD17 (void);
// 0x000001C8 System.Void PICOVRInputAdapter::Update()
extern void PICOVRInputAdapter_Update_m6BB22EC20ED960A5D818DF991517B25085452F68 (void);
// 0x000001C9 System.Void PICOVRInputAdapter::InitializePICOInput()
extern void PICOVRInputAdapter_InitializePICOInput_m9A4DCCE6BDE9CAD0161EDA915C8376B99B1AA324 (void);
// 0x000001CA System.Void PICOVRInputAdapter::HandlePICOInput()
extern void PICOVRInputAdapter_HandlePICOInput_mF3940DD2E6CBE4CAB8899C35BD271933AADF8AB6 (void);
// 0x000001CB System.Void PICOVRInputAdapter::HandleControllerInput(System.Object,System.String)
extern void PICOVRInputAdapter_HandleControllerInput_mDDB099B089A49BC4ED1E51132DACC9A74B001DEA (void);
// 0x000001CC System.Void PICOVRInputAdapter::OnTriggerPressed(System.String)
extern void PICOVRInputAdapter_OnTriggerPressed_mE549CF40673C509C9D9E9A81D05FCA1ABF6D7C26 (void);
// 0x000001CD System.Void PICOVRInputAdapter::OnPrimaryButtonPressed(System.String)
extern void PICOVRInputAdapter_OnPrimaryButtonPressed_m5B191F23E444682348C83342EE77E5F7733CBED5 (void);
// 0x000001CE System.Void PICOVRInputAdapter::OnSecondaryButtonPressed(System.String)
extern void PICOVRInputAdapter_OnSecondaryButtonPressed_mBE6FE162441EBEF8CBC7258253688BDC42639150 (void);
// 0x000001CF System.Void PICOVRInputAdapter::OnMenuButtonPressed(System.String)
extern void PICOVRInputAdapter_OnMenuButtonPressed_m07CD6C45894DC25455589BFB78C4C75BEB70A195 (void);
// 0x000001D0 System.Void PICOVRInputAdapter::HandleFallbackInput()
extern void PICOVRInputAdapter_HandleFallbackInput_m725E2F706B69D03843CCECD9CACE628846D07366 (void);
// 0x000001D1 System.Void PICOVRInputAdapter::ProvideTactileFeedback()
extern void PICOVRInputAdapter_ProvideTactileFeedback_mA106D6E843A8B64A4B8F1A1713D176EADB2F9B00 (void);
// 0x000001D2 System.Void PICOVRInputAdapter::ShowInputMapping()
extern void PICOVRInputAdapter_ShowInputMapping_mD80922B53B9A1B8A9923F45FB7350B8F658E45B4 (void);
// 0x000001D3 System.Void PICOVRInputAdapter::TestAllVRFunctions()
extern void PICOVRInputAdapter_TestAllVRFunctions_m422DC00A07F0828D9B4715651B75700B74E40E08 (void);
// 0x000001D4 System.Collections.IEnumerator PICOVRInputAdapter::TestSequence()
extern void PICOVRInputAdapter_TestSequence_m1F1934F4900216A04B1000AB2543D2B8A6A6DE48 (void);
// 0x000001D5 System.Void PICOVRInputAdapter::SetPICOInputEnabled(System.Boolean)
extern void PICOVRInputAdapter_SetPICOInputEnabled_m3997B8537C9BDB14CE2F8835E6FAABDC20DC34C5 (void);
// 0x000001D6 System.Void PICOVRInputAdapter::OnDestroy()
extern void PICOVRInputAdapter_OnDestroy_mC342C42272011A4436B1CF27A9119CDB0CC2EF8B (void);
// 0x000001D7 System.Void PICOVRInputAdapter::.ctor()
extern void PICOVRInputAdapter__ctor_m7A5E3D1662C082337811679E8795E15FB3B0202F (void);
// 0x000001D8 System.Void PICOVRInputAdapter/<TestSequence>d__20::.ctor(System.Int32)
extern void U3CTestSequenceU3Ed__20__ctor_mA9F3034EE53E73484D7BA64BB19DC98ABC050D63 (void);
// 0x000001D9 System.Void PICOVRInputAdapter/<TestSequence>d__20::System.IDisposable.Dispose()
extern void U3CTestSequenceU3Ed__20_System_IDisposable_Dispose_mF196E225B0C9F0EEBC722201DDB2CDF6B71C37D5 (void);
// 0x000001DA System.Boolean PICOVRInputAdapter/<TestSequence>d__20::MoveNext()
extern void U3CTestSequenceU3Ed__20_MoveNext_mEA6FF436BB67B5643100C293E7DED82F37B5536B (void);
// 0x000001DB System.Object PICOVRInputAdapter/<TestSequence>d__20::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CTestSequenceU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8B4EF2C347B47C55F065B2B5F2B97A7A2DA0B431 (void);
// 0x000001DC System.Void PICOVRInputAdapter/<TestSequence>d__20::System.Collections.IEnumerator.Reset()
extern void U3CTestSequenceU3Ed__20_System_Collections_IEnumerator_Reset_mC495B7926E34C34C35A2DDE558F9160DCA50CB06 (void);
// 0x000001DD System.Object PICOVRInputAdapter/<TestSequence>d__20::System.Collections.IEnumerator.get_Current()
extern void U3CTestSequenceU3Ed__20_System_Collections_IEnumerator_get_Current_mB9626F117F50976F9A521FDB5F507F5DD724EE7F (void);
// 0x000001DE System.Void SimpleAssemblyDataReceiver::Start()
extern void SimpleAssemblyDataReceiver_Start_mB44AE545C79D458750DBEE53F78418DC34163055 (void);
// 0x000001DF System.Void SimpleAssemblyDataReceiver::ReceiveSimpleAssemblyStep(System.String,System.String,System.String,System.String)
extern void SimpleAssemblyDataReceiver_ReceiveSimpleAssemblyStep_m751135B0629BE3E50283B250FE3E9D6BA623B337 (void);
// 0x000001E0 System.Void SimpleAssemblyDataReceiver::ReceiveAssemblyStepWithScrew(System.String,System.String,System.String,System.String,System.String,System.String)
extern void SimpleAssemblyDataReceiver_ReceiveAssemblyStepWithScrew_m87F12585CCFD4136F3B9DE8A367A6B051BD831B7 (void);
// 0x000001E1 System.Void SimpleAssemblyDataReceiver::TryAddStepToController(AssemblyStep)
extern void SimpleAssemblyDataReceiver_TryAddStepToController_m89E596EAEFA6294F5C076400486EDBB1B423C77B (void);
// 0x000001E2 System.Void SimpleAssemblyDataReceiver::ClearAllSteps()
extern void SimpleAssemblyDataReceiver_ClearAllSteps_m817241E6B6F8B229EF35A686EBDD8283CDA50F9D (void);
// 0x000001E3 System.Void SimpleAssemblyDataReceiver::StartAssemblyAnimation()
extern void SimpleAssemblyDataReceiver_StartAssemblyAnimation_mD334AECAA1620754F45521B94BCE7D035CD8F42E (void);
// 0x000001E4 System.String SimpleAssemblyDataReceiver::GetSystemStatus()
extern void SimpleAssemblyDataReceiver_GetSystemStatus_mE12B7F1DC369CC97FF75AA09133188F5E2548636 (void);
// 0x000001E5 System.Void SimpleAssemblyDataReceiver::ReceiveAssemblyDataJSON(System.String)
extern void SimpleAssemblyDataReceiver_ReceiveAssemblyDataJSON_m67F383928F0E66CA5A0DD51CB8C534C62DA78C8F (void);
// 0x000001E6 System.Void SimpleAssemblyDataReceiver::ReceiveAssemblyDataArray(System.String[])
extern void SimpleAssemblyDataReceiver_ReceiveAssemblyDataArray_m5E561D92167351D9352518004CAA3C5008D735F1 (void);
// 0x000001E7 System.Void SimpleAssemblyDataReceiver::ParseAndReceiveStep(System.String)
extern void SimpleAssemblyDataReceiver_ParseAndReceiveStep_m0372DAFEAEAD2350244888876E8FBA058EEA1B0A (void);
// 0x000001E8 System.Void SimpleAssemblyDataReceiver::TestSimpleAssembly()
extern void SimpleAssemblyDataReceiver_TestSimpleAssembly_mE5581D0D3574FD6F73497D42A0EF5614434C4B7B (void);
// 0x000001E9 System.Void SimpleAssemblyDataReceiver::TestScrewAssembly()
extern void SimpleAssemblyDataReceiver_TestScrewAssembly_m259B3BD72BE9EBAF72CE540C8D0D482021D92726 (void);
// 0x000001EA System.Void SimpleAssemblyDataReceiver::TestMultipleAssembly()
extern void SimpleAssemblyDataReceiver_TestMultipleAssembly_m6243B7111800784F8D077E28386A821F4744ED16 (void);
// 0x000001EB System.Void SimpleAssemblyDataReceiver::TestClearSteps()
extern void SimpleAssemblyDataReceiver_TestClearSteps_mC8257389399356339D993907E42103EA4D4731DA (void);
// 0x000001EC System.Void SimpleAssemblyDataReceiver::TestStartAnimation()
extern void SimpleAssemblyDataReceiver_TestStartAnimation_m5023F5E501A2FE7524A3FDF4E5B9C11656E91A5B (void);
// 0x000001ED System.Void SimpleAssemblyDataReceiver::.ctor()
extern void SimpleAssemblyDataReceiver__ctor_mCA7A0DCFEE31FF8AE677EF481BD4960C1B118BF6 (void);
// 0x000001EE System.Void SimpleExternalDataReceiver::Start()
extern void SimpleExternalDataReceiver_Start_mECA24017984741ED3C4CF74C3C90DFDE417E2417 (void);
// 0x000001EF System.Void SimpleExternalDataReceiver::ReceiveAssemblyData(System.String,System.String)
extern void SimpleExternalDataReceiver_ReceiveAssemblyData_m7AF8687829EA89619C310A2227EBE18FB25688CF (void);
// 0x000001F0 System.Void SimpleExternalDataReceiver::ReceiveSimpleAssemblyStep(System.String,System.String,System.String,System.String,System.String)
extern void SimpleExternalDataReceiver_ReceiveSimpleAssemblyStep_mE520344B7689530EB470781513F8A325343BE3D4 (void);
// 0x000001F1 System.Void SimpleExternalDataReceiver::ReceiveAssemblyStepWithFastener(System.String,System.String,System.String,System.String,System.String,System.String)
extern void SimpleExternalDataReceiver_ReceiveAssemblyStepWithFastener_m1984DFABC245B69573A8A4A7E2032CD82CA99F9D (void);
// 0x000001F2 System.Void SimpleExternalDataReceiver::LoadTestData()
extern void SimpleExternalDataReceiver_LoadTestData_m0FC21159F061F075440F99AAF799A2649056FCD6 (void);
// 0x000001F3 System.Void SimpleExternalDataReceiver::CreateSampleAssemblyData()
extern void SimpleExternalDataReceiver_CreateSampleAssemblyData_m2108FA0D0233FBA46269FE7F1124445B43A90E3D (void);
// 0x000001F4 System.Void SimpleExternalDataReceiver::TestSimpleAssemblyStep()
extern void SimpleExternalDataReceiver_TestSimpleAssemblyStep_m63592CCDBFE2ABD91516C20F4458EE3B5F03C1B2 (void);
// 0x000001F5 System.Void SimpleExternalDataReceiver::TestScrewAssemblyStep()
extern void SimpleExternalDataReceiver_TestScrewAssemblyStep_m09B2C7220E1CEA8C5CD5C6C5C394B59291B06166 (void);
// 0x000001F6 System.Void SimpleExternalDataReceiver::ClearAssemblySteps()
extern void SimpleExternalDataReceiver_ClearAssemblySteps_m05F35556FEC69C68310A9F7240ABA243154F2B88 (void);
// 0x000001F7 System.Boolean SimpleExternalDataReceiver::IsReadyToReceiveData()
extern void SimpleExternalDataReceiver_IsReadyToReceiveData_m5B34E4D6E78899F1FA1686BE5716E45FDFAB7821 (void);
// 0x000001F8 System.String SimpleExternalDataReceiver::GetAssemblyStatus()
extern void SimpleExternalDataReceiver_GetAssemblyStatus_m8BF8F36E42416F776DE765767DD5BBC2A3A7F508 (void);
// 0x000001F9 System.Void SimpleExternalDataReceiver::Update()
extern void SimpleExternalDataReceiver_Update_mABB95B818A82D5CBA18DEFB12504B29CB06F99CB (void);
// 0x000001FA System.Void SimpleExternalDataReceiver::.ctor()
extern void SimpleExternalDataReceiver__ctor_m430FD562BDA65E920723B815DF9F5E4F8992225D (void);
// 0x000001FB System.Void Test::Start()
extern void Test_Start_mA902842AF55C0A063D71F22B280F28BF0FB01497 (void);
// 0x000001FC System.Void Test::Update()
extern void Test_Update_m68AE52707CAA861E0DBDC29971974A5BF62D82D1 (void);
// 0x000001FD System.Void Test::OnDrawGizmos()
extern void Test_OnDrawGizmos_m6481F8828A0F683CCAE82CCB2C6CB8A94F05E6E8 (void);
// 0x000001FE System.Void Test::.ctor()
extern void Test__ctor_mB84DF4A3888723C395E76E3879FDFB8AA1EFEDCB (void);
// 0x000001FF System.Void VRAssemblyDebugger::Start()
extern void VRAssemblyDebugger_Start_m88057DC24FD49105662F4E7E745676D3B7362827 (void);
// 0x00000200 System.Void VRAssemblyDebugger::Update()
extern void VRAssemblyDebugger_Update_mEE80CE3E4E82FFB2C7D1BBD866F3561C6A3C577F (void);
// 0x00000201 System.Void VRAssemblyDebugger::FindComponents()
extern void VRAssemblyDebugger_FindComponents_m9609506EED6196374D2A6551B8361EF71DBBE20E (void);
// 0x00000202 System.Void VRAssemblyDebugger::ValidateSetup()
extern void VRAssemblyDebugger_ValidateSetup_m1200B28D916E56DA6D6A94CD18B92BDA862F7CF5 (void);
// 0x00000203 System.Void VRAssemblyDebugger::LogSystemStatus()
extern void VRAssemblyDebugger_LogSystemStatus_m67504E1AA45FE48557C8AEE8E518A2B406845D26 (void);
// 0x00000204 System.Void VRAssemblyDebugger::HandleDebugInput()
extern void VRAssemblyDebugger_HandleDebugInput_m95DC4AEC014F5083F4C6B3667F9181BB3D68087A (void);
// 0x00000205 System.Void VRAssemblyDebugger::TestPositioning()
extern void VRAssemblyDebugger_TestPositioning_mE4BE9287A0C26F70480A633E0AA84F01AD8EF095 (void);
// 0x00000206 System.Void VRAssemblyDebugger::TestCameraBasedPositioning()
extern void VRAssemblyDebugger_TestCameraBasedPositioning_m8805EAF07380827CDF88D5AAF4C1A997FCED85AD (void);
// 0x00000207 UnityEngine.Camera VRAssemblyDebugger::GetVRSimulatorCamera()
extern void VRAssemblyDebugger_GetVRSimulatorCamera_m42AE5CF74DEF231BDA5DFC445E0C78DD7894017F (void);
// 0x00000208 System.Void VRAssemblyDebugger::TestOrientation()
extern void VRAssemblyDebugger_TestOrientation_m8F0A58E8070694652BF410D17CD1ECAC13FFB120 (void);
// 0x00000209 System.Void VRAssemblyDebugger::ShowCurrentCameraInfo()
extern void VRAssemblyDebugger_ShowCurrentCameraInfo_mACE249A98EA7F9D20B8A794EF3EAFB5A9E85DBBF (void);
// 0x0000020A System.Void VRAssemblyDebugger::ShowTargetAssemblyPointInfo()
extern void VRAssemblyDebugger_ShowTargetAssemblyPointInfo_mDA6C48E266723BDE26C51399073099CF693FCAF4 (void);
// 0x0000020B System.Collections.IEnumerator VRAssemblyDebugger::TestOnlyOrientationStep()
extern void VRAssemblyDebugger_TestOnlyOrientationStep_m8B8B5D222D95300345C45D99402858AD873CAD66 (void);
// 0x0000020C System.Collections.IEnumerator VRAssemblyDebugger::TestSpecificAssemblyPointOrientation()
extern void VRAssemblyDebugger_TestSpecificAssemblyPointOrientation_m5CF4EB0F31A75F6F7B1B4D25A1D628F0C74760B4 (void);
// 0x0000020D System.Void VRAssemblyDebugger::VerifyAssemblyPointOrientation()
extern void VRAssemblyDebugger_VerifyAssemblyPointOrientation_mA1EF2FC7FA25374B28C61701E6D87DA824571E65 (void);
// 0x0000020E System.Void VRAssemblyDebugger::DiagnoseOrientationIssue()
extern void VRAssemblyDebugger_DiagnoseOrientationIssue_mAFFF4A3D6D5A704B1940C563634D21D0A245B0FC (void);
// 0x0000020F System.Void VRAssemblyDebugger::ResetPosition()
extern void VRAssemblyDebugger_ResetPosition_m1DEA543BFBA73DA26F0BAE298036516BB32A3A33 (void);
// 0x00000210 System.Void VRAssemblyDebugger::ToggleDebugMode()
extern void VRAssemblyDebugger_ToggleDebugMode_mF30A6483F7D73F8DE00EA69AD570FEE3CA1AC22B (void);
// 0x00000211 System.Void VRAssemblyDebugger::ForceInitializePositioner()
extern void VRAssemblyDebugger_ForceInitializePositioner_mA640224DBD231DE04B6D914DF72CF66D2705AA1C (void);
// 0x00000212 System.Object VRAssemblyDebugger::GetPrivateField(System.Object,System.String)
extern void VRAssemblyDebugger_GetPrivateField_m25929B441736549ED4A599156FF67034D765AEA4 (void);
// 0x00000213 System.Void VRAssemblyDebugger::SetRelativePosition(UnityEngine.Vector3)
extern void VRAssemblyDebugger_SetRelativePosition_m708C810BBC71BCD7A7CB44F87BC5916D45BD4B4E (void);
// 0x00000214 UnityEngine.GUIStyle VRAssemblyDebugger::EditorGUIStyle()
extern void VRAssemblyDebugger_EditorGUIStyle_m35445EEFA87748810503E4CB9FEE18A444015057 (void);
// 0x00000215 System.Void VRAssemblyDebugger::.ctor()
extern void VRAssemblyDebugger__ctor_m846C941FB4DC221B0C49AE40AEA08D325BDF0136 (void);
// 0x00000216 System.Void VRAssemblyDebugger/<TestOnlyOrientationStep>d__28::.ctor(System.Int32)
extern void U3CTestOnlyOrientationStepU3Ed__28__ctor_m9B662B94B759AC3B9F552E18AB7AB013101B1AB1 (void);
// 0x00000217 System.Void VRAssemblyDebugger/<TestOnlyOrientationStep>d__28::System.IDisposable.Dispose()
extern void U3CTestOnlyOrientationStepU3Ed__28_System_IDisposable_Dispose_m3E2C3C2CD915EEF1D58B5DB881A5002DD759BF0B (void);
// 0x00000218 System.Boolean VRAssemblyDebugger/<TestOnlyOrientationStep>d__28::MoveNext()
extern void U3CTestOnlyOrientationStepU3Ed__28_MoveNext_m78AF374949E36ABCB83706E8894069978470B4B5 (void);
// 0x00000219 System.Object VRAssemblyDebugger/<TestOnlyOrientationStep>d__28::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CTestOnlyOrientationStepU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m44B4CCBEEF7749175E732B612ABBD49F0C0CFEFC (void);
// 0x0000021A System.Void VRAssemblyDebugger/<TestOnlyOrientationStep>d__28::System.Collections.IEnumerator.Reset()
extern void U3CTestOnlyOrientationStepU3Ed__28_System_Collections_IEnumerator_Reset_m3590A923984CE409A45AF837D1B56CDCA672A6C8 (void);
// 0x0000021B System.Object VRAssemblyDebugger/<TestOnlyOrientationStep>d__28::System.Collections.IEnumerator.get_Current()
extern void U3CTestOnlyOrientationStepU3Ed__28_System_Collections_IEnumerator_get_Current_m230BEBBF16972C25F2A2FA0D950A3C34AD46C0D5 (void);
// 0x0000021C System.Void VRAssemblyDebugger/<TestSpecificAssemblyPointOrientation>d__29::.ctor(System.Int32)
extern void U3CTestSpecificAssemblyPointOrientationU3Ed__29__ctor_m5A6F84491B8F67D391E2BFA2D4B975587423C7DA (void);
// 0x0000021D System.Void VRAssemblyDebugger/<TestSpecificAssemblyPointOrientation>d__29::System.IDisposable.Dispose()
extern void U3CTestSpecificAssemblyPointOrientationU3Ed__29_System_IDisposable_Dispose_m9F953536DBFB57E85D98A21E9440BEBB7535B17E (void);
// 0x0000021E System.Boolean VRAssemblyDebugger/<TestSpecificAssemblyPointOrientation>d__29::MoveNext()
extern void U3CTestSpecificAssemblyPointOrientationU3Ed__29_MoveNext_m68332CED94D7FA13E233246364AEFF987BB7F35F (void);
// 0x0000021F System.Object VRAssemblyDebugger/<TestSpecificAssemblyPointOrientation>d__29::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CTestSpecificAssemblyPointOrientationU3Ed__29_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8D12D60712C0259F9A0F546EA7BD12623ECA146F (void);
// 0x00000220 System.Void VRAssemblyDebugger/<TestSpecificAssemblyPointOrientation>d__29::System.Collections.IEnumerator.Reset()
extern void U3CTestSpecificAssemblyPointOrientationU3Ed__29_System_Collections_IEnumerator_Reset_mF04B89F437DADBE1B555DF45F4471373FE045AFF (void);
// 0x00000221 System.Object VRAssemblyDebugger/<TestSpecificAssemblyPointOrientation>d__29::System.Collections.IEnumerator.get_Current()
extern void U3CTestSpecificAssemblyPointOrientationU3Ed__29_System_Collections_IEnumerator_get_Current_m70C8B298768F8D3616C6993EADE553B9C97E9A7D (void);
// 0x00000222 System.Void VRAssemblyInputManager::Start()
extern void VRAssemblyInputManager_Start_mA071D9867E841360986E8C25E0B0689565C15B51 (void);
// 0x00000223 System.Void VRAssemblyInputManager::Update()
extern void VRAssemblyInputManager_Update_m5F5EFC6D39C57E239CEFA69E578E9789DBEB4A5D (void);
// 0x00000224 System.Void VRAssemblyInputManager::HandleUserInput()
extern void VRAssemblyInputManager_HandleUserInput_mD95622AC11C59557AAD9693685AC4E4AF0B9F9B6 (void);
// 0x00000225 System.Void VRAssemblyInputManager::TriggerViewAdjustment()
extern void VRAssemblyInputManager_TriggerViewAdjustment_mCF5B8656FFFF2F4313D81F47D48EF3D824D3AEEA (void);
// 0x00000226 System.Void VRAssemblyInputManager::SetInputEnabled(System.Boolean)
extern void VRAssemblyInputManager_SetInputEnabled_mBAFD23791634AB7C79B70274AC68511F8DB47B56 (void);
// 0x00000227 System.Void VRAssemblyInputManager::SetAdjustViewKey(UnityEngine.KeyCode)
extern void VRAssemblyInputManager_SetAdjustViewKey_m4CD9D688CC342CB065496781B0263907C71D3415 (void);
// 0x00000228 System.Boolean VRAssemblyInputManager::IsInputEnabled()
extern void VRAssemblyInputManager_IsInputEnabled_mB36B4B78547223AC46C3B29212BBAD8AD8EDFDA6 (void);
// 0x00000229 UnityEngine.KeyCode VRAssemblyInputManager::GetAdjustViewKey()
extern void VRAssemblyInputManager_GetAdjustViewKey_m1F671ABA14E0F0F762D8DF3431A60F6ECE65FB0A (void);
// 0x0000022A System.Void VRAssemblyInputManager::ShowConfigInfo()
extern void VRAssemblyInputManager_ShowConfigInfo_mAE157D5244A6CAA8CB37FD237DABCD39A11815F8 (void);
// 0x0000022B System.Void VRAssemblyInputManager::ManualTriggerViewAdjustment()
extern void VRAssemblyInputManager_ManualTriggerViewAdjustment_mBA2B0E3CB91C06DF5386F52DEF8259F0E30B2699 (void);
// 0x0000022C System.Void VRAssemblyInputManager::.ctor()
extern void VRAssemblyInputManager__ctor_mE112945EA6B078E0A6C7847779FC55D210937E1C (void);
// 0x0000022D System.Void VRAssemblyManager::Start()
extern void VRAssemblyManager_Start_mDA576639DB8A0362297780138D4F3BC36920A994 (void);
// 0x0000022E System.Void VRAssemblyManager::InitializeVRAssembly()
extern void VRAssemblyManager_InitializeVRAssembly_m61B42336F65A0C38B9B418AC8570C935F1DA2467 (void);
// 0x0000022F System.Void VRAssemblyManager::ValidateComponents()
extern void VRAssemblyManager_ValidateComponents_m0F445CC8D29914F78350C91C215AC8A0702AFE15 (void);
// 0x00000230 System.Void VRAssemblyManager::RegisterEvents()
extern void VRAssemblyManager_RegisterEvents_m2955263D52A07D862808B447F67DB13A10D1CC3A (void);
// 0x00000231 System.Void VRAssemblyManager::StartVRAssembly()
extern void VRAssemblyManager_StartVRAssembly_mDD774F0335B0D92CC78ECD48E28FE47DF484BFE2 (void);
// 0x00000232 System.Collections.IEnumerator VRAssemblyManager::VRAssemblySequence()
extern void VRAssemblyManager_VRAssemblySequence_m4F60D5FAF4CA6701136808FE51BD120DB956FBCF (void);
// 0x00000233 System.Void VRAssemblyManager::OnAssemblyStepStart(AssemblyPart,AssemblyPart,System.String)
extern void VRAssemblyManager_OnAssemblyStepStart_m68CEC5F770F53F8920BC948047D761031F8C6D56 (void);
// 0x00000234 System.Collections.IEnumerator VRAssemblyManager::HandleVRAssemblyStep(AssemblyPart,AssemblyPart,System.String)
extern void VRAssemblyManager_HandleVRAssemblyStep_m0230321D5CC43CA35079CDA265EE3A58A81F0DFB (void);
// 0x00000235 System.Boolean VRAssemblyManager::get_IsVRPreparationComplete()
extern void VRAssemblyManager_get_IsVRPreparationComplete_mB97875686DCB650689742F75CD81C51FD8346F74 (void);
// 0x00000236 System.Collections.IEnumerator VRAssemblyManager::WaitForVRPreparationComplete()
extern void VRAssemblyManager_WaitForVRPreparationComplete_mDB066982391FD0FFCF4B8D5740CA3D5A23705A48 (void);
// 0x00000237 System.Void VRAssemblyManager::OnAssemblyStepEnd()
extern void VRAssemblyManager_OnAssemblyStepEnd_mAB514E9B1D4F5C641513091A2E6EA1B88EE73021 (void);
// 0x00000238 System.Boolean VRAssemblyManager::ShouldProvideGuidance(AssemblyPart,AssemblyPart)
extern void VRAssemblyManager_ShouldProvideGuidance_m31AA3A4E040B7D4DDD7282F100EE48617A63248B (void);
// 0x00000239 UnityEngine.Vector3 VRAssemblyManager::CalculateOptimalViewingPosition(AssemblyPart,AssemblyPart)
extern void VRAssemblyManager_CalculateOptimalViewingPosition_m60BD83EDB1B475046C19BB370054F21C9EB514DD (void);
// 0x0000023A System.Collections.IEnumerator VRAssemblyManager::WaitForUserConfirmation(System.String)
extern void VRAssemblyManager_WaitForUserConfirmation_mE0967F620CDABE033D93E2E06F8235D0CF79EE9D (void);
// 0x0000023B System.Void VRAssemblyManager::SetVRMode(System.Boolean)
extern void VRAssemblyManager_SetVRMode_m569524A5A1C7422D5C9B7186A742C0AD971D1016 (void);
// 0x0000023C System.Void VRAssemblyManager::SetAutoPositioning(System.Boolean)
extern void VRAssemblyManager_SetAutoPositioning_mBE16B86A3539819DDBE0A4C6B7CC7E923714E3F8 (void);
// 0x0000023D System.Void VRAssemblyManager::SetPreviewEnabled(System.Boolean)
extern void VRAssemblyManager_SetPreviewEnabled_m74C5E6007111F93F5D1AEAD155144F1A35815495 (void);
// 0x0000023E System.Void VRAssemblyManager::SetGuidanceEnabled(System.Boolean)
extern void VRAssemblyManager_SetGuidanceEnabled_m8EF37827F65350446E0B3A23699BB276AECF6E1E (void);
// 0x0000023F System.Void VRAssemblyManager::EmergencyStop()
extern void VRAssemblyManager_EmergencyStop_m81E3F4576F8183D2094D9B11DBFB734B9E2E9614 (void);
// 0x00000240 System.Boolean VRAssemblyManager::get_IsVRActive()
extern void VRAssemblyManager_get_IsVRActive_m7DAF896FB9B21197C36955BD5E67E4700AFD03DB (void);
// 0x00000241 System.Void VRAssemblyManager::Update()
extern void VRAssemblyManager_Update_m7BD02B95DCF357D82EFFB9FC7EE883C46CA96266 (void);
// 0x00000242 System.Void VRAssemblyManager::OnDestroy()
extern void VRAssemblyManager_OnDestroy_mC7E461C5726252E563A6CB849A40DF1AA8D2C26D (void);
// 0x00000243 System.Void VRAssemblyManager::.ctor()
extern void VRAssemblyManager__ctor_mB8DFC25C0B600A60B3AE7F237FFAEF2A280163CB (void);
// 0x00000244 System.Void VRAssemblyManager/<VRAssemblySequence>d__20::.ctor(System.Int32)
extern void U3CVRAssemblySequenceU3Ed__20__ctor_m15C1BA205A65889F3B1AA4687FA21CF3CBDB15E5 (void);
// 0x00000245 System.Void VRAssemblyManager/<VRAssemblySequence>d__20::System.IDisposable.Dispose()
extern void U3CVRAssemblySequenceU3Ed__20_System_IDisposable_Dispose_m4E450CF9D5A7DAA93754195466037BBA4332AC7A (void);
// 0x00000246 System.Boolean VRAssemblyManager/<VRAssemblySequence>d__20::MoveNext()
extern void U3CVRAssemblySequenceU3Ed__20_MoveNext_m94504F439833F8C405887E9B22DCA8D74C74F110 (void);
// 0x00000247 System.Object VRAssemblyManager/<VRAssemblySequence>d__20::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CVRAssemblySequenceU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7C9ADD12540525BB4E1E21A8EA0298FA27BDD0EE (void);
// 0x00000248 System.Void VRAssemblyManager/<VRAssemblySequence>d__20::System.Collections.IEnumerator.Reset()
extern void U3CVRAssemblySequenceU3Ed__20_System_Collections_IEnumerator_Reset_m25E4812F67F15F87525A091CFC2800258B5EC185 (void);
// 0x00000249 System.Object VRAssemblyManager/<VRAssemblySequence>d__20::System.Collections.IEnumerator.get_Current()
extern void U3CVRAssemblySequenceU3Ed__20_System_Collections_IEnumerator_get_Current_m37BD81E157F96F2B0D238706E54D60333BA4FF8D (void);
// 0x0000024A System.Void VRAssemblyManager/<HandleVRAssemblyStep>d__22::.ctor(System.Int32)
extern void U3CHandleVRAssemblyStepU3Ed__22__ctor_m5B8BD705CB5456122031EAC852D509B48AFC7BDC (void);
// 0x0000024B System.Void VRAssemblyManager/<HandleVRAssemblyStep>d__22::System.IDisposable.Dispose()
extern void U3CHandleVRAssemblyStepU3Ed__22_System_IDisposable_Dispose_mC252729B34F64E9BE86FD0F29859A4342EEFFFC3 (void);
// 0x0000024C System.Boolean VRAssemblyManager/<HandleVRAssemblyStep>d__22::MoveNext()
extern void U3CHandleVRAssemblyStepU3Ed__22_MoveNext_mC4148A804FBEEDFE908BBDC1A5E0812BC93B3A44 (void);
// 0x0000024D System.Object VRAssemblyManager/<HandleVRAssemblyStep>d__22::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CHandleVRAssemblyStepU3Ed__22_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m91CB3B7FBE56222FB39BB88518933448D3CE5638 (void);
// 0x0000024E System.Void VRAssemblyManager/<HandleVRAssemblyStep>d__22::System.Collections.IEnumerator.Reset()
extern void U3CHandleVRAssemblyStepU3Ed__22_System_Collections_IEnumerator_Reset_m616ED41E4B560E64832BE9DAC1166B4A548B9A79 (void);
// 0x0000024F System.Object VRAssemblyManager/<HandleVRAssemblyStep>d__22::System.Collections.IEnumerator.get_Current()
extern void U3CHandleVRAssemblyStepU3Ed__22_System_Collections_IEnumerator_get_Current_mFAF826A344407DA9BFFA9CFF231756CF4BDF86D6 (void);
// 0x00000250 System.Void VRAssemblyManager/<WaitForVRPreparationComplete>d__25::.ctor(System.Int32)
extern void U3CWaitForVRPreparationCompleteU3Ed__25__ctor_mB6081A3F963A624E483D6E2CB20EB43B42D6D259 (void);
// 0x00000251 System.Void VRAssemblyManager/<WaitForVRPreparationComplete>d__25::System.IDisposable.Dispose()
extern void U3CWaitForVRPreparationCompleteU3Ed__25_System_IDisposable_Dispose_m14D28E34281E8A0D815BD510A4BE3EEDC50BD334 (void);
// 0x00000252 System.Boolean VRAssemblyManager/<WaitForVRPreparationComplete>d__25::MoveNext()
extern void U3CWaitForVRPreparationCompleteU3Ed__25_MoveNext_m974547382090787B2AC4948000C97135A3D19717 (void);
// 0x00000253 System.Object VRAssemblyManager/<WaitForVRPreparationComplete>d__25::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CWaitForVRPreparationCompleteU3Ed__25_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m87E49B1C17012F82B835A84F695CCE93669C23C9 (void);
// 0x00000254 System.Void VRAssemblyManager/<WaitForVRPreparationComplete>d__25::System.Collections.IEnumerator.Reset()
extern void U3CWaitForVRPreparationCompleteU3Ed__25_System_Collections_IEnumerator_Reset_m0DE129C8A70A9F4C2EC4EE4010E1BDE0D735630F (void);
// 0x00000255 System.Object VRAssemblyManager/<WaitForVRPreparationComplete>d__25::System.Collections.IEnumerator.get_Current()
extern void U3CWaitForVRPreparationCompleteU3Ed__25_System_Collections_IEnumerator_get_Current_m9AF6C3D510AEDD059A65F4808C80017060DA52A6 (void);
// 0x00000256 System.Void VRAssemblyManager/<WaitForUserConfirmation>d__29::.ctor(System.Int32)
extern void U3CWaitForUserConfirmationU3Ed__29__ctor_m828B8C111734364C413EEA80DBAE7818B6693AAB (void);
// 0x00000257 System.Void VRAssemblyManager/<WaitForUserConfirmation>d__29::System.IDisposable.Dispose()
extern void U3CWaitForUserConfirmationU3Ed__29_System_IDisposable_Dispose_m8B19E83C5666C051EB8ABA41181E537E72D7852C (void);
// 0x00000258 System.Boolean VRAssemblyManager/<WaitForUserConfirmation>d__29::MoveNext()
extern void U3CWaitForUserConfirmationU3Ed__29_MoveNext_m9A99851260094FB73B76C0B38B120393B41E223D (void);
// 0x00000259 System.Object VRAssemblyManager/<WaitForUserConfirmation>d__29::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CWaitForUserConfirmationU3Ed__29_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mE2C104580BEDF91066B06522A1CC39F5FF5DE8B9 (void);
// 0x0000025A System.Void VRAssemblyManager/<WaitForUserConfirmation>d__29::System.Collections.IEnumerator.Reset()
extern void U3CWaitForUserConfirmationU3Ed__29_System_Collections_IEnumerator_Reset_m20CAE4230AA07AF2942969D9850D2EF181B074DA (void);
// 0x0000025B System.Object VRAssemblyManager/<WaitForUserConfirmation>d__29::System.Collections.IEnumerator.get_Current()
extern void U3CWaitForUserConfirmationU3Ed__29_System_Collections_IEnumerator_get_Current_m9F68820E433A8DAA48D59E06547A2A4C41734743 (void);
// 0x0000025C UnityEngine.Quaternion VRAssemblyOrientationHelper::CalculateOrientationToCamera(UnityEngine.Transform,UnityEngine.Transform)
extern void VRAssemblyOrientationHelper_CalculateOrientationToCamera_mF47564ED969D38B30467970E816E9B3173192493 (void);
// 0x0000025D UnityEngine.Quaternion VRAssemblyOrientationHelper::CalculateOptimalOrientationToCamera(UnityEngine.Transform,UnityEngine.Transform)
extern void VRAssemblyOrientationHelper_CalculateOptimalOrientationToCamera_m049E4AF65711CD58D2666CFFCCBD2AB9CBAA8642 (void);
// 0x0000025E System.Void VRAssemblyOrientationHelper::TestAssemblyFaceOrientation()
extern void VRAssemblyOrientationHelper_TestAssemblyFaceOrientation_mEC996FE985F0125881DC009D65528AA547AC7695 (void);
// 0x0000025F System.Void VRAssemblyOrientationHelper::ApplyOrientationToAssemblyRoot(UnityEngine.Transform,UnityEngine.Transform,UnityEngine.Transform,System.Boolean)
extern void VRAssemblyOrientationHelper_ApplyOrientationToAssemblyRoot_mFB1217FF274605584C302693443A8DE70B8395AD (void);
// 0x00000260 System.Void VRAssemblyOrientationHelper::.ctor()
extern void VRAssemblyOrientationHelper__ctor_mE949CDCE7B99F7F2DA680CBF98BE4940A8722DAF (void);
// 0x00000261 System.Void VRAssemblyPositioner::Start()
extern void VRAssemblyPositioner_Start_mFFF6952551522C27AD0B0E56278CFC8B8941FD26 (void);
// 0x00000262 System.Void VRAssemblyPositioner::InitializePositioner()
extern void VRAssemblyPositioner_InitializePositioner_m9124E7DB33BAAF7E4A553CB33D0D19AACAFFA47A (void);
// 0x00000263 UnityEngine.Transform VRAssemblyPositioner::FindAssemblyRoot()
extern void VRAssemblyPositioner_FindAssemblyRoot_m76F73E3280D092430590A3C5935EA6906082FC54 (void);
// 0x00000264 UnityEngine.Transform VRAssemblyPositioner::FindCommonParent(AssemblyPart[])
extern void VRAssemblyPositioner_FindCommonParent_mFD993971AE6B55D04A2F1BB88F3BFF82BE71DBF5 (void);
// 0x00000265 UnityEngine.Transform VRAssemblyPositioner::FindCommonAncestor(UnityEngine.Transform,UnityEngine.Transform)
extern void VRAssemblyPositioner_FindCommonAncestor_m8BF526F0357F659DBA58046175FF095B320458B7 (void);
// 0x00000266 UnityEngine.Vector3 VRAssemblyPositioner::CalculateOptimalAssemblyPosition()
extern void VRAssemblyPositioner_CalculateOptimalAssemblyPosition_mCCCA0B80E9FB0D9CBC24BA71FAE44CDC2E9C39C0 (void);
// 0x00000267 System.ValueTuple`2<UnityEngine.Vector3,UnityEngine.Quaternion> VRAssemblyPositioner::CalculateOptimalPositionForAssemblyPoint(AssemblyPart,AssemblyPart,System.Int32,System.Int32)
extern void VRAssemblyPositioner_CalculateOptimalPositionForAssemblyPoint_mF76630F9A037F2F91553F2827D908FA293BDDBEB (void);
// 0x00000268 UnityEngine.Quaternion VRAssemblyPositioner::CalculateOptimalAssemblyRotation(UnityEngine.Vector3)
extern void VRAssemblyPositioner_CalculateOptimalAssemblyRotation_m1B4D78A138191B6394B7C26C3C2D0A7E4FF01FF2 (void);
// 0x00000269 UnityEngine.Quaternion VRAssemblyPositioner::CalculateSimpleOrientationToCamera(UnityEngine.Transform,UnityEngine.Transform)
extern void VRAssemblyPositioner_CalculateSimpleOrientationToCamera_mB325A81E6266A102E64FD0399CA9E1600FFE81F3 (void);
// 0x0000026A System.Collections.IEnumerator VRAssemblyPositioner::AlignMountPointToCameraByRotatingAssemblyRoot(AssemblyPart,System.Int32,UnityEngine.Transform,System.Boolean)
extern void VRAssemblyPositioner_AlignMountPointToCameraByRotatingAssemblyRoot_m4C5C316E1F8245C13EC6254EA57E8036216077DB (void);
// 0x0000026B System.Collections.IEnumerator VRAssemblyPositioner::AdjustViewForUser()
extern void VRAssemblyPositioner_AdjustViewForUser_mC79C505C188EA75655EABB206EF0FA4C2E151118 (void);
// 0x0000026C System.Collections.IEnumerator VRAssemblyPositioner::AdjustOrientationForAssemblyStep(System.String,System.Int32,System.Boolean)
extern void VRAssemblyPositioner_AdjustOrientationForAssemblyStep_m7CB1A2538FBC14BE0B4DAFCF6F0279783B6B0D6F (void);
// 0x0000026D AssemblyPart VRAssemblyPositioner::FindAssemblyPartByName(System.String)
extern void VRAssemblyPositioner_FindAssemblyPartByName_mFC89B480A960886DB31C7EE3AF53E4813A11E33A (void);
// 0x0000026E UnityEngine.Transform VRAssemblyPositioner::GetCurrentCamera()
extern void VRAssemblyPositioner_GetCurrentCamera_mCFCABE3ADCC01A83A7D5AA9293E9268E4383E727 (void);
// 0x0000026F UnityEngine.Quaternion VRAssemblyPositioner::CalculateConfigurableOrientationToCamera(UnityEngine.Transform,UnityEngine.Transform,System.Boolean)
extern void VRAssemblyPositioner_CalculateConfigurableOrientationToCamera_mA0A6EE3AFD791D09A5C27FF83B58706DC252B755 (void);
// 0x00000270 System.Collections.IEnumerator VRAssemblyPositioner::MoveAssemblyToOptimalPosition()
extern void VRAssemblyPositioner_MoveAssemblyToOptimalPosition_m64DD6F7BC72200DCB2B5D1495437C304CF1BF1FA (void);
// 0x00000271 System.Collections.IEnumerator VRAssemblyPositioner::AdjustOrientationForAssemblyStep(AssemblyPart,AssemblyPart)
extern void VRAssemblyPositioner_AdjustOrientationForAssemblyStep_m893501C12F225D2ADF3A2AA5A69285FBB35B833E (void);
// 0x00000272 System.Collections.IEnumerator VRAssemblyPositioner::AdjustOrientationForAssemblyStepSimple(AssemblyPart,AssemblyPart)
extern void VRAssemblyPositioner_AdjustOrientationForAssemblyStepSimple_m45A85A5F4AEE0991EED1A32389FA0BC4C9C5C4A5 (void);
// 0x00000273 System.Collections.IEnumerator VRAssemblyPositioner::RotateAssemblyToOrientation(UnityEngine.Quaternion,System.Single)
extern void VRAssemblyPositioner_RotateAssemblyToOrientation_m903DDB21A7FB97B74763E9CD47A81045A9985342 (void);
// 0x00000274 System.Collections.IEnumerator VRAssemblyPositioner::RestoreOriginalPosition()
extern void VRAssemblyPositioner_RestoreOriginalPosition_m229244D2F6DC89EE58101C4E71A089FF5CC5EBAB (void);
// 0x00000275 System.Collections.IEnumerator VRAssemblyPositioner::MoveAssemblyToPosition(UnityEngine.Vector3,UnityEngine.Quaternion)
extern void VRAssemblyPositioner_MoveAssemblyToPosition_m3265AEF75C780D7AF67AF754063FC623CFD9D381 (void);
// 0x00000276 System.Void VRAssemblyPositioner::OnAssemblyStart()
extern void VRAssemblyPositioner_OnAssemblyStart_m1CC06174648066EB18079253DAEC0CF93F6CDCE4 (void);
// 0x00000277 System.Void VRAssemblyPositioner::OnAssemblyStepStart(AssemblyPart,AssemblyPart)
extern void VRAssemblyPositioner_OnAssemblyStepStart_mB2FDD80F3FF3212DA2D1DAB197AACFAC04C4DC54 (void);
// 0x00000278 System.Void VRAssemblyPositioner::OnAssemblyEnd()
extern void VRAssemblyPositioner_OnAssemblyEnd_m05C00CAB697658AA0CA5145709B5828DD36B3807 (void);
// 0x00000279 System.Void VRAssemblyPositioner::OnDrawGizmos()
extern void VRAssemblyPositioner_OnDrawGizmos_mD45B6543C4D4D7B39E5FB7C31F5FEF7B7D814AFE (void);
// 0x0000027A System.Void VRAssemblyPositioner::.ctor()
extern void VRAssemblyPositioner__ctor_mBDA40D1EDDAD06C5DF5C442F6384186127C9F720 (void);
// 0x0000027B System.Void VRAssemblyPositioner/<AlignMountPointToCameraByRotatingAssemblyRoot>d__21::.ctor(System.Int32)
extern void U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21__ctor_m0D0B0C195FCC2C55AA072ECC3862628B281A9D11 (void);
// 0x0000027C System.Void VRAssemblyPositioner/<AlignMountPointToCameraByRotatingAssemblyRoot>d__21::System.IDisposable.Dispose()
extern void U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_System_IDisposable_Dispose_mD275EFBB76EF555B83760CB9046C83B871785FBB (void);
// 0x0000027D System.Boolean VRAssemblyPositioner/<AlignMountPointToCameraByRotatingAssemblyRoot>d__21::MoveNext()
extern void U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_MoveNext_mC27FF8AD8F88F10E3E52A291E183C5A0CA66D378 (void);
// 0x0000027E System.Object VRAssemblyPositioner/<AlignMountPointToCameraByRotatingAssemblyRoot>d__21::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m72870BED84F078D5D9CEF398B90449A8118FB154 (void);
// 0x0000027F System.Void VRAssemblyPositioner/<AlignMountPointToCameraByRotatingAssemblyRoot>d__21::System.Collections.IEnumerator.Reset()
extern void U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_System_Collections_IEnumerator_Reset_mBD2FD28D6BB83701D6B287AF9D6FD9EBDB1744FE (void);
// 0x00000280 System.Object VRAssemblyPositioner/<AlignMountPointToCameraByRotatingAssemblyRoot>d__21::System.Collections.IEnumerator.get_Current()
extern void U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_System_Collections_IEnumerator_get_Current_m6D3579E96F47F1CA0C3D4FADF6932540CE116777 (void);
// 0x00000281 System.Void VRAssemblyPositioner/<AdjustViewForUser>d__22::.ctor(System.Int32)
extern void U3CAdjustViewForUserU3Ed__22__ctor_m006678A83626B4118E9B48C8834C283A399CF69A (void);
// 0x00000282 System.Void VRAssemblyPositioner/<AdjustViewForUser>d__22::System.IDisposable.Dispose()
extern void U3CAdjustViewForUserU3Ed__22_System_IDisposable_Dispose_mB593F54BDCD8AC0E1CC9500E2D7D43EE916CCA8E (void);
// 0x00000283 System.Boolean VRAssemblyPositioner/<AdjustViewForUser>d__22::MoveNext()
extern void U3CAdjustViewForUserU3Ed__22_MoveNext_mF5D76945924B72DD55643866906B3FAAC8665913 (void);
// 0x00000284 System.Object VRAssemblyPositioner/<AdjustViewForUser>d__22::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAdjustViewForUserU3Ed__22_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9525B598EB0788482F6E660F0DD6398E62D3A450 (void);
// 0x00000285 System.Void VRAssemblyPositioner/<AdjustViewForUser>d__22::System.Collections.IEnumerator.Reset()
extern void U3CAdjustViewForUserU3Ed__22_System_Collections_IEnumerator_Reset_m17778E32EB826F2FBC94DD0D4442326D189DACAE (void);
// 0x00000286 System.Object VRAssemblyPositioner/<AdjustViewForUser>d__22::System.Collections.IEnumerator.get_Current()
extern void U3CAdjustViewForUserU3Ed__22_System_Collections_IEnumerator_get_Current_m1A8EEC0A5344E81D4FB3DABC780690563123AFC5 (void);
// 0x00000287 System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__23::.ctor(System.Int32)
extern void U3CAdjustOrientationForAssemblyStepU3Ed__23__ctor_m75AC51E639A3509D82202FAF8ECCEAC579740E26 (void);
// 0x00000288 System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__23::System.IDisposable.Dispose()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__23_System_IDisposable_Dispose_mB6CBC1270D197E10FCCB502F48CBCDE46D7EE528 (void);
// 0x00000289 System.Boolean VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__23::MoveNext()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__23_MoveNext_m2A0113A7AD49513801B6DC2923BA555882E62C7E (void);
// 0x0000028A System.Object VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__23::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__23_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m68E2F9F06421CDDC956139C23A66C6226F60D96A (void);
// 0x0000028B System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__23::System.Collections.IEnumerator.Reset()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__23_System_Collections_IEnumerator_Reset_m27AF98F6C2E32EA7C3DB046B287D21D4BD916D91 (void);
// 0x0000028C System.Object VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__23::System.Collections.IEnumerator.get_Current()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__23_System_Collections_IEnumerator_get_Current_mC92DDC079FD5FA257BE79FF918079A209B053A5F (void);
// 0x0000028D System.Void VRAssemblyPositioner/<MoveAssemblyToOptimalPosition>d__27::.ctor(System.Int32)
extern void U3CMoveAssemblyToOptimalPositionU3Ed__27__ctor_mF2989CE76E89429FECCFD292F46546426A0BD51D (void);
// 0x0000028E System.Void VRAssemblyPositioner/<MoveAssemblyToOptimalPosition>d__27::System.IDisposable.Dispose()
extern void U3CMoveAssemblyToOptimalPositionU3Ed__27_System_IDisposable_Dispose_m040CC09EF1C1C55E0A1429C1C40AA6D4ACD18992 (void);
// 0x0000028F System.Boolean VRAssemblyPositioner/<MoveAssemblyToOptimalPosition>d__27::MoveNext()
extern void U3CMoveAssemblyToOptimalPositionU3Ed__27_MoveNext_m5C84A6F53876BCAAB8462568964A8CD5B9959E1B (void);
// 0x00000290 System.Object VRAssemblyPositioner/<MoveAssemblyToOptimalPosition>d__27::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveAssemblyToOptimalPositionU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC25F303E68D8FD213AD91A8AB6A1664B9F46AC1F (void);
// 0x00000291 System.Void VRAssemblyPositioner/<MoveAssemblyToOptimalPosition>d__27::System.Collections.IEnumerator.Reset()
extern void U3CMoveAssemblyToOptimalPositionU3Ed__27_System_Collections_IEnumerator_Reset_m65FF3DC73365597E0EF08CB82AB70FE8FBCF9E2E (void);
// 0x00000292 System.Object VRAssemblyPositioner/<MoveAssemblyToOptimalPosition>d__27::System.Collections.IEnumerator.get_Current()
extern void U3CMoveAssemblyToOptimalPositionU3Ed__27_System_Collections_IEnumerator_get_Current_m029D021F3BBF7BAFD5E3F6877BF280E47EE6737D (void);
// 0x00000293 System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__28::.ctor(System.Int32)
extern void U3CAdjustOrientationForAssemblyStepU3Ed__28__ctor_m478B426DA67564F70769C7A8DCC77DE206C9296B (void);
// 0x00000294 System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__28::System.IDisposable.Dispose()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__28_System_IDisposable_Dispose_mCF1920AC9A0761E522D9FFBE517B830A0E6E09EA (void);
// 0x00000295 System.Boolean VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__28::MoveNext()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__28_MoveNext_m7F684594D13601CDB11CD33A337A026CCFCBE4EF (void);
// 0x00000296 System.Object VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__28::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA6FDFBF40E74D2CF45C6C8AA05D54FE498D0CA7D (void);
// 0x00000297 System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__28::System.Collections.IEnumerator.Reset()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__28_System_Collections_IEnumerator_Reset_m08D72A9C82C45E47C81AD2A5F18EECE757317490 (void);
// 0x00000298 System.Object VRAssemblyPositioner/<AdjustOrientationForAssemblyStep>d__28::System.Collections.IEnumerator.get_Current()
extern void U3CAdjustOrientationForAssemblyStepU3Ed__28_System_Collections_IEnumerator_get_Current_m7D7EC06E9FAFA81FF34F504DD9D2F105C59CCF5F (void);
// 0x00000299 System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStepSimple>d__29::.ctor(System.Int32)
extern void U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29__ctor_mADF6F2936523932BC402AD3721A098844C8419B5 (void);
// 0x0000029A System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStepSimple>d__29::System.IDisposable.Dispose()
extern void U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_System_IDisposable_Dispose_mAD796BF9E9853F1DC3634FABD1C54A8918968F96 (void);
// 0x0000029B System.Boolean VRAssemblyPositioner/<AdjustOrientationForAssemblyStepSimple>d__29::MoveNext()
extern void U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_MoveNext_mDC6ACD100243A12AC9AD74433CA7052F8C161322 (void);
// 0x0000029C System.Object VRAssemblyPositioner/<AdjustOrientationForAssemblyStepSimple>d__29::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m686C03651D73FBCE812B60EBC0AA79BB8C76707B (void);
// 0x0000029D System.Void VRAssemblyPositioner/<AdjustOrientationForAssemblyStepSimple>d__29::System.Collections.IEnumerator.Reset()
extern void U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_System_Collections_IEnumerator_Reset_m45A1412C3E752EC244BA907D757D5351F56AB467 (void);
// 0x0000029E System.Object VRAssemblyPositioner/<AdjustOrientationForAssemblyStepSimple>d__29::System.Collections.IEnumerator.get_Current()
extern void U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_System_Collections_IEnumerator_get_Current_m88D91229D28C156D57D2358A3771A1C0DDFEA2DD (void);
// 0x0000029F System.Void VRAssemblyPositioner/<RotateAssemblyToOrientation>d__30::.ctor(System.Int32)
extern void U3CRotateAssemblyToOrientationU3Ed__30__ctor_m7B8DAFB80E21B1B53DEBFBD384F9BF86AAA0F147 (void);
// 0x000002A0 System.Void VRAssemblyPositioner/<RotateAssemblyToOrientation>d__30::System.IDisposable.Dispose()
extern void U3CRotateAssemblyToOrientationU3Ed__30_System_IDisposable_Dispose_m939C4A22BD5B47EDB386FCB36DB15CA9103F11BF (void);
// 0x000002A1 System.Boolean VRAssemblyPositioner/<RotateAssemblyToOrientation>d__30::MoveNext()
extern void U3CRotateAssemblyToOrientationU3Ed__30_MoveNext_m89840F02335F25724577553BB0F98938DC5396A7 (void);
// 0x000002A2 System.Object VRAssemblyPositioner/<RotateAssemblyToOrientation>d__30::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRotateAssemblyToOrientationU3Ed__30_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9957A50D7FFE8FCBF787F150F08B29E8C9A12C3E (void);
// 0x000002A3 System.Void VRAssemblyPositioner/<RotateAssemblyToOrientation>d__30::System.Collections.IEnumerator.Reset()
extern void U3CRotateAssemblyToOrientationU3Ed__30_System_Collections_IEnumerator_Reset_mA3B3F7E80BA89970F06B335F2DEF61DC5382F552 (void);
// 0x000002A4 System.Object VRAssemblyPositioner/<RotateAssemblyToOrientation>d__30::System.Collections.IEnumerator.get_Current()
extern void U3CRotateAssemblyToOrientationU3Ed__30_System_Collections_IEnumerator_get_Current_mAF40BAC1D918D12C264ED6A163C0D7FE04467025 (void);
// 0x000002A5 System.Void VRAssemblyPositioner/<RestoreOriginalPosition>d__31::.ctor(System.Int32)
extern void U3CRestoreOriginalPositionU3Ed__31__ctor_m06EFB24057F46B530E96E42095C5E2362D416F07 (void);
// 0x000002A6 System.Void VRAssemblyPositioner/<RestoreOriginalPosition>d__31::System.IDisposable.Dispose()
extern void U3CRestoreOriginalPositionU3Ed__31_System_IDisposable_Dispose_m2A69E59EC0674E66CE7BEDA074798713FF27CD37 (void);
// 0x000002A7 System.Boolean VRAssemblyPositioner/<RestoreOriginalPosition>d__31::MoveNext()
extern void U3CRestoreOriginalPositionU3Ed__31_MoveNext_m80D2FA6C72C39A0A2F83614CCBF460D87D756AF0 (void);
// 0x000002A8 System.Object VRAssemblyPositioner/<RestoreOriginalPosition>d__31::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CRestoreOriginalPositionU3Ed__31_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m85C10E5BD35459696CD125F61ABB83EDF4E4FCD4 (void);
// 0x000002A9 System.Void VRAssemblyPositioner/<RestoreOriginalPosition>d__31::System.Collections.IEnumerator.Reset()
extern void U3CRestoreOriginalPositionU3Ed__31_System_Collections_IEnumerator_Reset_m32B03686DB82B27D976FD318D1F224ED9B619BAA (void);
// 0x000002AA System.Object VRAssemblyPositioner/<RestoreOriginalPosition>d__31::System.Collections.IEnumerator.get_Current()
extern void U3CRestoreOriginalPositionU3Ed__31_System_Collections_IEnumerator_get_Current_m84B7DA2C6FBE436E8E986594089D5AF24D864FBB (void);
// 0x000002AB System.Void VRAssemblyPositioner/<MoveAssemblyToPosition>d__32::.ctor(System.Int32)
extern void U3CMoveAssemblyToPositionU3Ed__32__ctor_mF47F7D46AF4B998252FC4221BD01E1F5A76D08DE (void);
// 0x000002AC System.Void VRAssemblyPositioner/<MoveAssemblyToPosition>d__32::System.IDisposable.Dispose()
extern void U3CMoveAssemblyToPositionU3Ed__32_System_IDisposable_Dispose_mFAD0EF442D75C85F4B3AB09EC97831B6A2A7A86D (void);
// 0x000002AD System.Boolean VRAssemblyPositioner/<MoveAssemblyToPosition>d__32::MoveNext()
extern void U3CMoveAssemblyToPositionU3Ed__32_MoveNext_m92BD3A350F9DBD14408C6194EF811A8929866C41 (void);
// 0x000002AE System.Object VRAssemblyPositioner/<MoveAssemblyToPosition>d__32::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CMoveAssemblyToPositionU3Ed__32_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7ED8174506B440633059D41488C7870D6F75D53D (void);
// 0x000002AF System.Void VRAssemblyPositioner/<MoveAssemblyToPosition>d__32::System.Collections.IEnumerator.Reset()
extern void U3CMoveAssemblyToPositionU3Ed__32_System_Collections_IEnumerator_Reset_mE6810E007FA9A72966ABA8F23CC76723DABE127B (void);
// 0x000002B0 System.Object VRAssemblyPositioner/<MoveAssemblyToPosition>d__32::System.Collections.IEnumerator.get_Current()
extern void U3CMoveAssemblyToPositionU3Ed__32_System_Collections_IEnumerator_get_Current_m8E567E0EFA0301F933E85C3639F4E49431E7D0A0 (void);
// 0x000002B1 System.Void VRAssemblyPreview::Start()
extern void VRAssemblyPreview_Start_mED668E216CD1BF3238274F38FB189F86749D67D4 (void);
// 0x000002B2 System.Void VRAssemblyPreview::InitializePreview()
extern void VRAssemblyPreview_InitializePreview_mDFF4DA69A92C3C640A66CE0485E05AB370ACF703 (void);
// 0x000002B3 UnityEngine.Transform VRAssemblyPreview::FindVRControllerTransform()
extern void VRAssemblyPreview_FindVRControllerTransform_mAFFDB44E12C416D7C144012FDDFF63D731B7CA0E (void);
// 0x000002B4 System.Void VRAssemblyPreview::CreateTransparentPreviewMaterial()
extern void VRAssemblyPreview_CreateTransparentPreviewMaterial_mE497510E391F2D45B8B5792CB5CDBEC2F665AAB4 (void);
// 0x000002B5 UnityEngine.GameObject VRAssemblyPreview::CreateAssemblyPreview(AssemblyPart[],System.String)
extern void VRAssemblyPreview_CreateAssemblyPreview_mD5A7DCD6F4AF1B208E0BF28B8723C3D1FA161067 (void);
// 0x000002B6 UnityEngine.GameObject VRAssemblyPreview::CreatePreviewPart(UnityEngine.GameObject,UnityEngine.Transform)
extern void VRAssemblyPreview_CreatePreviewPart_m4CFA31DC394DC9C74857A26BB7A4B0AC5C4803CE (void);
// 0x000002B7 System.Void VRAssemblyPreview::RemoveUnnecessaryComponents(UnityEngine.GameObject)
extern void VRAssemblyPreview_RemoveUnnecessaryComponents_m563B40629121E9F57F2F9371B41AF992EDFC9260 (void);
// 0x000002B8 System.Void VRAssemblyPreview::ApplyPreviewMaterial(UnityEngine.GameObject)
extern void VRAssemblyPreview_ApplyPreviewMaterial_m3384BA6F9D4268A6D2E2F156094CB725B3729724 (void);
// 0x000002B9 UnityEngine.Vector3 VRAssemblyPreview::CalculatePreviewPosition()
extern void VRAssemblyPreview_CalculatePreviewPosition_m82C1750BB96AB66FB89ABFCE5AB1B9040D9F3061 (void);
// 0x000002BA System.Void VRAssemblyPreview::StartAutoRotation()
extern void VRAssemblyPreview_StartAutoRotation_m94C67A95F47BAE5E3A0E4592258CBF29F5FE9E7E (void);
// 0x000002BB System.Collections.IEnumerator VRAssemblyPreview::AutoRotatePreview()
extern void VRAssemblyPreview_AutoRotatePreview_m36965A4615E1D102B093C8ADF3BC80401625343F (void);
// 0x000002BC System.Void VRAssemblyPreview::StopAutoRotation()
extern void VRAssemblyPreview_StopAutoRotation_m997BA0A1A20DF95CD892A377F0BAF4DFCC6AA4D7 (void);
// 0x000002BD System.Void VRAssemblyPreview::HighlightPart(System.String)
extern void VRAssemblyPreview_HighlightPart_mED7B531F23C5CA7259EA2981C38FD8BCF70EBE26 (void);
// 0x000002BE System.Void VRAssemblyPreview::ApplyHighlightMaterial(UnityEngine.GameObject)
extern void VRAssemblyPreview_ApplyHighlightMaterial_mF7D0CB782613B0A798605598ACBD2A84D4AFC6D7 (void);
// 0x000002BF System.Void VRAssemblyPreview::UpdatePreviewLabel(System.String)
extern void VRAssemblyPreview_UpdatePreviewLabel_mDDACD5208B82FAA0FB2E138005D6F656C62C7709 (void);
// 0x000002C0 System.Void VRAssemblyPreview::ClearPreview()
extern void VRAssemblyPreview_ClearPreview_m4C94B8C33901A3F0ACF93AC9431C3D0C97BC840D (void);
// 0x000002C1 System.Void VRAssemblyPreview::SetPreviewVisible(System.Boolean)
extern void VRAssemblyPreview_SetPreviewVisible_m748E67C64D66E4A7E23E38DAE1FC705D6C0937A7 (void);
// 0x000002C2 System.Void VRAssemblyPreview::Update()
extern void VRAssemblyPreview_Update_m08B0D97AC62AB87B51812F77552F9325944AA62B (void);
// 0x000002C3 System.Void VRAssemblyPreview::CreateStepPreview(AssemblyPart,AssemblyPart,System.String)
extern void VRAssemblyPreview_CreateStepPreview_m0C8F9ACC6A716360C2C05840DE6C831A88F7107F (void);
// 0x000002C4 System.Void VRAssemblyPreview::OnAssemblyStepEnd()
extern void VRAssemblyPreview_OnAssemblyStepEnd_mEDDD449732FEF79B41FB9C25EC54F56ABEE5BCA9 (void);
// 0x000002C5 System.Void VRAssemblyPreview::OnDestroy()
extern void VRAssemblyPreview_OnDestroy_mF713FB4825A4B3722D6293AE530BD67592A58C42 (void);
// 0x000002C6 System.Void VRAssemblyPreview::.ctor()
extern void VRAssemblyPreview__ctor_m64135A87EB8C83AEEAE0361EDA4D9B23F679ED71 (void);
// 0x000002C7 System.Void VRAssemblyPreview/<AutoRotatePreview>d__27::.ctor(System.Int32)
extern void U3CAutoRotatePreviewU3Ed__27__ctor_mA2DA3759648FF5E918D549B13DF0350BBCE2697D (void);
// 0x000002C8 System.Void VRAssemblyPreview/<AutoRotatePreview>d__27::System.IDisposable.Dispose()
extern void U3CAutoRotatePreviewU3Ed__27_System_IDisposable_Dispose_m677C9339AED9A4AE57E1E5B145D36F20BE8D224D (void);
// 0x000002C9 System.Boolean VRAssemblyPreview/<AutoRotatePreview>d__27::MoveNext()
extern void U3CAutoRotatePreviewU3Ed__27_MoveNext_m1D3C137D39CCB4D977B7264816433D6110B35BA6 (void);
// 0x000002CA System.Object VRAssemblyPreview/<AutoRotatePreview>d__27::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CAutoRotatePreviewU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8F92CD0884F8448279C86F50188C9BE659B93638 (void);
// 0x000002CB System.Void VRAssemblyPreview/<AutoRotatePreview>d__27::System.Collections.IEnumerator.Reset()
extern void U3CAutoRotatePreviewU3Ed__27_System_Collections_IEnumerator_Reset_m80E579BC4C4F965234CD610773047C40FF719F5B (void);
// 0x000002CC System.Object VRAssemblyPreview/<AutoRotatePreview>d__27::System.Collections.IEnumerator.get_Current()
extern void U3CAutoRotatePreviewU3Ed__27_System_Collections_IEnumerator_get_Current_mC019D63C87CE3E229D18D6E48A62D08E6295B529 (void);
// 0x000002CD System.Void VRAssemblyStepManager::Start()
extern void VRAssemblyStepManager_Start_m72D6B27C41F36AFE890F7B37056B3ABE02FB78DF (void);
// 0x000002CE System.Collections.IEnumerator VRAssemblyStepManager::PrepareAssemblyStep(System.String,System.Int32)
extern void VRAssemblyStepManager_PrepareAssemblyStep_mC750835B08545AACBDA36F421C977E18BAAD81B3 (void);
// 0x000002CF System.Collections.IEnumerator VRAssemblyStepManager::PrepareAssemblyStep(System.String,System.Int32,System.Boolean)
extern void VRAssemblyStepManager_PrepareAssemblyStep_m28CA76769B5FF321AC452C60258EF6280381AD09 (void);
// 0x000002D0 System.Void VRAssemblyStepManager::SetAutoAdjustOrientation(System.Boolean)
extern void VRAssemblyStepManager_SetAutoAdjustOrientation_m1C11B0B943C64F84C28303644EB74763F19EB50D (void);
// 0x000002D1 System.Void VRAssemblyStepManager::SetDefaultOrientationAxis(System.Boolean)
extern void VRAssemblyStepManager_SetDefaultOrientationAxis_mCAA7E4A6207F9682DE1C53B60B6CD085A602DDC8 (void);
// 0x000002D2 System.Void VRAssemblyStepManager::SetDelayBeforeAnimation(System.Single)
extern void VRAssemblyStepManager_SetDelayBeforeAnimation_m35708D0C4A5C72490B86021A7A3D43CD8A9C2ED2 (void);
// 0x000002D3 System.ValueTuple`3<System.Boolean,System.Boolean,System.Single> VRAssemblyStepManager::GetCurrentConfig()
extern void VRAssemblyStepManager_GetCurrentConfig_m441D5C2FFB1F9895ECC368223B9A20EB199CF5B4 (void);
// 0x000002D4 System.Void VRAssemblyStepManager::ShowConfigInfo()
extern void VRAssemblyStepManager_ShowConfigInfo_m1DA49B5FF6DAAF830F30F97ADB163D06F47BFA53 (void);
// 0x000002D5 System.Void VRAssemblyStepManager::TestPrepareAssemblyStep()
extern void VRAssemblyStepManager_TestPrepareAssemblyStep_m7A7F21A20B8C077A72E132692860D1F721C252BA (void);
// 0x000002D6 System.Void VRAssemblyStepManager::.ctor()
extern void VRAssemblyStepManager__ctor_m1653B6CD8E821AE2C032729D4A23943C122C08D6 (void);
// 0x000002D7 System.Void VRAssemblyStepManager/<PrepareAssemblyStep>d__6::.ctor(System.Int32)
extern void U3CPrepareAssemblyStepU3Ed__6__ctor_mE7075AE756C42E03383ABE6827CB451A2D02007E (void);
// 0x000002D8 System.Void VRAssemblyStepManager/<PrepareAssemblyStep>d__6::System.IDisposable.Dispose()
extern void U3CPrepareAssemblyStepU3Ed__6_System_IDisposable_Dispose_m02AC0D6BD3E36876681CE177F5F02859A0965580 (void);
// 0x000002D9 System.Boolean VRAssemblyStepManager/<PrepareAssemblyStep>d__6::MoveNext()
extern void U3CPrepareAssemblyStepU3Ed__6_MoveNext_m39E448C266CCAEC4CA68E7F237C5BE944CC5D689 (void);
// 0x000002DA System.Object VRAssemblyStepManager/<PrepareAssemblyStep>d__6::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CPrepareAssemblyStepU3Ed__6_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC153B0CD5B76ABE23D9B62949080B20BF56F057A (void);
// 0x000002DB System.Void VRAssemblyStepManager/<PrepareAssemblyStep>d__6::System.Collections.IEnumerator.Reset()
extern void U3CPrepareAssemblyStepU3Ed__6_System_Collections_IEnumerator_Reset_m982FF1DAFB00EAC4B78A875EB23D6EF877AF136A (void);
// 0x000002DC System.Object VRAssemblyStepManager/<PrepareAssemblyStep>d__6::System.Collections.IEnumerator.get_Current()
extern void U3CPrepareAssemblyStepU3Ed__6_System_Collections_IEnumerator_get_Current_mE2B078D3C6BF8242FF514A3A11A69C6B77DA7044 (void);
// 0x000002DD System.Void VRAssemblyStepManager/<PrepareAssemblyStep>d__7::.ctor(System.Int32)
extern void U3CPrepareAssemblyStepU3Ed__7__ctor_m4CA782BAFF0957AC85A3C7BE7DD61D4DCDC43B1C (void);
// 0x000002DE System.Void VRAssemblyStepManager/<PrepareAssemblyStep>d__7::System.IDisposable.Dispose()
extern void U3CPrepareAssemblyStepU3Ed__7_System_IDisposable_Dispose_mC4B289C29A307D5B6CB38523CED148B3B53D0929 (void);
// 0x000002DF System.Boolean VRAssemblyStepManager/<PrepareAssemblyStep>d__7::MoveNext()
extern void U3CPrepareAssemblyStepU3Ed__7_MoveNext_m12D236973EB066E62EDC93CCF70130E0D2F6A039 (void);
// 0x000002E0 System.Object VRAssemblyStepManager/<PrepareAssemblyStep>d__7::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CPrepareAssemblyStepU3Ed__7_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m28B749622E1767C8347A58468F1438DD18C21ED8 (void);
// 0x000002E1 System.Void VRAssemblyStepManager/<PrepareAssemblyStep>d__7::System.Collections.IEnumerator.Reset()
extern void U3CPrepareAssemblyStepU3Ed__7_System_Collections_IEnumerator_Reset_mD1C68AF192BFAEF82C17C4654A2C796FEE9207FF (void);
// 0x000002E2 System.Object VRAssemblyStepManager/<PrepareAssemblyStep>d__7::System.Collections.IEnumerator.get_Current()
extern void U3CPrepareAssemblyStepU3Ed__7_System_Collections_IEnumerator_get_Current_m688E40F03DE06B7464051366E169A2447EC14E95 (void);
// 0x000002E3 System.Void VRInputManager::add_OnPartSelected(System.Action`1<AssemblyPart>)
extern void VRInputManager_add_OnPartSelected_m5C3070EA0886F30681B28F729759E44945503631 (void);
// 0x000002E4 System.Void VRInputManager::remove_OnPartSelected(System.Action`1<AssemblyPart>)
extern void VRInputManager_remove_OnPartSelected_mE5B70AD10CA05140511B584946DAFFF157DCD00D (void);
// 0x000002E5 System.Void VRInputManager::add_OnNextStepRequested(System.Action)
extern void VRInputManager_add_OnNextStepRequested_m2AED815A84BB90AED60FA2ADBDB9BE01850C5128 (void);
// 0x000002E6 System.Void VRInputManager::remove_OnNextStepRequested(System.Action)
extern void VRInputManager_remove_OnNextStepRequested_mDC65B60380B65586D84B0AE6486F1D699B3CCA4D (void);
// 0x000002E7 System.Void VRInputManager::add_OnResetRequested(System.Action)
extern void VRInputManager_add_OnResetRequested_m1C1B61023DB588C405AB2FDE0C8D89E0358BFB36 (void);
// 0x000002E8 System.Void VRInputManager::remove_OnResetRequested(System.Action)
extern void VRInputManager_remove_OnResetRequested_mF0F568735A599F592A11B3E3F8BA09AD20DFE065 (void);
// 0x000002E9 System.Void VRInputManager::add_OnReplayRequested(System.Action)
extern void VRInputManager_add_OnReplayRequested_m35F9442BEBC3708327BB67E82AC1AD933A6FBC44 (void);
// 0x000002EA System.Void VRInputManager::remove_OnReplayRequested(System.Action)
extern void VRInputManager_remove_OnReplayRequested_mC7C89A4ED9CEB8D3AAE425FE22981108BFAC25FB (void);
// 0x000002EB System.Void VRInputManager::Start()
extern void VRInputManager_Start_mEDCE09DC068C2A7676C78F6BC85EC184FA2F8CBA (void);
// 0x000002EC System.Void VRInputManager::Update()
extern void VRInputManager_Update_m7292786F9775C1AD6BE6BFFEADDE8C0779FE6607 (void);
// 0x000002ED System.Void VRInputManager::InitializeVRInput()
extern void VRInputManager_InitializeVRInput_m014D294AD01E88C13A296A9320ACB776A9DC9344 (void);
// 0x000002EE System.Void VRInputManager::HandleVRInput()
extern void VRInputManager_HandleVRInput_m2945939741DC28FDAC21E86422F0B328B023B04C (void);
// 0x000002EF System.Void VRInputManager::HandleFallbackInput()
extern void VRInputManager_HandleFallbackInput_m78EE683656B46278F07043FF6E01BE34654F7C7D (void);
// 0x000002F0 System.Void VRInputManager::HandleControllerInput(System.Object,System.Object)
extern void VRInputManager_HandleControllerInput_m9A3A2BC6C842ED3485AE7F633C8A8E78C25B190B (void);
// 0x000002F1 System.Void VRInputManager::HandlePartSelection(System.Object)
extern void VRInputManager_HandlePartSelection_m31C91C31BE9209E526B636D4994BDB56852DA231 (void);
// 0x000002F2 System.Void VRInputManager::SelectPart(AssemblyPart,System.Object)
extern void VRInputManager_SelectPart_m89687CF7C30B69579DB6ABB6E87502C2F5DC3A84 (void);
// 0x000002F3 System.Void VRInputManager::HandleMenuInput()
extern void VRInputManager_HandleMenuInput_mB767C0F4AE4A0A081A0052ED5A3FCDCE13CFC4AE (void);
// 0x000002F4 System.Void VRInputManager::TriggerHapticFeedback(System.Object)
extern void VRInputManager_TriggerHapticFeedback_m914B5A1B27AD7FF6AF0C69D147C977C5726379DD (void);
// 0x000002F5 AssemblyPart VRInputManager::GetSelectedPart()
extern void VRInputManager_GetSelectedPart_mBC791B9E65C1B02EEDE52A970D2F851CFE3DF8D9 (void);
// 0x000002F6 System.Object VRInputManager::GetActiveInteractor()
extern void VRInputManager_GetActiveInteractor_m881063E8E4974B5F28149A27A909EB288536E84E (void);
// 0x000002F7 System.Void VRInputManager::SetInteractionLayerMask(UnityEngine.LayerMask)
extern void VRInputManager_SetInteractionLayerMask_m40C012C138859D2A930F2F5F2E07F89E110A5FB3 (void);
// 0x000002F8 System.Void VRInputManager::SetHapticFeedback(System.Boolean)
extern void VRInputManager_SetHapticFeedback_m84F62D86924CB54B7922FE04A2494933842313C0 (void);
// 0x000002F9 System.Void VRInputManager::TriggerNextStep()
extern void VRInputManager_TriggerNextStep_m9D4087D1FAEDD1B31D3FDFF307777D43E3D32B6A (void);
// 0x000002FA System.Void VRInputManager::TriggerReset()
extern void VRInputManager_TriggerReset_m6078607B188F8C35D0D12E816ED32BA5784C4491 (void);
// 0x000002FB System.Void VRInputManager::TriggerReplay()
extern void VRInputManager_TriggerReplay_m0E79D4ECB60ACD5797AA0702C8E7215EFFB8E19D (void);
// 0x000002FC System.Void VRInputManager::OnDestroy()
extern void VRInputManager_OnDestroy_m83E19ABE0AEB47324FE2F3526117192E9ADEBC1E (void);
// 0x000002FD System.Void VRInputManager::.ctor()
extern void VRInputManager__ctor_m1A0FBE8ECE1AEDE907F35442F5634CBAD5A9A546 (void);
// 0x000002FE System.Void VRSimulator::Start()
extern void VRSimulator_Start_mD3E50DCE68A0531D10D9B0890923C8AD19E2E42C (void);
// 0x000002FF System.Void VRSimulator::Update()
extern void VRSimulator_Update_m9327341FF575FC0397A7F26DEFC504F1461B811C (void);
// 0x00000300 System.Void VRSimulator::InitializeVRSimulator()
extern void VRSimulator_InitializeVRSimulator_m08012E382E2E65F7682B95B68FE09F3962808B6D (void);
// 0x00000301 System.Void VRSimulator::CreateControllerSimulators()
extern void VRSimulator_CreateControllerSimulators_m9515B3DF2549CD6DDAE872426520560CFA1CEB72 (void);
// 0x00000302 System.Void VRSimulator::CreateControllerVisual(UnityEngine.Transform,UnityEngine.Color)
extern void VRSimulator_CreateControllerVisual_mBAC43F3090A61B7A16382DE351F9D71F0D24A0AC (void);
// 0x00000303 System.Void VRSimulator::HandleVRSimulation()
extern void VRSimulator_HandleVRSimulation_mD04E9283442A2697F29376F31694486BF0EB0CCB (void);
// 0x00000304 System.Void VRSimulator::HandleHeadMovement()
extern void VRSimulator_HandleHeadMovement_m89D0144BFCC732DFD5F8B431B5E3F405D888BAC6 (void);
// 0x00000305 System.Void VRSimulator::HandleHeadRotation()
extern void VRSimulator_HandleHeadRotation_m0BAB512A2F654EC70E7E3CCC1E1AA85EC1FC8386 (void);
// 0x00000306 System.Void VRSimulator::HandleControllerSimulation()
extern void VRSimulator_HandleControllerSimulation_m491E50B6FA22C44B18B5B46C8E54DF3D364402D7 (void);
// 0x00000307 System.Void VRSimulator::ClampToVRBounds()
extern void VRSimulator_ClampToVRBounds_m346D4C4FAFE0D69D220BC73482F7A0A41850FE21 (void);
// 0x00000308 UnityEngine.Camera VRSimulator::GetVRCamera()
extern void VRSimulator_GetVRCamera_m3DFB20A1FE02C03F6739D3946B00529BBDE6B986 (void);
// 0x00000309 UnityEngine.Transform VRSimulator::GetControllerTransform(System.Boolean)
extern void VRSimulator_GetControllerTransform_m1C1EA8393D526442039540E5A31909D6F980738B (void);
// 0x0000030A System.Void VRSimulator::ResetVRPosition()
extern void VRSimulator_ResetVRPosition_m62A6C813FFAAF87F3CE95C74B625011322F2B72A (void);
// 0x0000030B System.Void VRSimulator::ToggleVRSimulation()
extern void VRSimulator_ToggleVRSimulation_mE3CB63194F7580CF49D2D50207B6D93767B2F87D (void);
// 0x0000030C System.Void VRSimulator::SetUserHeight(System.Single)
extern void VRSimulator_SetUserHeight_m5EFF0E65925D0C9D487C436847E13B11075A9AF2 (void);
// 0x0000030D System.Void VRSimulator::OnDrawGizmos()
extern void VRSimulator_OnDrawGizmos_m68FA100330BD0AD428DCBC8DF436445AAC857061 (void);
// 0x0000030E System.Void VRSimulator::OnGUI()
extern void VRSimulator_OnGUI_m7DD4CA686C0C1D92F4ADD9CFAB89EF3DF332486D (void);
// 0x0000030F UnityEngine.GUIStyle VRSimulator::EditorGUIStyle()
extern void VRSimulator_EditorGUIStyle_m678D64F4A00ABC95B7E5EC7074CCD8FF99135800 (void);
// 0x00000310 System.Void VRSimulator::.ctor()
extern void VRSimulator__ctor_m7D11F1D5F20D30258E8D21FC8DC28D626C180C31 (void);
// 0x00000311 System.Void VRSystemDebugger::Start()
extern void VRSystemDebugger_Start_mF4D9B8DF58E6FD69789E8744925FE6ED5D5F5CA5 (void);
// 0x00000312 System.Void VRSystemDebugger::Update()
extern void VRSystemDebugger_Update_m6FC02EC2526241D70C8CDD5450C225273B55CAFF (void);
// 0x00000313 System.Void VRSystemDebugger::InitializeDebugger()
extern void VRSystemDebugger_InitializeDebugger_m00E152AC644E455ED18EB8DB4775542780A1EDBB (void);
// 0x00000314 System.Void VRSystemDebugger::CheckVRSystemStatus()
extern void VRSystemDebugger_CheckVRSystemStatus_m1DA318C9E0EE0C15FF2EF29267550B7ECD8AA38E (void);
// 0x00000315 System.Void VRSystemDebugger::TestPositionAdjustment()
extern void VRSystemDebugger_TestPositionAdjustment_mFC5A3F99CFF09B56C21BEBBA3628982E72CE988E (void);
// 0x00000316 System.Void VRSystemDebugger::TestPreviewFunction()
extern void VRSystemDebugger_TestPreviewFunction_mD94D3AB447BE71F612E62305D67F31E55F28697C (void);
// 0x00000317 System.Void VRSystemDebugger::TestGuidanceFunction()
extern void VRSystemDebugger_TestGuidanceFunction_m4AB64ED31A52BF0BD75AD70E226A915951FE55EC (void);
// 0x00000318 System.Void VRSystemDebugger::ResetVRFunctions()
extern void VRSystemDebugger_ResetVRFunctions_m1D0D51958DBE77D2F4AC514BE83FCEF6F771E8EA (void);
// 0x00000319 System.Void VRSystemDebugger::ToggleDebugMode()
extern void VRSystemDebugger_ToggleDebugMode_m3A95E2806E7651F21F95183F1C151B0465F6DC07 (void);
// 0x0000031A System.Void VRSystemDebugger::UpdateDebugInfo()
extern void VRSystemDebugger_UpdateDebugInfo_m4C02BAD48D1BB8392EB8760B9B95FF82FF8AE3C6 (void);
// 0x0000031B System.Void VRSystemDebugger::UpdateStatusText()
extern void VRSystemDebugger_UpdateStatusText_m6A59DD8A2FB1777300016A3ACDF79E8C237B45F4 (void);
// 0x0000031C System.Void VRSystemDebugger::CreateDebugUI()
extern void VRSystemDebugger_CreateDebugUI_m0892B8F0361D6914BCF045FBACB4D6CEE94969BA (void);
// 0x0000031D System.Void VRSystemDebugger::CreateDebugButton(System.String,UnityEngine.Vector2,System.Action,UnityEngine.Transform)
extern void VRSystemDebugger_CreateDebugButton_mD9CD27AF600971B2A1605A75897024DDA83B7E3B (void);
// 0x0000031E System.Void VRSystemDebugger::ShowHelp()
extern void VRSystemDebugger_ShowHelp_m51B9B9CF53B8D9C5477FC883F2F8E3EAA211F581 (void);
// 0x0000031F System.Void VRSystemDebugger::OnGUI()
extern void VRSystemDebugger_OnGUI_m6406E1978F3D72C836069F62E7483C38D821E569 (void);
// 0x00000320 System.Void VRSystemDebugger::.ctor()
extern void VRSystemDebugger__ctor_m13E74A955BFAC048E5CF671518C3BAD1287A56F1 (void);
// 0x00000321 System.Void VRSystemDebugger/<>c__DisplayClass28_0::.ctor()
extern void U3CU3Ec__DisplayClass28_0__ctor_mF171D5F5DC51408281799B284EFE62F2752CA5B2 (void);
// 0x00000322 System.Void VRSystemDebugger/<>c__DisplayClass28_0::<CreateDebugButton>b__0()
extern void U3CU3Ec__DisplayClass28_0_U3CCreateDebugButtonU3Eb__0_mBBC5C967B55B62451B75696B24EA7C15F61D6F82 (void);
// 0x00000323 System.Void VRUserGuidance::Start()
extern void VRUserGuidance_Start_m2C26A5944C138C6EB9D103D0240709E0409E0DA2 (void);
// 0x00000324 System.Void VRUserGuidance::InitializeGuidance()
extern void VRUserGuidance_InitializeGuidance_m111726F47FA961DDD0D503F7BEF48608DAFA63BE (void);
// 0x00000325 System.Void VRUserGuidance::CreateDefaultGuidanceElements()
extern void VRUserGuidance_CreateDefaultGuidanceElements_m0AFE2F40630B4E4C06B0A43CAEBCC18278A63355 (void);
// 0x00000326 UnityEngine.GameObject VRUserGuidance::CreateDefaultArrow()
extern void VRUserGuidance_CreateDefaultArrow_mB91943240AA44802B4DE5AAC6BDF2608DDAB7F1A (void);
// 0x00000327 UnityEngine.GameObject VRUserGuidance::CreateDefaultFloorIndicator()
extern void VRUserGuidance_CreateDefaultFloorIndicator_mA45E47D528074ADD517DB3183F94D65E07BCF562 (void);
// 0x00000328 UnityEngine.Material VRUserGuidance::CreateGlowMaterial()
extern void VRUserGuidance_CreateGlowMaterial_m495FF8571A8E0BDEDE67C09F3DEFEE7E4E63E3B5 (void);
// 0x00000329 System.Collections.IEnumerator VRUserGuidance::GuideUserToOptimalPosition(UnityEngine.Vector3,System.String)
extern void VRUserGuidance_GuideUserToOptimalPosition_mC79A19264F09BC6BCEF153E0CC101D8126AD4F79 (void);
// 0x0000032A UnityEngine.GameObject VRUserGuidance::CreateFloorIndicator(UnityEngine.Vector3)
extern void VRUserGuidance_CreateFloorIndicator_m49DCB1913C2AE2108BAE7A25987E44FA61C959AF (void);
// 0x0000032B UnityEngine.GameObject VRUserGuidance::CreateDirectionArrow(UnityEngine.Vector3)
extern void VRUserGuidance_CreateDirectionArrow_m49DDE0E2C40D72DA7D005ABC9FF2056B175AAEE9 (void);
// 0x0000032C System.Collections.IEnumerator VRUserGuidance::GuidanceAnimationCoroutine()
extern void VRUserGuidance_GuidanceAnimationCoroutine_m3B8D4FD6BF51349E6E5987D0C6D7886C4F5E7E96 (void);
// 0x0000032D System.Collections.IEnumerator VRUserGuidance::WaitForUserPosition(UnityEngine.Vector3,System.Single)
extern void VRUserGuidance_WaitForUserPosition_m25A0AD600F78E2A12D386122D5318A9D40137B3D (void);
// 0x0000032E System.Void VRUserGuidance::HighlightAssemblyArea(UnityEngine.Transform,System.Single)
extern void VRUserGuidance_HighlightAssemblyArea_m8F23D0E3987831C7C6E06FA181BB7786A9F02894 (void);
// 0x0000032F System.Collections.IEnumerator VRUserGuidance::HighlightCoroutine(UnityEngine.Transform,System.Single)
extern void VRUserGuidance_HighlightCoroutine_m266744DF7580311A49F5D1B52BF4068ACBDD071C (void);
// 0x00000330 System.Void VRUserGuidance::ShowGuidanceMessage(System.String)
extern void VRUserGuidance_ShowGuidanceMessage_mC6CD298B1CF141D06FAC053F87B89F593B523BD6 (void);
// 0x00000331 System.Void VRUserGuidance::HideGuidanceMessage()
extern void VRUserGuidance_HideGuidanceMessage_mA90A93C1FC49799AE7431E38E0AE7BED37A8D988 (void);
// 0x00000332 System.Void VRUserGuidance::PlayVoiceGuidance(System.String)
extern void VRUserGuidance_PlayVoiceGuidance_mF07DE289690B3B6D0D078DF1852F39F7601022E6 (void);
// 0x00000333 System.Void VRUserGuidance::StopCurrentGuidance()
extern void VRUserGuidance_StopCurrentGuidance_mBD5A25783D8D2B21DBE870B5C3F799BF2BF3CCE0 (void);
// 0x00000334 System.Void VRUserGuidance::GuideForAssemblyStep(UnityEngine.Vector3,System.String)
extern void VRUserGuidance_GuideForAssemblyStep_mE182EFF483C7A344834439E154DCF1DBD145356D (void);
// 0x00000335 System.Void VRUserGuidance::GuideToBackView(UnityEngine.Transform)
extern void VRUserGuidance_GuideToBackView_mCA2A1CF9AA944EACA2EC5D7C50E05A92ACD8CCFC (void);
// 0x00000336 System.Void VRUserGuidance::OnDestroy()
extern void VRUserGuidance_OnDestroy_m7BF288A0E12B51E4B715EA2C23E9E3A46FC00E68 (void);
// 0x00000337 System.Void VRUserGuidance::.ctor()
extern void VRUserGuidance__ctor_m9F6E3AFCB34E45E926F7ABAA32999A0B63591C9A (void);
// 0x00000338 System.Void VRUserGuidance/<GuideUserToOptimalPosition>d__24::.ctor(System.Int32)
extern void U3CGuideUserToOptimalPositionU3Ed__24__ctor_m1383C15052E14C47D77A90AAE7423BAF6E590D0D (void);
// 0x00000339 System.Void VRUserGuidance/<GuideUserToOptimalPosition>d__24::System.IDisposable.Dispose()
extern void U3CGuideUserToOptimalPositionU3Ed__24_System_IDisposable_Dispose_mEB8F8FF06B92E65B2167F12F36A9A618A6CD0F04 (void);
// 0x0000033A System.Boolean VRUserGuidance/<GuideUserToOptimalPosition>d__24::MoveNext()
extern void U3CGuideUserToOptimalPositionU3Ed__24_MoveNext_m81B10167D70F0B0245058FFBEFB3F5883B2DE8EC (void);
// 0x0000033B System.Object VRUserGuidance/<GuideUserToOptimalPosition>d__24::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CGuideUserToOptimalPositionU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB81D6CD4EF4AAF55F4E784DFC0C52997104AB727 (void);
// 0x0000033C System.Void VRUserGuidance/<GuideUserToOptimalPosition>d__24::System.Collections.IEnumerator.Reset()
extern void U3CGuideUserToOptimalPositionU3Ed__24_System_Collections_IEnumerator_Reset_m6AE49C806C145AF9AF743741D6813856FB20E436 (void);
// 0x0000033D System.Object VRUserGuidance/<GuideUserToOptimalPosition>d__24::System.Collections.IEnumerator.get_Current()
extern void U3CGuideUserToOptimalPositionU3Ed__24_System_Collections_IEnumerator_get_Current_m05DA6C906386DC256EF371B3DC563E92E1BCF2D3 (void);
// 0x0000033E System.Void VRUserGuidance/<GuidanceAnimationCoroutine>d__27::.ctor(System.Int32)
extern void U3CGuidanceAnimationCoroutineU3Ed__27__ctor_m41C3F2CB62ADDF6131E88E9D478201915A12D72B (void);
// 0x0000033F System.Void VRUserGuidance/<GuidanceAnimationCoroutine>d__27::System.IDisposable.Dispose()
extern void U3CGuidanceAnimationCoroutineU3Ed__27_System_IDisposable_Dispose_m3E698714563A7AB7FE0851759B017FFBE7571D8E (void);
// 0x00000340 System.Boolean VRUserGuidance/<GuidanceAnimationCoroutine>d__27::MoveNext()
extern void U3CGuidanceAnimationCoroutineU3Ed__27_MoveNext_mE7639A38359A58563755BBA6746487EF891FB5C3 (void);
// 0x00000341 System.Object VRUserGuidance/<GuidanceAnimationCoroutine>d__27::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CGuidanceAnimationCoroutineU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCB8CA2A7022D7B8157A2FFBEF84D936A7F2D1218 (void);
// 0x00000342 System.Void VRUserGuidance/<GuidanceAnimationCoroutine>d__27::System.Collections.IEnumerator.Reset()
extern void U3CGuidanceAnimationCoroutineU3Ed__27_System_Collections_IEnumerator_Reset_m22B7737FA1DE2913391936766502E491C30B285A (void);
// 0x00000343 System.Object VRUserGuidance/<GuidanceAnimationCoroutine>d__27::System.Collections.IEnumerator.get_Current()
extern void U3CGuidanceAnimationCoroutineU3Ed__27_System_Collections_IEnumerator_get_Current_m398E02A923F8C6F2634C18740BF345F5E32A34AF (void);
// 0x00000344 System.Void VRUserGuidance/<WaitForUserPosition>d__28::.ctor(System.Int32)
extern void U3CWaitForUserPositionU3Ed__28__ctor_m0BECD0EF5CF852EADD90DF2D5232960ACDB06907 (void);
// 0x00000345 System.Void VRUserGuidance/<WaitForUserPosition>d__28::System.IDisposable.Dispose()
extern void U3CWaitForUserPositionU3Ed__28_System_IDisposable_Dispose_m945E3580AB7FA758B5E6D66ACFFEEDDA3B144734 (void);
// 0x00000346 System.Boolean VRUserGuidance/<WaitForUserPosition>d__28::MoveNext()
extern void U3CWaitForUserPositionU3Ed__28_MoveNext_m69132C50DDCD695621E34947CA71BCC29A972CAC (void);
// 0x00000347 System.Object VRUserGuidance/<WaitForUserPosition>d__28::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CWaitForUserPositionU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1D1F574D4CBF44FC28EFCDBD7EFE663E1C7F8B3C (void);
// 0x00000348 System.Void VRUserGuidance/<WaitForUserPosition>d__28::System.Collections.IEnumerator.Reset()
extern void U3CWaitForUserPositionU3Ed__28_System_Collections_IEnumerator_Reset_mE174ADE025D5BDE79204F7F552D442729054DD6D (void);
// 0x00000349 System.Object VRUserGuidance/<WaitForUserPosition>d__28::System.Collections.IEnumerator.get_Current()
extern void U3CWaitForUserPositionU3Ed__28_System_Collections_IEnumerator_get_Current_mA076668FDC8A7D8845AD524FFEEC97CB07220914 (void);
// 0x0000034A System.Void VRUserGuidance/<HighlightCoroutine>d__30::.ctor(System.Int32)
extern void U3CHighlightCoroutineU3Ed__30__ctor_m4801E771114AF06F0F1DF0AD483BF36DF6F73DFA (void);
// 0x0000034B System.Void VRUserGuidance/<HighlightCoroutine>d__30::System.IDisposable.Dispose()
extern void U3CHighlightCoroutineU3Ed__30_System_IDisposable_Dispose_m1355F4D82BABB77482C78A8B8D47CB9F54A4E42D (void);
// 0x0000034C System.Boolean VRUserGuidance/<HighlightCoroutine>d__30::MoveNext()
extern void U3CHighlightCoroutineU3Ed__30_MoveNext_mA4F4F16B04D4A5CA97C46D94E1C65F499625AA85 (void);
// 0x0000034D System.Object VRUserGuidance/<HighlightCoroutine>d__30::System.Collections.Generic.IEnumerator<System.Object>.get_Current()
extern void U3CHighlightCoroutineU3Ed__30_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m58CAB82258849F24804D018BC8BDAFD7891FD733 (void);
// 0x0000034E System.Void VRUserGuidance/<HighlightCoroutine>d__30::System.Collections.IEnumerator.Reset()
extern void U3CHighlightCoroutineU3Ed__30_System_Collections_IEnumerator_Reset_m4541F0BC302E8F10FFF049C61292FB3086C8E04D (void);
// 0x0000034F System.Object VRUserGuidance/<HighlightCoroutine>d__30::System.Collections.IEnumerator.get_Current()
extern void U3CHighlightCoroutineU3Ed__30_System_Collections_IEnumerator_get_Current_m7B462A3F8AD8BF9C023937252F26709B026C22CC (void);
// 0x00000350 SimpleJSON.JSONNodeType SimpleJSON.JSONNode::get_Tag()
// 0x00000351 SimpleJSON.JSONNode SimpleJSON.JSONNode::get_Item(System.Int32)
extern void JSONNode_get_Item_m77F15891BEC7ED659BFBC392555178B558747AD8 (void);
// 0x00000352 System.Void SimpleJSON.JSONNode::set_Item(System.Int32,SimpleJSON.JSONNode)
extern void JSONNode_set_Item_mC6F47073D979B943286B2EAB1A6D0380AFE58A09 (void);
// 0x00000353 SimpleJSON.JSONNode SimpleJSON.JSONNode::get_Item(System.String)
extern void JSONNode_get_Item_m466B08DF2E30B20606697EC7AE043C2791DC6768 (void);
// 0x00000354 System.Void SimpleJSON.JSONNode::set_Item(System.String,SimpleJSON.JSONNode)
extern void JSONNode_set_Item_m045530804B67FC5E2E57E497219F27ED70CE437E (void);
// 0x00000355 System.String SimpleJSON.JSONNode::get_Value()
extern void JSONNode_get_Value_m2A9961ACC3D4BCBB028012CD79B619DCBD82A839 (void);
// 0x00000356 System.Void SimpleJSON.JSONNode::set_Value(System.String)
extern void JSONNode_set_Value_mE8CD0E68E0E2B0A716F56B0FE9B988EC2BAD773A (void);
// 0x00000357 System.Int32 SimpleJSON.JSONNode::get_Count()
extern void JSONNode_get_Count_m260DDA50B8AFB98F5946E54B9EADD05891A82C8B (void);
// 0x00000358 System.Boolean SimpleJSON.JSONNode::get_IsNumber()
extern void JSONNode_get_IsNumber_m6B495FE576572E9FC7999740C63980BCB65AD768 (void);
// 0x00000359 System.Boolean SimpleJSON.JSONNode::get_IsString()
extern void JSONNode_get_IsString_mBDE2CAF25E51CDA450074BE9DC81D834903BA392 (void);
// 0x0000035A System.Boolean SimpleJSON.JSONNode::get_IsBoolean()
extern void JSONNode_get_IsBoolean_m13F16853C0F6D76D0AB6B7E866923A0632C108A2 (void);
// 0x0000035B System.Boolean SimpleJSON.JSONNode::get_IsNull()
extern void JSONNode_get_IsNull_m6443A7B3540D725ED3ACA0038A74CE0346A31F8D (void);
// 0x0000035C System.Boolean SimpleJSON.JSONNode::get_IsArray()
extern void JSONNode_get_IsArray_m52DCDB47E4CB2673FDCECCD3BE9DD2D90B5C948F (void);
// 0x0000035D System.Boolean SimpleJSON.JSONNode::get_IsObject()
extern void JSONNode_get_IsObject_m237FE2EA3382DD9762ED426B49F46183F5EF39AB (void);
// 0x0000035E System.Boolean SimpleJSON.JSONNode::get_Inline()
extern void JSONNode_get_Inline_m7A5B6C07F44EFEEDD80FD72580C32C0579041F4C (void);
// 0x0000035F System.Void SimpleJSON.JSONNode::set_Inline(System.Boolean)
extern void JSONNode_set_Inline_m18362F10F03DDCD1FF29B4868C3EA793D39AE7F6 (void);
// 0x00000360 System.Void SimpleJSON.JSONNode::Add(System.String,SimpleJSON.JSONNode)
extern void JSONNode_Add_mB05F1A32B54A9A1223F9AC6A6A737836FA1F4E7E (void);
// 0x00000361 System.Void SimpleJSON.JSONNode::Add(SimpleJSON.JSONNode)
extern void JSONNode_Add_mDAF96580EAF3B9FF23888A8549BED7A98439075D (void);
// 0x00000362 SimpleJSON.JSONNode SimpleJSON.JSONNode::Remove(System.String)
extern void JSONNode_Remove_mF56C4223700DF4F1D5AE12BCD69C492C2487FA59 (void);
// 0x00000363 SimpleJSON.JSONNode SimpleJSON.JSONNode::Remove(System.Int32)
extern void JSONNode_Remove_m7B5E0BC0A29C35857D7B10857A8C52C0E3DFB615 (void);
// 0x00000364 SimpleJSON.JSONNode SimpleJSON.JSONNode::Remove(SimpleJSON.JSONNode)
extern void JSONNode_Remove_mE2CFD05512C25BD11EA4160CAAF88B8154D9DBE5 (void);
// 0x00000365 System.Void SimpleJSON.JSONNode::Clear()
extern void JSONNode_Clear_mD9B59BDE8D07A352AB775FD4A8FB262D406EB848 (void);
// 0x00000366 SimpleJSON.JSONNode SimpleJSON.JSONNode::Clone()
extern void JSONNode_Clone_mE7849A4FBD98462D93E715826B0D01DE7FC822C3 (void);
// 0x00000367 System.Collections.Generic.IEnumerable`1<SimpleJSON.JSONNode> SimpleJSON.JSONNode::get_Children()
extern void JSONNode_get_Children_m3E2D70DBCA2C8311F65A47B766668728392B1F89 (void);
// 0x00000368 System.Collections.Generic.IEnumerable`1<SimpleJSON.JSONNode> SimpleJSON.JSONNode::get_DeepChildren()
extern void JSONNode_get_DeepChildren_m891CB892AEA834980686ED760B952A86DC1E8725 (void);
// 0x00000369 System.Boolean SimpleJSON.JSONNode::HasKey(System.String)
extern void JSONNode_HasKey_mBEF13E4AC99F2F0D76D4CD87405BB71586C4486B (void);
// 0x0000036A SimpleJSON.JSONNode SimpleJSON.JSONNode::GetValueOrDefault(System.String,SimpleJSON.JSONNode)
extern void JSONNode_GetValueOrDefault_m751E871953EBA8094B4DE73CC261C884720811F6 (void);
// 0x0000036B System.String SimpleJSON.JSONNode::ToString()
extern void JSONNode_ToString_m4CC464630B0AEEDD82AEB6B069690949AF569345 (void);
// 0x0000036C System.String SimpleJSON.JSONNode::ToString(System.Int32)
extern void JSONNode_ToString_m1F607CB90F49115510B7CF5228733578E9AD41F2 (void);
// 0x0000036D System.Void SimpleJSON.JSONNode::WriteToStringBuilder(System.Text.StringBuilder,System.Int32,System.Int32,SimpleJSON.JSONTextMode)
// 0x0000036E SimpleJSON.JSONNode/Enumerator SimpleJSON.JSONNode::GetEnumerator()
// 0x0000036F System.Collections.Generic.IEnumerable`1<System.Collections.Generic.KeyValuePair`2<System.String,SimpleJSON.JSONNode>> SimpleJSON.JSONNode::get_Linq()
extern void JSONNode_get_Linq_m8569DB478533504290D9A09ECA0DF12F116122DA (void);
// 0x00000370 SimpleJSON.JSONNode/KeyEnumerator SimpleJSON.JSONNode::get_Keys()
extern void JSONNode_get_Keys_mC3401CC91BBD9D1166EF8EFC0C87A820FC543D1B (void);
// 0x00000371 SimpleJSON.JSONNode/ValueEnumerator SimpleJSON.JSONNode::get_Values()
extern void JSONNode_get_Values_mF8FB164A48A169146D00EBA3F51D4C8E380C1930 (void);
// 0x00000372 System.Double SimpleJSON.JSONNode::get_AsDouble()
extern void JSONNode_get_AsDouble_m9A8E3EC46E4545BCBFA26B99C0F013067D2F0AE4 (void);
// 0x00000373 System.Void SimpleJSON.JSONNode::set_AsDouble(System.Double)
extern void JSONNode_set_AsDouble_mCDBB05BD0AE82EEF0C4842F5A9205B8F4C858015 (void);
// 0x00000374 System.Int32 SimpleJSON.JSONNode::get_AsInt()
extern void JSONNode_get_AsInt_mE4A3FCC1D91362D077C2ACF418ACAB43771B1FE6 (void);
// 0x00000375 System.Void SimpleJSON.JSONNode::set_AsInt(System.Int32)
extern void JSONNode_set_AsInt_m12FCF0B7E45E17EA0456AE44EFEF0C8731603F50 (void);
// 0x00000376 System.Single SimpleJSON.JSONNode::get_AsFloat()
extern void JSONNode_get_AsFloat_m0D044C1F3FC35086783A4BAF506EA96DC997D050 (void);
// 0x00000377 System.Void SimpleJSON.JSONNode::set_AsFloat(System.Single)
extern void JSONNode_set_AsFloat_m55FCE24DF60B37724DACCCF0A759522B2561DE92 (void);
// 0x00000378 System.Boolean SimpleJSON.JSONNode::get_AsBool()
extern void JSONNode_get_AsBool_m902380F5939671ACBBB7EFA01A48F1A082B1FD9C (void);
// 0x00000379 System.Void SimpleJSON.JSONNode::set_AsBool(System.Boolean)
extern void JSONNode_set_AsBool_m6097FD196A8C7BB156125363D1C1D3EF0EB67CD3 (void);
// 0x0000037A System.Int64 SimpleJSON.JSONNode::get_AsLong()
extern void JSONNode_get_AsLong_m31250905C6F4BED9B1059009E064D07D609C4C18 (void);
// 0x0000037B System.Void SimpleJSON.JSONNode::set_AsLong(System.Int64)
extern void JSONNode_set_AsLong_m8D29780DEA1458A2F9C33805432DB1554950ECF4 (void);
// 0x0000037C System.UInt64 SimpleJSON.JSONNode::get_AsULong()
extern void JSONNode_get_AsULong_mA34C3D1DA0D3339D0B63F386867ADE3E460909DD (void);
// 0x0000037D System.Void SimpleJSON.JSONNode::set_AsULong(System.UInt64)
extern void JSONNode_set_AsULong_m2BC120C5B1842E17BC0E6E5714511391DD504091 (void);
// 0x0000037E SimpleJSON.JSONArray SimpleJSON.JSONNode::get_AsArray()
extern void JSONNode_get_AsArray_m2D0890FDDA140528CAB44B1B6B3E34B26383ACC7 (void);
// 0x0000037F SimpleJSON.JSONObject SimpleJSON.JSONNode::get_AsObject()
extern void JSONNode_get_AsObject_m72F6D406BECA2FB0A24B20E0A353FDB8E409CA1B (void);
// 0x00000380 SimpleJSON.JSONNode SimpleJSON.JSONNode::op_Implicit(System.String)
extern void JSONNode_op_Implicit_m71A2D2EECDD79DC3A3DAF6510BB2F8ED57DE6AAC (void);
// 0x00000381 System.String SimpleJSON.JSONNode::op_Implicit(SimpleJSON.JSONNode)
extern void JSONNode_op_Implicit_m6019D30B60A2033906907488CB6236EC9A7B7B6B (void);
// 0x00000382 SimpleJSON.JSONNode SimpleJSON.JSONNode::op_Implicit(System.Double)
extern void JSONNode_op_Implicit_m098A31323C3D89615E0EBD709D83AB4F684453CF (void);
// 0x00000383 System.Double SimpleJSON.JSONNode::op_Implicit(SimpleJSON.JSONNode)
extern void JSONNode_op_Implicit_m17B71B28DE136B73EF2B97DA87BB4A4BB27332E9 (void);
// 0x00000384 SimpleJSON.JSONNode SimpleJSON.JSONNode::op_Implicit(System.Single)
extern void JSONNode_op_Implicit_m4A9267CC71FC4FDD39ABAE262B7B2D334EB0FFBD (void);
// 0x00000385 System.Single SimpleJSON.JSONNode::op_Implicit(SimpleJSON.JSONNode)
extern void JSONNode_op_Implicit_m808991B4DFA11ECBF7080226CFC3069A7E6673E8 (void);
// 0x00000386 SimpleJSON.JSONNode SimpleJSON.JSONNode::op_Implicit(System.Int32)
extern void JSONNode_op_Implicit_m6AC3F8DDC02644CA8D85EC90758373D1B7EC4322 (void);
// 0x00000387 System.Int32 SimpleJSON.JSONNode::op_Implicit(SimpleJSON.JSONNode)
extern void JSONNode_op_Implicit_mD9A5824FE62046D01AD966EC503DEB775B2C7482 (void);
// 0x00000388 SimpleJSON.JSONNode SimpleJSON.JSONNode::op_Implicit(System.Int64)
extern void JSONNode_op_Implicit_m94EF6FC36942EA4A49ABFCA42FC12BCE914990FA (void);
// 0x00000389 System.Int64 SimpleJSON.JSONNode::op_Implicit(SimpleJSON.JSONNode)
extern void JSONNode_op_Implicit_mD25BDDE21767954AE9D36F16948B6F77173EC2F6 (void);
// 0x0000038A SimpleJSON.JSONNode SimpleJSON.JSONNode::op_Implicit(System.UInt64)
extern void JSONNode_op_Implicit_m64294932E998EC6A35EF99F1CD4D36BFB9A8FB1E (void);
// 0x0000038B System.UInt64 SimpleJSON.JSONNode::op_Implicit(SimpleJSON.JSONNode)
extern void JSONNode_op_Implicit_mD5974501A6FBBD60D6E7331940440498D67A7A05 (void);
// 0x0000038C SimpleJSON.JSONNode SimpleJSON.JSONNode::op_Implicit(System.Boolean)
extern void JSONNode_op_Implicit_mCE1B7A6233218E114687A876F778B4A1CBF22B74 (void);
// 0x0000038D System.Boolean SimpleJSON.JSONNode::op_Implicit(SimpleJSON.JSONNode)
extern void JSONNode_op_Implicit_m29CA9621387E0DDDECCCAAB240A140A854567FDF (void);
// 0x0000038E SimpleJSON.JSONNode SimpleJSON.JSONNode::op_Implicit(System.Collections.Generic.KeyValuePair`2<System.String,SimpleJSON.JSONNode>)
extern void JSONNode_op_Implicit_mB9C34D74CF1854E402B4AD106AB2084169287E1E (void);
// 0x0000038F System.Boolean SimpleJSON.JSONNode::op_Equality(SimpleJSON.JSONNode,System.Object)
extern void JSONNode_op_Equality_mD30EBFA5F9398107FCC5CE51B05CE4CFFBCC6A8E (void);
// 0x00000390 System.Boolean SimpleJSON.JSONNode::op_Inequality(SimpleJSON.JSONNode,System.Object)
extern void JSONNode_op_Inequality_m91693B2A4AC881F8703CC1D1050371B8EC552CF7 (void);
// 0x00000391 System.Boolean SimpleJSON.JSONNode::Equals(System.Object)
extern void JSONNode_Equals_mE1B8A846783529B1E54786975A6A2396089A88DE (void);
// 0x00000392 System.Int32 SimpleJSON.JSONNode::GetHashCode()
extern void JSONNode_GetHashCode_m0A263555D1F0E6766A61692A7E1BC3546B2BC984 (void);
// 0x00000393 System.Text.StringBuilder SimpleJSON.JSONNode::get_EscapeBuilder()
extern void JSONNode_get_EscapeBuilder_mA695C85FBFBCF3863E2AC3B63821B469CC632DB1 (void);
// 0x00000394 System.String SimpleJSON.JSONNode::Escape(System.String)
extern void JSONNode_Escape_m5C811748A36C7258315C1D2036712855F184ADDD (void);
// 0x00000395 SimpleJSON.JSONNode SimpleJSON.JSONNode::ParseElement(System.String,System.Boolean)
extern void JSONNode_ParseElement_m3478B79AC164A87E0B2088067EDEC6DE146DAA56 (void);
// 0x00000396 SimpleJSON.JSONNode SimpleJSON.JSONNode::Parse(System.String)
extern void JSONNode_Parse_m7198C73C509B06CD8A96576D7D2A5A125DC7D0B4 (void);
// 0x00000397 System.Void SimpleJSON.JSONNode::.ctor()
extern void JSONNode__ctor_mF8F2893483161D3B7B9877B63C69063D26A5C353 (void);
// 0x00000398 System.Void SimpleJSON.JSONNode::.cctor()
extern void JSONNode__cctor_m00855C5266A7EF25B6EBE62476F1FAD5C7046065 (void);
// 0x00000399 System.Boolean SimpleJSON.JSONNode/Enumerator::get_IsValid()
extern void Enumerator_get_IsValid_mBC273331DC1699FF46BD3621AE5059A54AD98BA6 (void);
// 0x0000039A System.Void SimpleJSON.JSONNode/Enumerator::.ctor(System.Collections.Generic.List`1/Enumerator<SimpleJSON.JSONNode>)
extern void Enumerator__ctor_mF21239C69620D815F8CD34F022BE18E9DAF9CB10 (void);
// 0x0000039B System.Void SimpleJSON.JSONNode/Enumerator::.ctor(System.Collections.Generic.Dictionary`2/Enumerator<System.String,SimpleJSON.JSONNode>)
extern void Enumerator__ctor_mAC4ED0FA4B083E2652E865A41EA5C74A49478EFE (void);
// 0x0000039C System.Collections.Generic.KeyValuePair`2<System.String,SimpleJSON.JSONNode> SimpleJSON.JSONNode/Enumerator::get_Current()
extern void Enumerator_get_Current_mDE6750203413E1069D0520793D6AA0B2527CB20E (void);
// 0x0000039D System.Boolean SimpleJSON.JSONNode/Enumerator::MoveNext()
extern void Enumerator_MoveNext_m238CF072385A1106BEDEFCE33BA2B0DBE999758A (void);
// 0x0000039E System.Void SimpleJSON.JSONNode/ValueEnumerator::.ctor(System.Collections.Generic.List`1/Enumerator<SimpleJSON.JSONNode>)
extern void ValueEnumerator__ctor_mCC61CE3EDCF1AC94A84E031F2E89F8054C94A015 (void);
// 0x0000039F System.Void SimpleJSON.JSONNode/ValueEnumerator::.ctor(System.Collections.Generic.Dictionary`2/Enumerator<System.String,SimpleJSON.JSONNode>)
extern void ValueEnumerator__ctor_m122732DF448B45E8E82956E07AC8314C60E28C29 (void);
// 0x000003A0 System.Void SimpleJSON.JSONNode/ValueEnumerator::.ctor(SimpleJSON.JSONNode/Enumerator)
extern void ValueEnumerator__ctor_m7BA4BAD5FEBAC4054F71575B728DC27EC4080F0A (void);
// 0x000003A1 SimpleJSON.JSONNode SimpleJSON.JSONNode/ValueEnumerator::get_Current()
extern void ValueEnumerator_get_Current_mAA24A52FDEB7160BD268193175388EACB41B7CE2 (void);
// 0x000003A2 System.Boolean SimpleJSON.JSONNode/ValueEnumerator::MoveNext()
extern void ValueEnumerator_MoveNext_m5B596A2EF2FF395EDA8F5CAB97C0789498D250C9 (void);
// 0x000003A3 SimpleJSON.JSONNode/ValueEnumerator SimpleJSON.JSONNode/ValueEnumerator::GetEnumerator()
extern void ValueEnumerator_GetEnumerator_m765261287A2C0AEF757B94994826F43951387E4C (void);
// 0x000003A4 System.Void SimpleJSON.JSONNode/KeyEnumerator::.ctor(System.Collections.Generic.List`1/Enumerator<SimpleJSON.JSONNode>)
extern void KeyEnumerator__ctor_m6EA81E2BED4CA5194A7306D8B324F7356E37F80A (void);
// 0x000003A5 System.Void SimpleJSON.JSONNode/KeyEnumerator::.ctor(System.Collections.Generic.Dictionary`2/Enumerator<System.String,SimpleJSON.JSONNode>)
extern void KeyEnumerator__ctor_mA6338E82A9F8AA19A1744352B4FE54103AD70405 (void);
// 0x000003A6 System.Void SimpleJSON.JSONNode/KeyEnumerator::.ctor(SimpleJSON.JSONNode/Enumerator)
extern void KeyEnumerator__ctor_m526EA1364C367B83C931F4208CDD816BD02810EA (void);
// 0x000003A7 System.String SimpleJSON.JSONNode/KeyEnumerator::get_Current()
extern void KeyEnumerator_get_Current_mB4E0F33D7E23A7F365D12B3530DE7FB6B7A1F7E3 (void);
// 0x000003A8 System.Boolean SimpleJSON.JSONNode/KeyEnumerator::MoveNext()
extern void KeyEnumerator_MoveNext_m42FE2CEE808A7E065895BA333B7FBD2F3AEE032F (void);
// 0x000003A9 SimpleJSON.JSONNode/KeyEnumerator SimpleJSON.JSONNode/KeyEnumerator::GetEnumerator()
extern void KeyEnumerator_GetEnumerator_mD4687B4D6D10E4D6870CBBECC680689A62A95C0B (void);
// 0x000003AA System.Void SimpleJSON.JSONNode/LinqEnumerator::.ctor(SimpleJSON.JSONNode)
extern void LinqEnumerator__ctor_m9FD8AB1580F3D94C5C36D070DBE85E023ED36E30 (void);
// 0x000003AB System.Collections.Generic.KeyValuePair`2<System.String,SimpleJSON.JSONNode> SimpleJSON.JSONNode/LinqEnumerator::get_Current()
extern void LinqEnumerator_get_Current_m28F0BE4D9B5736F5BD79197C1895EAC1592EBAAF (void);
// 0x000003AC System.Object SimpleJSON.JSONNode/LinqEnumerator::System.Collections.IEnumerator.get_Current()
extern void LinqEnumerator_System_Collections_IEnumerator_get_Current_m6B6C12C7E8CD21DF513FCDCB4E88E454790B6FF0 (void);
// 0x000003AD System.Boolean SimpleJSON.JSONNode/LinqEnumerator::MoveNext()
extern void LinqEnumerator_MoveNext_mCA8604B6E8D857CF16003E674048C05E29447819 (void);
// 0x000003AE System.Void SimpleJSON.JSONNode/LinqEnumerator::Dispose()
extern void LinqEnumerator_Dispose_m5D6A54C4B712D138739726323D5BEA50A4E12E32 (void);
// 0x000003AF System.Collections.Generic.IEnumerator`1<System.Collections.Generic.KeyValuePair`2<System.String,SimpleJSON.JSONNode>> SimpleJSON.JSONNode/LinqEnumerator::GetEnumerator()
extern void LinqEnumerator_GetEnumerator_m4A9F0720F0C0964F91032AB8B8776F09DC70A90B (void);
// 0x000003B0 System.Void SimpleJSON.JSONNode/LinqEnumerator::Reset()
extern void LinqEnumerator_Reset_m56B65E398518EF57070307FDC48069DFE37BC57B (void);
// 0x000003B1 System.Collections.IEnumerator SimpleJSON.JSONNode/LinqEnumerator::System.Collections.IEnumerable.GetEnumerator()
extern void LinqEnumerator_System_Collections_IEnumerable_GetEnumerator_mB63F02D713868ABF87DAB18ABFD5D832F4D805A4 (void);
// 0x000003B2 System.Void SimpleJSON.JSONNode/<get_Children>d__43::.ctor(System.Int32)
extern void U3Cget_ChildrenU3Ed__43__ctor_mA2E1AC1211A03DAFF45B69AF872ED71E58F4D458 (void);
// 0x000003B3 System.Void SimpleJSON.JSONNode/<get_Children>d__43::System.IDisposable.Dispose()
extern void U3Cget_ChildrenU3Ed__43_System_IDisposable_Dispose_m0C7490DE49A53AB049729E66293845681AB08395 (void);
// 0x000003B4 System.Boolean SimpleJSON.JSONNode/<get_Children>d__43::MoveNext()
extern void U3Cget_ChildrenU3Ed__43_MoveNext_m33A56DB8F47EADE4EB91E3FBFF4D01F1CF255839 (void);
// 0x000003B5 SimpleJSON.JSONNode SimpleJSON.JSONNode/<get_Children>d__43::System.Collections.Generic.IEnumerator<SimpleJSON.JSONNode>.get_Current()
extern void U3Cget_ChildrenU3Ed__43_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m85EB3E729C5EE85E2103FED7453D79C1D132C2EB (void);
// 0x000003B6 System.Void SimpleJSON.JSONNode/<get_Children>d__43::System.Collections.IEnumerator.Reset()
extern void U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerator_Reset_m755BAC68C65681AA8266C6AC37D2308771D54067 (void);
// 0x000003B7 System.Object SimpleJSON.JSONNode/<get_Children>d__43::System.Collections.IEnumerator.get_Current()
extern void U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerator_get_Current_m04BDDA2EB2EC20489BB50BDDB46313F624F90CF9 (void);
// 0x000003B8 System.Collections.Generic.IEnumerator`1<SimpleJSON.JSONNode> SimpleJSON.JSONNode/<get_Children>d__43::System.Collections.Generic.IEnumerable<SimpleJSON.JSONNode>.GetEnumerator()
extern void U3Cget_ChildrenU3Ed__43_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_m96326AFEFC6998DB0E90D15633CFE23661C21916 (void);
// 0x000003B9 System.Collections.IEnumerator SimpleJSON.JSONNode/<get_Children>d__43::System.Collections.IEnumerable.GetEnumerator()
extern void U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerable_GetEnumerator_m39BF4FF795523B96CA4FA6383244D82117D96C46 (void);
// 0x000003BA System.Void SimpleJSON.JSONNode/<get_DeepChildren>d__45::.ctor(System.Int32)
extern void U3Cget_DeepChildrenU3Ed__45__ctor_m89830CB6F115E0AD956EF880354CAFBAD7AF9E5A (void);
// 0x000003BB System.Void SimpleJSON.JSONNode/<get_DeepChildren>d__45::System.IDisposable.Dispose()
extern void U3Cget_DeepChildrenU3Ed__45_System_IDisposable_Dispose_mCE52C471742B7A6DA19AF43E9096545012D560DD (void);
// 0x000003BC System.Boolean SimpleJSON.JSONNode/<get_DeepChildren>d__45::MoveNext()
extern void U3Cget_DeepChildrenU3Ed__45_MoveNext_m644F556E82CCF23C7B91E0B0266F4716E18C2F5E (void);
// 0x000003BD System.Void SimpleJSON.JSONNode/<get_DeepChildren>d__45::<>m__Finally1()
extern void U3Cget_DeepChildrenU3Ed__45_U3CU3Em__Finally1_mBA31C43EB8ACB72C8A163B470D786ACB361CF740 (void);
// 0x000003BE System.Void SimpleJSON.JSONNode/<get_DeepChildren>d__45::<>m__Finally2()
extern void U3Cget_DeepChildrenU3Ed__45_U3CU3Em__Finally2_mC829190BED7A6B48F2F4C64848495925A3C58EEE (void);
// 0x000003BF SimpleJSON.JSONNode SimpleJSON.JSONNode/<get_DeepChildren>d__45::System.Collections.Generic.IEnumerator<SimpleJSON.JSONNode>.get_Current()
extern void U3Cget_DeepChildrenU3Ed__45_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m6E1A05C1C6A7BF9748F1768E2B2AB1B140F49983 (void);
// 0x000003C0 System.Void SimpleJSON.JSONNode/<get_DeepChildren>d__45::System.Collections.IEnumerator.Reset()
extern void U3Cget_DeepChildrenU3Ed__45_System_Collections_IEnumerator_Reset_mB10807E87C7440A590E9580E6A5B329ACCAD49E4 (void);
// 0x000003C1 System.Object SimpleJSON.JSONNode/<get_DeepChildren>d__45::System.Collections.IEnumerator.get_Current()
extern void U3Cget_DeepChildrenU3Ed__45_System_Collections_IEnumerator_get_Current_m2A8CD7D70A8ACF8A362378B75EAF7B41BC9FCEF6 (void);
// 0x000003C2 System.Collections.Generic.IEnumerator`1<SimpleJSON.JSONNode> SimpleJSON.JSONNode/<get_DeepChildren>d__45::System.Collections.Generic.IEnumerable<SimpleJSON.JSONNode>.GetEnumerator()
extern void U3Cget_DeepChildrenU3Ed__45_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mAD2929E624663DCA925B762F05FCF8CDDE1FC6C8 (void);
// 0x000003C3 System.Collections.IEnumerator SimpleJSON.JSONNode/<get_DeepChildren>d__45::System.Collections.IEnumerable.GetEnumerator()
extern void U3Cget_DeepChildrenU3Ed__45_System_Collections_IEnumerable_GetEnumerator_m8BB12003DCC4402BDA35F5B5AE1B82EF7C1A4856 (void);
// 0x000003C4 System.Boolean SimpleJSON.JSONArray::get_Inline()
extern void JSONArray_get_Inline_mBA0C9AEBB7420DBDFD977C0F54CC237E8F2BE3E5 (void);
// 0x000003C5 System.Void SimpleJSON.JSONArray::set_Inline(System.Boolean)
extern void JSONArray_set_Inline_m731089F5D0FA649ED210518DC299635A8D86A1DC (void);
// 0x000003C6 SimpleJSON.JSONNodeType SimpleJSON.JSONArray::get_Tag()
extern void JSONArray_get_Tag_m360EB078D7897D6D52783B8CDA6B736D014E97BC (void);
// 0x000003C7 System.Boolean SimpleJSON.JSONArray::get_IsArray()
extern void JSONArray_get_IsArray_mA7B4EF5B0128FB64ACEB7EAC66FA3522991980AF (void);
// 0x000003C8 SimpleJSON.JSONNode/Enumerator SimpleJSON.JSONArray::GetEnumerator()
extern void JSONArray_GetEnumerator_m6AF64AE0DD2A5AAB8C0E271BF0CAB8AA1FD32E17 (void);
// 0x000003C9 SimpleJSON.JSONNode SimpleJSON.JSONArray::get_Item(System.Int32)
extern void JSONArray_get_Item_m8BE9047FC512840E6A4594560EDF86BB4E0FF657 (void);
// 0x000003CA System.Void SimpleJSON.JSONArray::set_Item(System.Int32,SimpleJSON.JSONNode)
extern void JSONArray_set_Item_mBCD05590C34BC589B786E753B9FE796EBA3F6725 (void);
// 0x000003CB SimpleJSON.JSONNode SimpleJSON.JSONArray::get_Item(System.String)
extern void JSONArray_get_Item_mE18312128B02B505BA656D7F444B05A6769710AE (void);
// 0x000003CC System.Void SimpleJSON.JSONArray::set_Item(System.String,SimpleJSON.JSONNode)
extern void JSONArray_set_Item_mE4E0DE5133E60AF49E46FEDAD00D2A04349C0855 (void);
// 0x000003CD System.Int32 SimpleJSON.JSONArray::get_Count()
extern void JSONArray_get_Count_mB71218A2D8288D0665C467844F7351D301FDAFDD (void);
// 0x000003CE System.Void SimpleJSON.JSONArray::Add(System.String,SimpleJSON.JSONNode)
extern void JSONArray_Add_mD1FBE0F0FC20E7415014B7FF21939592EBB0C9A1 (void);
// 0x000003CF SimpleJSON.JSONNode SimpleJSON.JSONArray::Remove(System.Int32)
extern void JSONArray_Remove_m79500DBD9751A04C02756470A4D22DDCF9C97FEC (void);
// 0x000003D0 SimpleJSON.JSONNode SimpleJSON.JSONArray::Remove(SimpleJSON.JSONNode)
extern void JSONArray_Remove_m64C3EBFE3DB5BE130232769DC43000E84589E674 (void);
// 0x000003D1 System.Void SimpleJSON.JSONArray::Clear()
extern void JSONArray_Clear_m86E2E8BE6493C5C555525B9935AFF9E53BB72C2B (void);
// 0x000003D2 SimpleJSON.JSONNode SimpleJSON.JSONArray::Clone()
extern void JSONArray_Clone_mA05BA59E71672A88208218DF12C4E5F7A8773502 (void);
// 0x000003D3 System.Collections.Generic.IEnumerable`1<SimpleJSON.JSONNode> SimpleJSON.JSONArray::get_Children()
extern void JSONArray_get_Children_m733AE4C5816E51E6F86441110606489A0406AA91 (void);
// 0x000003D4 System.Void SimpleJSON.JSONArray::WriteToStringBuilder(System.Text.StringBuilder,System.Int32,System.Int32,SimpleJSON.JSONTextMode)
extern void JSONArray_WriteToStringBuilder_m9F23115433028794DCAC019F82EEFD946990D994 (void);
// 0x000003D5 System.Void SimpleJSON.JSONArray::.ctor()
extern void JSONArray__ctor_m92FFF2DC8E1425398814F50D4B253EB459B8477F (void);
// 0x000003D6 System.Void SimpleJSON.JSONArray/<get_Children>d__24::.ctor(System.Int32)
extern void U3Cget_ChildrenU3Ed__24__ctor_m4FA6CFA96B1189496D9E219499A0C05F713A6D28 (void);
// 0x000003D7 System.Void SimpleJSON.JSONArray/<get_Children>d__24::System.IDisposable.Dispose()
extern void U3Cget_ChildrenU3Ed__24_System_IDisposable_Dispose_m91E6F93E3940835795BCA9BFD783592E29BDEE5A (void);
// 0x000003D8 System.Boolean SimpleJSON.JSONArray/<get_Children>d__24::MoveNext()
extern void U3Cget_ChildrenU3Ed__24_MoveNext_m9C8F57C9E0722A9D843A2BA0259E7EE30778CF6B (void);
// 0x000003D9 System.Void SimpleJSON.JSONArray/<get_Children>d__24::<>m__Finally1()
extern void U3Cget_ChildrenU3Ed__24_U3CU3Em__Finally1_m8E8730694C83B14CFFB30D810166D12563C1DFF2 (void);
// 0x000003DA SimpleJSON.JSONNode SimpleJSON.JSONArray/<get_Children>d__24::System.Collections.Generic.IEnumerator<SimpleJSON.JSONNode>.get_Current()
extern void U3Cget_ChildrenU3Ed__24_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m6958E538A455210191F2E06BA531D4AE5F0E97F0 (void);
// 0x000003DB System.Void SimpleJSON.JSONArray/<get_Children>d__24::System.Collections.IEnumerator.Reset()
extern void U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerator_Reset_mE122AA2BA93A72C8C8733C4F7EC6A7B8CFB42FCD (void);
// 0x000003DC System.Object SimpleJSON.JSONArray/<get_Children>d__24::System.Collections.IEnumerator.get_Current()
extern void U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerator_get_Current_m508CF18DF3857321EA1CFDC62E0406DBEF6FDF7F (void);
// 0x000003DD System.Collections.Generic.IEnumerator`1<SimpleJSON.JSONNode> SimpleJSON.JSONArray/<get_Children>d__24::System.Collections.Generic.IEnumerable<SimpleJSON.JSONNode>.GetEnumerator()
extern void U3Cget_ChildrenU3Ed__24_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_m7679E5F774E9512FC2DA58B2D0236A66983BC632 (void);
// 0x000003DE System.Collections.IEnumerator SimpleJSON.JSONArray/<get_Children>d__24::System.Collections.IEnumerable.GetEnumerator()
extern void U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerable_GetEnumerator_m7593480F6CC6218E2EA7CD84ED3A56FF6274AB32 (void);
// 0x000003DF System.Boolean SimpleJSON.JSONObject::get_Inline()
extern void JSONObject_get_Inline_mCDF2154366BEFF9E547918F999E7F3C7C4865F84 (void);
// 0x000003E0 System.Void SimpleJSON.JSONObject::set_Inline(System.Boolean)
extern void JSONObject_set_Inline_m7F048A7565E5A53FDB610D44B7CA75A314CB7A7A (void);
// 0x000003E1 SimpleJSON.JSONNodeType SimpleJSON.JSONObject::get_Tag()
extern void JSONObject_get_Tag_mD57D6BCAD1C677B88693FD508129CFAD661F4FBD (void);
// 0x000003E2 System.Boolean SimpleJSON.JSONObject::get_IsObject()
extern void JSONObject_get_IsObject_m9F72861BE5A0DB2888AA3CBEC82718E08DD71E93 (void);
// 0x000003E3 SimpleJSON.JSONNode/Enumerator SimpleJSON.JSONObject::GetEnumerator()
extern void JSONObject_GetEnumerator_m8912E3D1EA302655BB5701B53EB19437238BABDA (void);
// 0x000003E4 SimpleJSON.JSONNode SimpleJSON.JSONObject::get_Item(System.String)
extern void JSONObject_get_Item_m219B9BA37D800A5DFEAA14E4EECA375B3565BF96 (void);
// 0x000003E5 System.Void SimpleJSON.JSONObject::set_Item(System.String,SimpleJSON.JSONNode)
extern void JSONObject_set_Item_m1AC7334DBA67D0CB6C9549B83B3FFA75CF226AEF (void);
// 0x000003E6 SimpleJSON.JSONNode SimpleJSON.JSONObject::get_Item(System.Int32)
extern void JSONObject_get_Item_m5C2EDBE7B154A3FC1CC43616C4C40255B4D95652 (void);
// 0x000003E7 System.Void SimpleJSON.JSONObject::set_Item(System.Int32,SimpleJSON.JSONNode)
extern void JSONObject_set_Item_mFB6E61E3FA394B7D2CA01CC957A6A253642D109B (void);
// 0x000003E8 System.Int32 SimpleJSON.JSONObject::get_Count()
extern void JSONObject_get_Count_m9109E9A81559A9006EE160CA6A0F3291C71F2D08 (void);
// 0x000003E9 System.Void SimpleJSON.JSONObject::Add(System.String,SimpleJSON.JSONNode)
extern void JSONObject_Add_m25BD208A0AC0F0223FD93FBCB42785B12A6E1A18 (void);
// 0x000003EA SimpleJSON.JSONNode SimpleJSON.JSONObject::Remove(System.String)
extern void JSONObject_Remove_m34280FDB4512E61F42781475E492BE98514830C9 (void);
// 0x000003EB SimpleJSON.JSONNode SimpleJSON.JSONObject::Remove(System.Int32)
extern void JSONObject_Remove_mD1B01E22A9C1FEE83A00ECDFD8E0D8A422F8E4C2 (void);
// 0x000003EC SimpleJSON.JSONNode SimpleJSON.JSONObject::Remove(SimpleJSON.JSONNode)
extern void JSONObject_Remove_m51B998A7997D184A1A20359D512C6B5A1B825404 (void);
// 0x000003ED System.Void SimpleJSON.JSONObject::Clear()
extern void JSONObject_Clear_m74686B9AF4B75949F959B81AAF8DE5076C60B3FE (void);
// 0x000003EE SimpleJSON.JSONNode SimpleJSON.JSONObject::Clone()
extern void JSONObject_Clone_mF3146F5687820508FD22051B23EFA20430B811C1 (void);
// 0x000003EF System.Boolean SimpleJSON.JSONObject::HasKey(System.String)
extern void JSONObject_HasKey_m79E034D14422C265C62C6C50C8E6F8337749457E (void);
// 0x000003F0 SimpleJSON.JSONNode SimpleJSON.JSONObject::GetValueOrDefault(System.String,SimpleJSON.JSONNode)
extern void JSONObject_GetValueOrDefault_m969ABBC8049DB2DF4EC53968CDF7DF45666873BC (void);
// 0x000003F1 System.Collections.Generic.IEnumerable`1<SimpleJSON.JSONNode> SimpleJSON.JSONObject::get_Children()
extern void JSONObject_get_Children_m03D7227DE57F0BE2977FC0436C0DE48858650B7C (void);
// 0x000003F2 System.Void SimpleJSON.JSONObject::WriteToStringBuilder(System.Text.StringBuilder,System.Int32,System.Int32,SimpleJSON.JSONTextMode)
extern void JSONObject_WriteToStringBuilder_m931DC8805C6B8F09617958EFDAEA957751EB2EAE (void);
// 0x000003F3 System.Void SimpleJSON.JSONObject::.ctor()
extern void JSONObject__ctor_m8007967452F5257DC9F5DF2B78B411BFD4B6D6AB (void);
// 0x000003F4 System.Void SimpleJSON.JSONObject/<>c__DisplayClass21_0::.ctor()
extern void U3CU3Ec__DisplayClass21_0__ctor_m6976B4CF7F93E28364B390F81E55DAD60BB141C1 (void);
// 0x000003F5 System.Boolean SimpleJSON.JSONObject/<>c__DisplayClass21_0::<Remove>b__0(System.Collections.Generic.KeyValuePair`2<System.String,SimpleJSON.JSONNode>)
extern void U3CU3Ec__DisplayClass21_0_U3CRemoveU3Eb__0_m8B35D441B276B749481FF797FC51A256A7A56105 (void);
// 0x000003F6 System.Void SimpleJSON.JSONObject/<get_Children>d__27::.ctor(System.Int32)
extern void U3Cget_ChildrenU3Ed__27__ctor_mC18696B4562A62E4AA0969D6399C8C0631E35DC8 (void);
// 0x000003F7 System.Void SimpleJSON.JSONObject/<get_Children>d__27::System.IDisposable.Dispose()
extern void U3Cget_ChildrenU3Ed__27_System_IDisposable_Dispose_mC5CC72D1E22DD570C8E2EB525332F70406CDB9AA (void);
// 0x000003F8 System.Boolean SimpleJSON.JSONObject/<get_Children>d__27::MoveNext()
extern void U3Cget_ChildrenU3Ed__27_MoveNext_mF000F683CB97030C47BF22BD34472814A0C7630C (void);
// 0x000003F9 System.Void SimpleJSON.JSONObject/<get_Children>d__27::<>m__Finally1()
extern void U3Cget_ChildrenU3Ed__27_U3CU3Em__Finally1_mF5ECB5874D716A4939B7F1DB00D93DC58CEA824D (void);
// 0x000003FA SimpleJSON.JSONNode SimpleJSON.JSONObject/<get_Children>d__27::System.Collections.Generic.IEnumerator<SimpleJSON.JSONNode>.get_Current()
extern void U3Cget_ChildrenU3Ed__27_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_mD5BCAEE8B6A2ADEAF8EC61432A9619287942CD66 (void);
// 0x000003FB System.Void SimpleJSON.JSONObject/<get_Children>d__27::System.Collections.IEnumerator.Reset()
extern void U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerator_Reset_m7F54C4A2495814DE04F74FB9E9296EA2B68BFF6D (void);
// 0x000003FC System.Object SimpleJSON.JSONObject/<get_Children>d__27::System.Collections.IEnumerator.get_Current()
extern void U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerator_get_Current_mF24C3141BA1436A87068A46004816112F281FF9E (void);
// 0x000003FD System.Collections.Generic.IEnumerator`1<SimpleJSON.JSONNode> SimpleJSON.JSONObject/<get_Children>d__27::System.Collections.Generic.IEnumerable<SimpleJSON.JSONNode>.GetEnumerator()
extern void U3Cget_ChildrenU3Ed__27_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mB7F1824F0A6AD34C4EFEB913F04662B64CEF262C (void);
// 0x000003FE System.Collections.IEnumerator SimpleJSON.JSONObject/<get_Children>d__27::System.Collections.IEnumerable.GetEnumerator()
extern void U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerable_GetEnumerator_m02800F9D77652D9E15E570729565FE79BCC2B3F8 (void);
// 0x000003FF SimpleJSON.JSONNodeType SimpleJSON.JSONString::get_Tag()
extern void JSONString_get_Tag_m68B0FF9ADDC3E203E5D60BB10639AEABACA34D44 (void);
// 0x00000400 System.Boolean SimpleJSON.JSONString::get_IsString()
extern void JSONString_get_IsString_m933985E37AE8A887A2039A9BAC7698F083BCD6E3 (void);
// 0x00000401 SimpleJSON.JSONNode/Enumerator SimpleJSON.JSONString::GetEnumerator()
extern void JSONString_GetEnumerator_m1CB9E437FC8622F3FE05D0AC12024D144747E0B8 (void);
// 0x00000402 System.String SimpleJSON.JSONString::get_Value()
extern void JSONString_get_Value_mEAD2BD372A2C517E83233BA5F6E309745AA5E9B4 (void);
// 0x00000403 System.Void SimpleJSON.JSONString::set_Value(System.String)
extern void JSONString_set_Value_mB974D9B82AB8F9FAB84DCA99B8BD4B7C1C08ED00 (void);
// 0x00000404 System.Void SimpleJSON.JSONString::.ctor(System.String)
extern void JSONString__ctor_m1DD5FB9A4147F72A0ED5F773FF82FA269241AD19 (void);
// 0x00000405 SimpleJSON.JSONNode SimpleJSON.JSONString::Clone()
extern void JSONString_Clone_m59FCBC159496A334397171CF5127205C82C30A73 (void);
// 0x00000406 System.Void SimpleJSON.JSONString::WriteToStringBuilder(System.Text.StringBuilder,System.Int32,System.Int32,SimpleJSON.JSONTextMode)
extern void JSONString_WriteToStringBuilder_mDF24D860FBF8E71F6F04799DD70F7700CE41D818 (void);
// 0x00000407 System.Boolean SimpleJSON.JSONString::Equals(System.Object)
extern void JSONString_Equals_m1C60B537E558E6DF85ACF3EF9FF43BF9A3CF5435 (void);
// 0x00000408 System.Int32 SimpleJSON.JSONString::GetHashCode()
extern void JSONString_GetHashCode_m979A74F84B4C0F45BF63D75DE1146490F743EE00 (void);
// 0x00000409 System.Void SimpleJSON.JSONString::Clear()
extern void JSONString_Clear_m3E9CBF4AB37C6FD0011E19CA99E074FEA129FED7 (void);
// 0x0000040A SimpleJSON.JSONNodeType SimpleJSON.JSONNumber::get_Tag()
extern void JSONNumber_get_Tag_m7C6E217E85B6161812496B63E5D371B910AAC856 (void);
// 0x0000040B System.Boolean SimpleJSON.JSONNumber::get_IsNumber()
extern void JSONNumber_get_IsNumber_mFABFD0C9C4905CFB34A62700A1BD335F53E4214E (void);
// 0x0000040C SimpleJSON.JSONNode/Enumerator SimpleJSON.JSONNumber::GetEnumerator()
extern void JSONNumber_GetEnumerator_m4D13E84756AEED9FCD7EFEEE4D01187DD049C596 (void);
// 0x0000040D System.String SimpleJSON.JSONNumber::get_Value()
extern void JSONNumber_get_Value_mBC5AB046D134B1E54C228C9C1C2231F8448CD56D (void);
// 0x0000040E System.Void SimpleJSON.JSONNumber::set_Value(System.String)
extern void JSONNumber_set_Value_m2264762BBD76F39DDC5DF3160910A44FBEFDE54C (void);
// 0x0000040F System.Double SimpleJSON.JSONNumber::get_AsDouble()
extern void JSONNumber_get_AsDouble_m8C004121700A7E7EB2B77ED223187227E33DE60B (void);
// 0x00000410 System.Void SimpleJSON.JSONNumber::set_AsDouble(System.Double)
extern void JSONNumber_set_AsDouble_m8E17AF8C0E9AE0EF6E25D86CB1B119904ADC0558 (void);
// 0x00000411 System.Int64 SimpleJSON.JSONNumber::get_AsLong()
extern void JSONNumber_get_AsLong_mF96069F806F51121CBFE8847D9E0D312F05986BB (void);
// 0x00000412 System.Void SimpleJSON.JSONNumber::set_AsLong(System.Int64)
extern void JSONNumber_set_AsLong_m541EF4E20CD8683CA860E0B969CECF7B71E2A357 (void);
// 0x00000413 System.UInt64 SimpleJSON.JSONNumber::get_AsULong()
extern void JSONNumber_get_AsULong_mD1EB0D23B9143C4CC1AA4BF75F17E326C08785CA (void);
// 0x00000414 System.Void SimpleJSON.JSONNumber::set_AsULong(System.UInt64)
extern void JSONNumber_set_AsULong_m320EA0ACC4B63183B5223CFCF0B25B8DA383C0DA (void);
// 0x00000415 System.Void SimpleJSON.JSONNumber::.ctor(System.Double)
extern void JSONNumber__ctor_m1CE3527102D15EBC3A183E3519895E291CAC1D90 (void);
// 0x00000416 System.Void SimpleJSON.JSONNumber::.ctor(System.String)
extern void JSONNumber__ctor_m39FDDE1A9EFEE9C4F2498E531D12B97AA49A1BA5 (void);
// 0x00000417 SimpleJSON.JSONNode SimpleJSON.JSONNumber::Clone()
extern void JSONNumber_Clone_m1C9DD94EB3011E55E840B55B4D4F3EAB63AF8A52 (void);
// 0x00000418 System.Void SimpleJSON.JSONNumber::WriteToStringBuilder(System.Text.StringBuilder,System.Int32,System.Int32,SimpleJSON.JSONTextMode)
extern void JSONNumber_WriteToStringBuilder_mD311BC3C1EE3E159C43801EB214F084E567367F2 (void);
// 0x00000419 System.Boolean SimpleJSON.JSONNumber::IsNumeric(System.Object)
extern void JSONNumber_IsNumeric_m9039F8DA776517548A2A6BEA7377B419C0525887 (void);
// 0x0000041A System.Boolean SimpleJSON.JSONNumber::Equals(System.Object)
extern void JSONNumber_Equals_mC04BB811CCAF20E70AE696AE74ECFDF5DA888688 (void);
// 0x0000041B System.Int32 SimpleJSON.JSONNumber::GetHashCode()
extern void JSONNumber_GetHashCode_m976ADFE41037830524798C7E6AFE08006B5F77AD (void);
// 0x0000041C System.Void SimpleJSON.JSONNumber::Clear()
extern void JSONNumber_Clear_mEB7835A2B2D433CE017CFD91CAE974ADB27CE72C (void);
// 0x0000041D SimpleJSON.JSONNodeType SimpleJSON.JSONBool::get_Tag()
extern void JSONBool_get_Tag_m82CE84C4C89E157D4DB036B9F0745343C005C338 (void);
// 0x0000041E System.Boolean SimpleJSON.JSONBool::get_IsBoolean()
extern void JSONBool_get_IsBoolean_m2671AE98710859611DF47E6BC58E6582C3A5B445 (void);
// 0x0000041F SimpleJSON.JSONNode/Enumerator SimpleJSON.JSONBool::GetEnumerator()
extern void JSONBool_GetEnumerator_mA07A10A6111713F7AD09FF03D09A6028556094D9 (void);
// 0x00000420 System.String SimpleJSON.JSONBool::get_Value()
extern void JSONBool_get_Value_mBEA89869448B0B597758D5BF2A3B576CA0BB64E3 (void);
// 0x00000421 System.Void SimpleJSON.JSONBool::set_Value(System.String)
extern void JSONBool_set_Value_mC960EE4083CA91D0059BE24661AFC06E131E2CFC (void);
// 0x00000422 System.Boolean SimpleJSON.JSONBool::get_AsBool()
extern void JSONBool_get_AsBool_mE04224144EAD0A9AD2F3B14BC0C68557A3BF22AC (void);
// 0x00000423 System.Void SimpleJSON.JSONBool::set_AsBool(System.Boolean)
extern void JSONBool_set_AsBool_m88EDF61A5ABBFF3ECF723312852E14F3C60AE365 (void);
// 0x00000424 System.Void SimpleJSON.JSONBool::.ctor(System.Boolean)
extern void JSONBool__ctor_mBB02E388CFB96B99E84561FCFF68147F00391C58 (void);
// 0x00000425 System.Void SimpleJSON.JSONBool::.ctor(System.String)
extern void JSONBool__ctor_m8CFB6AA78095EA003AB9B5EDD8932E8E0B01A1B9 (void);
// 0x00000426 SimpleJSON.JSONNode SimpleJSON.JSONBool::Clone()
extern void JSONBool_Clone_m0B98A17130A9A6FCEC5A92408F551E344CB80274 (void);
// 0x00000427 System.Void SimpleJSON.JSONBool::WriteToStringBuilder(System.Text.StringBuilder,System.Int32,System.Int32,SimpleJSON.JSONTextMode)
extern void JSONBool_WriteToStringBuilder_m82C70C80863730E8A22EE7A5B099C765F2E1D91E (void);
// 0x00000428 System.Boolean SimpleJSON.JSONBool::Equals(System.Object)
extern void JSONBool_Equals_m2671F40DA8F1128BA1451FE7066515C6E0C50D45 (void);
// 0x00000429 System.Int32 SimpleJSON.JSONBool::GetHashCode()
extern void JSONBool_GetHashCode_mC5B59375A9EE9978A5ADD1A24ECEE3FC920836DB (void);
// 0x0000042A System.Void SimpleJSON.JSONBool::Clear()
extern void JSONBool_Clear_m7841012AB307EA72DCFA23305AF45E45ACF7B7DE (void);
// 0x0000042B SimpleJSON.JSONNull SimpleJSON.JSONNull::CreateOrGet()
extern void JSONNull_CreateOrGet_mDC16038413CE71B027A7F9AB1546AF8666D3D3BD (void);
// 0x0000042C System.Void SimpleJSON.JSONNull::.ctor()
extern void JSONNull__ctor_m909243259F39D10FA6FEB176474DEF9C9972D76B (void);
// 0x0000042D SimpleJSON.JSONNodeType SimpleJSON.JSONNull::get_Tag()
extern void JSONNull_get_Tag_m89A7F368EA6269874235F85E43AE82254AAFD41E (void);
// 0x0000042E System.Boolean SimpleJSON.JSONNull::get_IsNull()
extern void JSONNull_get_IsNull_m1174212D6379871AC361EF06FA05DD510FC55595 (void);
// 0x0000042F SimpleJSON.JSONNode/Enumerator SimpleJSON.JSONNull::GetEnumerator()
extern void JSONNull_GetEnumerator_m16D254C74386D1A0AB2EFD1DE0EAF409C73B7686 (void);
// 0x00000430 System.String SimpleJSON.JSONNull::get_Value()
extern void JSONNull_get_Value_mB15431220D7D0B45CE002A204DF9E070CF78DBE0 (void);
// 0x00000431 System.Void SimpleJSON.JSONNull::set_Value(System.String)
extern void JSONNull_set_Value_mAF0CD2E912EF772E0892EB4ABB77294F689CF20A (void);
// 0x00000432 System.Boolean SimpleJSON.JSONNull::get_AsBool()
extern void JSONNull_get_AsBool_m6F3817CD49ED7CC10C180D31D84ED4B0151C78CE (void);
// 0x00000433 System.Void SimpleJSON.JSONNull::set_AsBool(System.Boolean)
extern void JSONNull_set_AsBool_m5717BC3921B7DE0683E9160B3816628B5CBC663D (void);
// 0x00000434 SimpleJSON.JSONNode SimpleJSON.JSONNull::Clone()
extern void JSONNull_Clone_m103493F0850508FB95CCA260491BAA283658289F (void);
// 0x00000435 System.Boolean SimpleJSON.JSONNull::Equals(System.Object)
extern void JSONNull_Equals_m8A39CAD3A41E9584C434B90A1360C62B3E158DE6 (void);
// 0x00000436 System.Int32 SimpleJSON.JSONNull::GetHashCode()
extern void JSONNull_GetHashCode_m74BE6286F06C6E7D5E35381E8BD27215117D9061 (void);
// 0x00000437 System.Void SimpleJSON.JSONNull::WriteToStringBuilder(System.Text.StringBuilder,System.Int32,System.Int32,SimpleJSON.JSONTextMode)
extern void JSONNull_WriteToStringBuilder_mB5B78BFA6A4943319926C1B2AE93F68C7B9B5FFD (void);
// 0x00000438 System.Void SimpleJSON.JSONNull::.cctor()
extern void JSONNull__cctor_m00A365175E9F31A2842DA242EE490783F0EAC483 (void);
// 0x00000439 SimpleJSON.JSONNodeType SimpleJSON.JSONLazyCreator::get_Tag()
extern void JSONLazyCreator_get_Tag_m1CB86FEA25328F1BE9CC01F6D020C9450E9F466E (void);
// 0x0000043A SimpleJSON.JSONNode/Enumerator SimpleJSON.JSONLazyCreator::GetEnumerator()
extern void JSONLazyCreator_GetEnumerator_m720BF0642A079A8BD44F6D650CF4D833DEF67757 (void);
// 0x0000043B System.Void SimpleJSON.JSONLazyCreator::.ctor(SimpleJSON.JSONNode)
extern void JSONLazyCreator__ctor_m0B3625D19DDD8DBDBB45822FAABCE266FA4EE694 (void);
// 0x0000043C System.Void SimpleJSON.JSONLazyCreator::.ctor(SimpleJSON.JSONNode,System.String)
extern void JSONLazyCreator__ctor_m02E2D630C60045F25A3AC001B7A17DF2D5D197B4 (void);
// 0x0000043D T SimpleJSON.JSONLazyCreator::Set(T)
// 0x0000043E SimpleJSON.JSONNode SimpleJSON.JSONLazyCreator::get_Item(System.Int32)
extern void JSONLazyCreator_get_Item_m562D16AE7F1F0CACA5ED050B390B63F98EBC77B1 (void);
// 0x0000043F System.Void SimpleJSON.JSONLazyCreator::set_Item(System.Int32,SimpleJSON.JSONNode)
extern void JSONLazyCreator_set_Item_m42894F9D00193BC7138C5D451E1B0BBD1BFE1084 (void);
// 0x00000440 SimpleJSON.JSONNode SimpleJSON.JSONLazyCreator::get_Item(System.String)
extern void JSONLazyCreator_get_Item_mF7AE3ADFBE062BF3B83FECCE0EF10F10996DE0CD (void);
// 0x00000441 System.Void SimpleJSON.JSONLazyCreator::set_Item(System.String,SimpleJSON.JSONNode)
extern void JSONLazyCreator_set_Item_m0107997E3B3CB75FACD86FB487C5D9416171CBEC (void);
// 0x00000442 System.Void SimpleJSON.JSONLazyCreator::Add(SimpleJSON.JSONNode)
extern void JSONLazyCreator_Add_mA8451EE34FEA0205B6BD6527AB46E5926451F49F (void);
// 0x00000443 System.Void SimpleJSON.JSONLazyCreator::Add(System.String,SimpleJSON.JSONNode)
extern void JSONLazyCreator_Add_mDC69A4E203B73054072D1575EC4CF20D95064F61 (void);
// 0x00000444 System.Boolean SimpleJSON.JSONLazyCreator::op_Equality(SimpleJSON.JSONLazyCreator,System.Object)
extern void JSONLazyCreator_op_Equality_m46508F81FB60FE9DCA683335676093A23D59D799 (void);
// 0x00000445 System.Boolean SimpleJSON.JSONLazyCreator::op_Inequality(SimpleJSON.JSONLazyCreator,System.Object)
extern void JSONLazyCreator_op_Inequality_m06C76EEC055AE314ED6E4FE7A49719AC7ACA397D (void);
// 0x00000446 System.Boolean SimpleJSON.JSONLazyCreator::Equals(System.Object)
extern void JSONLazyCreator_Equals_m753939907CFDB1548B0DAAB38E4737EF17B50066 (void);
// 0x00000447 System.Int32 SimpleJSON.JSONLazyCreator::GetHashCode()
extern void JSONLazyCreator_GetHashCode_m878E7AFF42AE5C43F4F643B6AEB25662491316F9 (void);
// 0x00000448 System.Int32 SimpleJSON.JSONLazyCreator::get_AsInt()
extern void JSONLazyCreator_get_AsInt_mE1404FBC99CE4E8EF4ABBE0BDF661206BAC2C44D (void);
// 0x00000449 System.Void SimpleJSON.JSONLazyCreator::set_AsInt(System.Int32)
extern void JSONLazyCreator_set_AsInt_m13146E53FD6A2F7573B752BFF079E0AF6A5FAE74 (void);
// 0x0000044A System.Single SimpleJSON.JSONLazyCreator::get_AsFloat()
extern void JSONLazyCreator_get_AsFloat_m2600D4B0E1179583EFE268070C66EAC11D380E04 (void);
// 0x0000044B System.Void SimpleJSON.JSONLazyCreator::set_AsFloat(System.Single)
extern void JSONLazyCreator_set_AsFloat_m9DCF79C70D4ED3728C12B709A6D95A0F0A057DE0 (void);
// 0x0000044C System.Double SimpleJSON.JSONLazyCreator::get_AsDouble()
extern void JSONLazyCreator_get_AsDouble_m41D6DF89CD7CEC00F36962068EE072D391EC0B38 (void);
// 0x0000044D System.Void SimpleJSON.JSONLazyCreator::set_AsDouble(System.Double)
extern void JSONLazyCreator_set_AsDouble_mB7ABE38136DBEDA7CC9AC12A381322D6C49ADED9 (void);
// 0x0000044E System.Int64 SimpleJSON.JSONLazyCreator::get_AsLong()
extern void JSONLazyCreator_get_AsLong_mFBA0000985629FA20509FA45A6A8B751C9CAC2B8 (void);
// 0x0000044F System.Void SimpleJSON.JSONLazyCreator::set_AsLong(System.Int64)
extern void JSONLazyCreator_set_AsLong_mBD4640D2F347DEF793A631A44026A03D3D5D73A4 (void);
// 0x00000450 System.UInt64 SimpleJSON.JSONLazyCreator::get_AsULong()
extern void JSONLazyCreator_get_AsULong_m09F6B8D28F383D9A0F857339A6663B24D6AB97A2 (void);
// 0x00000451 System.Void SimpleJSON.JSONLazyCreator::set_AsULong(System.UInt64)
extern void JSONLazyCreator_set_AsULong_m5514AFD97B29BBA5D1A4EC80F7086929DE977A7D (void);
// 0x00000452 System.Boolean SimpleJSON.JSONLazyCreator::get_AsBool()
extern void JSONLazyCreator_get_AsBool_m7D8AF5879C2C8036916AA6B15E22CB4B80412CF4 (void);
// 0x00000453 System.Void SimpleJSON.JSONLazyCreator::set_AsBool(System.Boolean)
extern void JSONLazyCreator_set_AsBool_m4DB409DB959182CAA610147A51A2ECDBAFEA6092 (void);
// 0x00000454 SimpleJSON.JSONArray SimpleJSON.JSONLazyCreator::get_AsArray()
extern void JSONLazyCreator_get_AsArray_m493C069A3624597885A7B6E00C82E829A84B47C4 (void);
// 0x00000455 SimpleJSON.JSONObject SimpleJSON.JSONLazyCreator::get_AsObject()
extern void JSONLazyCreator_get_AsObject_mE01B43B261A6A56F4FCE40AB11F3AAF90B7C292D (void);
// 0x00000456 System.Void SimpleJSON.JSONLazyCreator::WriteToStringBuilder(System.Text.StringBuilder,System.Int32,System.Int32,SimpleJSON.JSONTextMode)
extern void JSONLazyCreator_WriteToStringBuilder_mC9975859B1C42C9F5E507E604121D10B2FB2D93D (void);
// 0x00000457 SimpleJSON.JSONNode SimpleJSON.JSON::Parse(System.String)
extern void JSON_Parse_mEE6C962A58074E33C05C49D74221F1852E7963CE (void);
static Il2CppMethodPointer s_methodPointers[1111] = 
{
	AlignmentTest_Start_m2992385C4EC00DF4CBD504AFE7C4733AB74EA27C,
	AlignmentTest_Update_mC549F80E002E8792FC35718DB75096B24417796C,
	AlignmentTest_ValidateReferencesPoints_m204A4E87E164E0B9A7BF3EF65CF1A40DF03C9FD7,
	AlignmentTest_PrintReferencePointAxes_mC8B51A9797C0BAB6F31DF8B4856DBCCFD6DF07E5,
	AlignmentTest_TestAlignment_m0FE6AF80AE013ABF0311E74A818A1739C362664B,
	AlignmentTest_GetReferencePointIndex_mD953C570EF17CBE25C6A6FCEEAC7F098213346B6,
	AlignmentTest_ResetSourcePart_mD9BD350634284BC34A214C90A9097D928BA02877,
	AlignmentTest_AnalyzeRotationConstraints_m6F5468E3CD3F9C5FF79861F198486D2FCF442C06,
	AlignmentTest_OnDrawGizmos_m3B6D469A036F4294CCB23D0FC7ED12B4370CD139,
	AlignmentTest__ctor_m03EB6CA5E2A81935E8B7633CF50EC4512D82D9E4,
	AssemblyAnimation_Start_m6B156AEC545BABFC9E83268A7242AB3E6AAD57A0,
	AssemblyAnimation_AssembleAnimation_mD23C831759F0A86521787D49EB39059B229A87DF,
	AssemblyAnimation_CalculateTargetRotation_mA6508A3B3E9CD605EDDF12699318800E66B5A0D6,
	AssemblyAnimation_GetAxisDirection_m8AFF54066643C0EC9792A5D5060431F772F5F983,
	AssemblyAnimation_MoveAndRotate_mBF7B193EA8886BD53D4AF584C7CC0226BFF99D49,
	AssemblyAnimation_UpdateMountPointPosition_mEF4CC16237F1B31ECDC5989FCAC099D13AE9F060,
	AssemblyAnimation_Move_mDD30F014C6D6EB3049C99A57EEDA225B59E675FA,
	AssemblyAnimation_RotateAndMoveNut_m2A5A259FCF9DB2A7C975DC329853C3C237218F2A,
	AssemblyAnimation_RotateAroundPivot_m11F4F892FE211459F0A8FE6E7E26A3D16020ED9C,
	AssemblyAnimation_OnDrawGizmos_m9C46EA1309C3833F2A60966C729161043E990B0C,
	AssemblyAnimation_PrintDebugInfo_m886797F456E00CE82212CA80B857BED824C4382E,
	AssemblyAnimation_Update_m4775FEC5063A69CB7B4062F471A710822952F9FF,
	AssemblyAnimation_MoveScrewImproved_m34A71C209750820105B716048F82429A90D11EA8,
	AssemblyAnimation_RotateInPlace_mC016BA5F2E9EFCF628B4E57E3299B095D0087D82,
	AssemblyAnimation_MoveNutImproved_m3787B1F890FBC2A73697F7FC99833FFC5360C68E,
	AssemblyAnimation_MoveAndRotateByMountPoint_m0490CD567207CBB769708FEC8C33887C1F0EDA3D,
	AssemblyAnimation_MoveByMountPoint_m3F9E1AB39B0010533A6D51206BA700BC14168F57,
	AssemblyAnimation_AlignTransformPosition_m3D151DC8759C7E823C727DFD49ED7E446ADDF9F7,
	AssemblyAnimation__ctor_m3B6ADFF0903CB9927E995FE63DC4FEDCE5AEE408,
	U3CAssembleAnimationU3Ed__21__ctor_m486944FD17AD41EF0EB65721E434EDD0C859FBC0,
	U3CAssembleAnimationU3Ed__21_System_IDisposable_Dispose_mC982219E4D12769F0121C6B9F5BC0A547268F74C,
	U3CAssembleAnimationU3Ed__21_MoveNext_m9195D7E71EFAA2CD414BCE34293DD023C25543BA,
	U3CAssembleAnimationU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDCA1BD8238361DF92C2206969BA75929D5886379,
	U3CAssembleAnimationU3Ed__21_System_Collections_IEnumerator_Reset_m6E8DD4B28AB37EE167F18FB88D936E3295AAF610,
	U3CAssembleAnimationU3Ed__21_System_Collections_IEnumerator_get_Current_m0C9027DEDA977EF87AD0476301DAC9FAA27D0B67,
	U3CMoveAndRotateU3Ed__24__ctor_m14BA0EF9D087AA07D1FA44644D8D76EE7B3C1EAC,
	U3CMoveAndRotateU3Ed__24_System_IDisposable_Dispose_m122DFE0E8B6098A97535CBDE8823AB03ECE2275F,
	U3CMoveAndRotateU3Ed__24_MoveNext_mFA6F675DE65A4AA8970D97C434AEBA3849B55BEE,
	U3CMoveAndRotateU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m252FE712A52F09C50A074FB2D672EEB600BFB317,
	U3CMoveAndRotateU3Ed__24_System_Collections_IEnumerator_Reset_mEE4599C8344317F907FAFE052180C8CAC745C1E8,
	U3CMoveAndRotateU3Ed__24_System_Collections_IEnumerator_get_Current_mEB6C6624554E112D7CB1C745504DC9ECC5D01589,
	U3CMoveU3Ed__26__ctor_m9716B47FCF49AC1DB55C0D018EBC2A9E888E1351,
	U3CMoveU3Ed__26_System_IDisposable_Dispose_m13AAE265F854DD6C74470BA76FB235FC2A3AE5BD,
	U3CMoveU3Ed__26_MoveNext_m09017BC9C35757AEB0E89872C7623A4ED9CA3205,
	U3CMoveU3Ed__26_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m184E395C74F4E6536C76E8F549D0102279BD21EA,
	U3CMoveU3Ed__26_System_Collections_IEnumerator_Reset_m48E2711DAA629D40F110620E7C00F3DF1F28234E,
	U3CMoveU3Ed__26_System_Collections_IEnumerator_get_Current_m971AC46F3CF6750BBE7F0C586BD91ACC97DCD68E,
	U3CRotateAndMoveNutU3Ed__27__ctor_m0AA3BFBE304E14564FA817E68A63F9E529D8C6DB,
	U3CRotateAndMoveNutU3Ed__27_System_IDisposable_Dispose_m9CCAE6E643EA4FC7C0EADAF67D1357BC131FAE0A,
	U3CRotateAndMoveNutU3Ed__27_MoveNext_mB1AAAF5FEBAF1D2B290AB05349BF2683CB52FA17,
	U3CRotateAndMoveNutU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7417189A45F030516B044C8A83724507E296718C,
	U3CRotateAndMoveNutU3Ed__27_System_Collections_IEnumerator_Reset_m8368D49D750EF526B25970EDAF45917ADC48FA54,
	U3CRotateAndMoveNutU3Ed__27_System_Collections_IEnumerator_get_Current_mC164F69E0B2620D4B773E91FDD95E76EDE2183D5,
	U3CRotateAroundPivotU3Ed__28__ctor_m7514BDD06AE5CBE0394539FF5B388116E252AF59,
	U3CRotateAroundPivotU3Ed__28_System_IDisposable_Dispose_mFFC5549CC8E641BDC1D8D67D1899ED2DF9F577C9,
	U3CRotateAroundPivotU3Ed__28_MoveNext_m7049D492430B44C01E36B6966FCBF05AE1CA7487,
	U3CRotateAroundPivotU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD87D0B599C83DB3030C042558E7BC11DF134029F,
	U3CRotateAroundPivotU3Ed__28_System_Collections_IEnumerator_Reset_m8F009E68606B9434FF94AEA9025D3D0220F95A0D,
	U3CRotateAroundPivotU3Ed__28_System_Collections_IEnumerator_get_Current_m995BEB903FD7837F6FECCE90B73058479F3E57F3,
	U3CMoveScrewImprovedU3Ed__32__ctor_mCDE0CDA7508BE39FB54800AF3DB2A2F399815EC6,
	U3CMoveScrewImprovedU3Ed__32_System_IDisposable_Dispose_mC1BD0D23CC393EBECFCEB68B629D2BB8900C11D2,
	U3CMoveScrewImprovedU3Ed__32_MoveNext_mE0EFAD28F37BFBBD90687BAA2F449DC7136C5459,
	U3CMoveScrewImprovedU3Ed__32_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mE6549B0292EA7BBD8E33A7CA1AA62171C351CC4A,
	U3CMoveScrewImprovedU3Ed__32_System_Collections_IEnumerator_Reset_mCC6F396AE06E5BCA37EBB1F5F43EDDB5EF0AFFA7,
	U3CMoveScrewImprovedU3Ed__32_System_Collections_IEnumerator_get_Current_m822E2E6F2C3DC8D60CDFA7D759B9588A8B793FDE,
	U3CRotateInPlaceU3Ed__33__ctor_mC8C4126A76A5AFA2BED0922A9A560AEBA42FD4C4,
	U3CRotateInPlaceU3Ed__33_System_IDisposable_Dispose_m4D19E780BD89A5BE3FD6033C757793FAAED1EB0C,
	U3CRotateInPlaceU3Ed__33_MoveNext_mAFB49C696CAC83C5682FBBEDD560A018337E4A1F,
	U3CRotateInPlaceU3Ed__33_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m966453D359708348AD2877B2B65FC557002CC921,
	U3CRotateInPlaceU3Ed__33_System_Collections_IEnumerator_Reset_mA9E151254C98D3129A5608CF72644B7A44523D7F,
	U3CRotateInPlaceU3Ed__33_System_Collections_IEnumerator_get_Current_mA2EF39F00D69519A69EB032BBAB65571E639FA92,
	U3CMoveNutImprovedU3Ed__34__ctor_mAE734DEEF29803125421859097231EB174A0A816,
	U3CMoveNutImprovedU3Ed__34_System_IDisposable_Dispose_m81E68AF574B8DD98DCECC368AE6EDCDF4239C945,
	U3CMoveNutImprovedU3Ed__34_MoveNext_m3D73BBEA683BC5404B7865852255F53FBC907B94,
	U3CMoveNutImprovedU3Ed__34_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m118403148DDF0A023CDF1CFC10FD389EE84E0285,
	U3CMoveNutImprovedU3Ed__34_System_Collections_IEnumerator_Reset_m6766341120AA0057CA9A4A2D67B3FFB6F6950613,
	U3CMoveNutImprovedU3Ed__34_System_Collections_IEnumerator_get_Current_m3D758031FBF1166270AB8B33D8ECE2BD134C9802,
	U3CMoveAndRotateByMountPointU3Ed__35__ctor_mBA636007EAF50870880909CB73BE2FB864AD4914,
	U3CMoveAndRotateByMountPointU3Ed__35_System_IDisposable_Dispose_mF8426BDEA58C677F89E48D79F73EFA8CBFCB524A,
	U3CMoveAndRotateByMountPointU3Ed__35_MoveNext_m04B0FA0693C400947042F490CFF4F4B06C920270,
	U3CMoveAndRotateByMountPointU3Ed__35_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9321065DDE127745BAFF0090A1529BE7A4D10A3E,
	U3CMoveAndRotateByMountPointU3Ed__35_System_Collections_IEnumerator_Reset_mE4D4C4AA40D64F22503CEC2EBEF702B06A4593B7,
	U3CMoveAndRotateByMountPointU3Ed__35_System_Collections_IEnumerator_get_Current_mC27596FEB950ECF7C6BD79B33462B221947244EF,
	U3CMoveByMountPointU3Ed__36__ctor_m1F54AE1061A0248056DF5DD5999A009334899B3B,
	U3CMoveByMountPointU3Ed__36_System_IDisposable_Dispose_m97D03CFD8318D2EA71F3DA9ADAA0243214A851D1,
	U3CMoveByMountPointU3Ed__36_MoveNext_mA51856E916803374B00A52F35F194BBCA0356FA2,
	U3CMoveByMountPointU3Ed__36_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF9266A75C9C4C9A8B0B0F64E4F7AD3C5E478D2D7,
	U3CMoveByMountPointU3Ed__36_System_Collections_IEnumerator_Reset_mAFA5F0C0C5A727A53F1189E6195C432A9890B47E,
	U3CMoveByMountPointU3Ed__36_System_Collections_IEnumerator_get_Current_mC81D9F1CB442057CF329B637CF00BACF54CA3CE4,
	AssemblyAnimationManager_get_AnimationSpeedRate_mDDBE70FD2FAD13DD46C69E376953215CF3B33E68,
	AssemblyAnimationManager_set_AnimationSpeedRate_m40A5C4BD1F9B4DF2AB78FE5932B160E32FE67292,
	AssemblyAnimationManager_ValidatePart_m46F20A6F8EC1C83A4BA17E29EEE6938FA86433DA,
	AssemblyAnimationManager_AnimateOverTime_m81DD5756B67A920512A58FF3DE976E2B1512249F,
	AssemblyAnimationManager_MovePart_m09C69C191E8C81AA6DAC05CD7EE6A7509B2ABA29,
	AssemblyAnimationManager_RotatePartInPlace_m9347E51B925042884D46A5C1908FD9E911D59D46,
	AssemblyAnimationManager_RotatePartAroundAxis_m63484611346ED485115C86577179D0DF3B21470A,
	AssemblyAnimationManager_MoveAndRotateByMountPoint_m8DD0338BCBBD7B54E03416BE6B2532177419EA73,
	AssemblyAnimationManager_MoveByMountPoint_m81F522832892F3795A0E865113CAD3800B7DCC25,
	AssemblyAnimationManager_AlignTransformPosition_m9D6C3792968DBDB142757118D3C0298A1E2C9391,
	AssemblyAnimationManager_AlignParts_m7A6EA927C278B2855C41C4AA9534EAB306868E78,
	AssemblyAnimationManager_CalculateAlignmentRotation_m11CBD762346D5454230FD794143B9994E37F4CA9,
	AssemblyAnimationManager_GetAxisDirection_m4D43AF8864F81864ACCC4EC249DB55B8B9A82DB4,
	AssemblyAnimationManager_InstallScrew_mD607DC25321427D9DF79BD546E1EEE269D5CC6E1,
	AssemblyAnimationManager_InstallNut_m402E3A46E8C657C86E6C3B7465E58B09F682D35F,
	AssemblyAnimationManager_AlignPartsAsync_m5E585B7D17D642E4074BFD40D17713910AB09CBE,
	AssemblyAnimationManager_AlignPartsSync_m688E20F88A62998DAF49DDB8D7EBFD2A62F982E4,
	AssemblyAnimationManager_CompleteAssemblySequence_m2D88E4AFFF901634E3B1D159D3AC9C12CB12C03D,
	AssemblyAnimationManager__ctor_mBEC5D5A7F30D782D77777D225EC4EB2BE97E1531,
	U3CAnimateOverTimeU3Ed__10__ctor_m6FCD3B0A5AF03A24AF7855090ECB2A3599AD626F,
	U3CAnimateOverTimeU3Ed__10_System_IDisposable_Dispose_mF4C77CC94ED80E76C76F571E6811ADB7ADE3454F,
	U3CAnimateOverTimeU3Ed__10_MoveNext_m195A1FC6B97364310709D23CE85B1D4F86854370,
	U3CAnimateOverTimeU3Ed__10_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0B993B2148D70AAD993C1717D53A5784D27ECACA,
	U3CAnimateOverTimeU3Ed__10_System_Collections_IEnumerator_Reset_mB732BBA347180FD8B94FBEA662120F00D8786111,
	U3CAnimateOverTimeU3Ed__10_System_Collections_IEnumerator_get_Current_m4F0F9A98AAEFDEEB272405E86F8B02A5335C4F60,
	U3CU3Ec__DisplayClass11_0__ctor_m967251DBE7B3B10F213ADD91883755FADA548AB4,
	U3CU3Ec__DisplayClass11_0_U3CMovePartU3Eb__0_mFE9AE39CD908349C0EAEE49C2A8FFF428D94C85F,
	U3CU3Ec__DisplayClass11_0_U3CMovePartU3Eb__1_m17628096485283FE54E09C9F938CD11ED9CC6E06,
	U3CMovePartU3Ed__11__ctor_mFA3733646E1A3F848FE6AF0D2FCEEC8D68E50693,
	U3CMovePartU3Ed__11_System_IDisposable_Dispose_mA079DA89C6672151C2CD1314F5765C125AF96C57,
	U3CMovePartU3Ed__11_MoveNext_m21218F444A2D0047B188088AB460646A93D9305B,
	U3CMovePartU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m55FC00A208A5DB3DD89DA1674D16386029538640,
	U3CMovePartU3Ed__11_System_Collections_IEnumerator_Reset_m5F3B4BD4B900E1216395E69DF7DA981CA1A1B549,
	U3CMovePartU3Ed__11_System_Collections_IEnumerator_get_Current_m69793D09EC560C4DD5F3515011D0D4A2D5D06386,
	U3CU3Ec__DisplayClass12_0__ctor_mB6F29EF3938BE271918C03B9E60F474FCB91F1A0,
	U3CU3Ec__DisplayClass12_0_U3CRotatePartInPlaceU3Eb__0_m72522C877F6F4AEFE8203DADB1C07D2BBFC61115,
	U3CU3Ec__DisplayClass12_0_U3CRotatePartInPlaceU3Eb__1_m44ACA0310BCB59F931DCB5B64F963C9D23CA1C85,
	U3CRotatePartInPlaceU3Ed__12__ctor_mBEAA0D6912A77EF3CA7345E3F8536984DFA9BD7E,
	U3CRotatePartInPlaceU3Ed__12_System_IDisposable_Dispose_m447EE95281F6777373242F2B98710E144AEFEC9F,
	U3CRotatePartInPlaceU3Ed__12_MoveNext_mA8B419B80BF09EF8959DCF95FEE2B073EB740F1C,
	U3CRotatePartInPlaceU3Ed__12_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mFB47B3D54C6AB9FF50DE010DAB96D2C8AEA412ED,
	U3CRotatePartInPlaceU3Ed__12_System_Collections_IEnumerator_Reset_m02A1EA77E16E756AC9EC6DC86F8B7E856065BE46,
	U3CRotatePartInPlaceU3Ed__12_System_Collections_IEnumerator_get_Current_m06B320F837DFD7E7B8CDCA391D2A97C3CE263DB7,
	U3CU3Ec__DisplayClass13_0__ctor_mBCC63144A53BF221E89F58AF6381A540E801EB51,
	U3CU3Ec__DisplayClass13_0_U3CRotatePartAroundAxisU3Eb__0_mA262C9AAA7259FB504A9C0603F0FAC70549F5C6F,
	U3CU3Ec__DisplayClass13_0_U3CRotatePartAroundAxisU3Eb__1_mE14982D28A9AA313AE0092D73E6FAAC5B6950B25,
	U3CRotatePartAroundAxisU3Ed__13__ctor_mBB1D7E0F7826D2919AA3B89AB3BE3A8D739A92D7,
	U3CRotatePartAroundAxisU3Ed__13_System_IDisposable_Dispose_m2AE5F0A9BE26DA938FBC3E7D2CDE3CBCA540ADC4,
	U3CRotatePartAroundAxisU3Ed__13_MoveNext_m850999FBC7D938BAB9B0DD1819C0F7600F56ADE9,
	U3CRotatePartAroundAxisU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mFC1A4041D2B788A31244F310786491D89C1ADAFE,
	U3CRotatePartAroundAxisU3Ed__13_System_Collections_IEnumerator_Reset_mA646940B486F8DF2ABDC54D9BB4A522D6DF474D2,
	U3CRotatePartAroundAxisU3Ed__13_System_Collections_IEnumerator_get_Current_mC12DCAF15357B258D61791DDD82B51736D590D71,
	U3CU3Ec__DisplayClass14_0__ctor_m5B7FEE20A5C07A27340298EAE5D539571DF7A66C,
	U3CU3Ec__DisplayClass14_0_U3CMoveAndRotateByMountPointU3Eb__0_m7EC2C1E485309DC5260F9CE9562BEF09F7B54D85,
	U3CU3Ec__DisplayClass14_0_U3CMoveAndRotateByMountPointU3Eb__1_mC71660A82CF4BC0BDE3B78AE5DF10942D7F105EA,
	U3CMoveAndRotateByMountPointU3Ed__14__ctor_m9A080D9627117975CA8C32F3604679177706FDD0,
	U3CMoveAndRotateByMountPointU3Ed__14_System_IDisposable_Dispose_m3D9FCEC1E9F76AA3208DE678E6DF2DC0DB8A6D91,
	U3CMoveAndRotateByMountPointU3Ed__14_MoveNext_m9BF3CBA940C3AE5D8149D70FD9B41515E2C3274A,
	U3CMoveAndRotateByMountPointU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB8382E78AF30CFC27ACF7CE8D7C003BF4BB807D4,
	U3CMoveAndRotateByMountPointU3Ed__14_System_Collections_IEnumerator_Reset_m0910B6ADAB14DD9159A00C7F31AB2DED22F786F9,
	U3CMoveAndRotateByMountPointU3Ed__14_System_Collections_IEnumerator_get_Current_m1487B11E1F693248CEC406CA5AC9C2DB0D325389,
	U3CU3Ec__DisplayClass15_0__ctor_mF6E8D22BC5FFBFB118E420CC73A0C72A976B8107,
	U3CU3Ec__DisplayClass15_0_U3CMoveByMountPointU3Eb__0_mB05EF60579F08183A9971542487DC38E3615E699,
	U3CU3Ec__DisplayClass15_0_U3CMoveByMountPointU3Eb__1_m0F56C3D3B73ACFAE4A74FAF6945D2277689980D7,
	U3CMoveByMountPointU3Ed__15__ctor_m982295494133219B3C01226A883BEA8EC569A4D6,
	U3CMoveByMountPointU3Ed__15_System_IDisposable_Dispose_m8EEC45AECB0F1B2462BD6F6476952424DA8F3A6E,
	U3CMoveByMountPointU3Ed__15_MoveNext_mCDBA24FC2646E2FCD400F35F974795C9F1E71FC7,
	U3CMoveByMountPointU3Ed__15_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1BC767D8A33C5A375BDF4ECE75E0C38D3E1B2B95,
	U3CMoveByMountPointU3Ed__15_System_Collections_IEnumerator_Reset_mD64E62DB133113EFD531A989ECB1EF4731F19BEB,
	U3CMoveByMountPointU3Ed__15_System_Collections_IEnumerator_get_Current_m1581E82FD713A836C717677202408056D67DDAEC,
	U3CAlignPartsU3Ed__17__ctor_mB5D8DDBCF1971CBDF22EF7C5809AE214E88DE8ED,
	U3CAlignPartsU3Ed__17_System_IDisposable_Dispose_m40C91ED59A35B8A76984C5CD07D3FCB2788BDA6C,
	U3CAlignPartsU3Ed__17_MoveNext_mA1B59E17C37E16B8B82BA8BA15F105A0B08FF6FD,
	U3CAlignPartsU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF518E7555FF4B2DCF918B67E313FDC7CAC4AA2CC,
	U3CAlignPartsU3Ed__17_System_Collections_IEnumerator_Reset_m65CC8619FC89A9B803813020F70F0C87A6B1C3EB,
	U3CAlignPartsU3Ed__17_System_Collections_IEnumerator_get_Current_m215BB3886B1F0545F172FBEC19FA6DE0E8BF0C6E,
	U3CInstallScrewU3Ed__20__ctor_mFFF5D2B23896C8D28E7DE9404D1B6FEC58AB0DAF,
	U3CInstallScrewU3Ed__20_System_IDisposable_Dispose_mD805219881F7C390067A14B1E9613DE9E44DE154,
	U3CInstallScrewU3Ed__20_MoveNext_m11E25DE6C83D14583BB5BF047D814D128578A261,
	U3CInstallScrewU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mADFCEB5C97976EDD36BFE11DD4F52875C8A3298F,
	U3CInstallScrewU3Ed__20_System_Collections_IEnumerator_Reset_m002E0B711EBA27F381052CD9A368205A8793FCDE,
	U3CInstallScrewU3Ed__20_System_Collections_IEnumerator_get_Current_m34CF5377FB92312DC437E444E2F20043D48BAEB4,
	U3CInstallNutU3Ed__21__ctor_m33D0919297A18444AA527119F6AF425631EE8F58,
	U3CInstallNutU3Ed__21_System_IDisposable_Dispose_m442604572C4481A33B63F08FDDD8231B86AE8803,
	U3CInstallNutU3Ed__21_MoveNext_m7B3938DCEF64AA43B11A0AE6C0528729F57A6FD1,
	U3CInstallNutU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m574645F338EBBD73499835737B97CBC00F9D0582,
	U3CInstallNutU3Ed__21_System_Collections_IEnumerator_Reset_m3B4DBFF0D24901B7C680AB35FB771F4AF80422DF,
	U3CInstallNutU3Ed__21_System_Collections_IEnumerator_get_Current_m4F217576A9159CEA37515A906EED62555AF6979C,
	U3CAlignPartsSyncU3Ed__23__ctor_m89125A48D794F5FCE14384806D911DA878A8E1D9,
	U3CAlignPartsSyncU3Ed__23_System_IDisposable_Dispose_m44C3F9883751EFD57D16BD3A4344FC439C9F5054,
	U3CAlignPartsSyncU3Ed__23_MoveNext_m702695B033F90E38B6C660E6300DA10F66B81D19,
	U3CAlignPartsSyncU3Ed__23_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF1984E7DCA1A253A1BE2FE00D871366DFA7B9C87,
	U3CAlignPartsSyncU3Ed__23_System_Collections_IEnumerator_Reset_mDDFD01B78693121FD30943BD678F0738412C2892,
	U3CAlignPartsSyncU3Ed__23_System_Collections_IEnumerator_get_Current_m930CCA9D1CFD7BFB03B5F5A32E3EF53C5980D745,
	U3CCompleteAssemblySequenceU3Ed__24__ctor_m6873ECA5E7A1B716F0EBC7343F2352A0BFEA116B,
	U3CCompleteAssemblySequenceU3Ed__24_System_IDisposable_Dispose_mBFA49DF4E24E707B41CB3E9103F535A909F97718,
	U3CCompleteAssemblySequenceU3Ed__24_MoveNext_mF07AF18D2B7FE9D8FDAEC0CB35E06D72BEA061CE,
	U3CCompleteAssemblySequenceU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m6A0F91B1BA366E512665D752B47AB37752D22CBF,
	U3CCompleteAssemblySequenceU3Ed__24_System_Collections_IEnumerator_Reset_m4E60FE5DEFC1708BF1B59877D3C882F2689A7F10,
	U3CCompleteAssemblySequenceU3Ed__24_System_Collections_IEnumerator_get_Current_mE1E14AAB8061094B63E88C56F85DB287B36BFF7D,
	AssemblyAnimationService_get_AnimationManager_m2390D3B47AAE7E2447E4C67572CB7C27DC1B3AC5,
	AssemblyAnimationService_Awake_mB7E5899A0B2D0BAFC2C5CB6A6A0A47FE12DAAC93,
	AssemblyAnimationService_Start_m2F6680EF6EF0EF87313B5852350D6CD2AA65A9E4,
	AssemblyAnimationService_AlignPartsPublic_mA8042FD975498F9879E139EE96089CBA5CADB774,
	AssemblyAnimationService_MoveScrewImprovedPublic_m60D285E03B83A1FB2FD0A5D91BCCFECBC55EFB9B,
	AssemblyAnimationService_MoveNutImprovedPublic_mDE246AD0253F5EB806E42CB521680F08C3C06AE4,
	AssemblyAnimationService_GetAxisDirection_m16F7FB7DF40731F203B4972B7013BEC0AABEB1D5,
	AssemblyAnimationService_MoveScrewImproved_m84B33DE65006D0CC6399AFFAC3DD9DC7333C1E35,
	AssemblyAnimationService_MoveNutImproved_m6DF3B64C6188156C89239B005BBC0AECB6EFCCB8,
	AssemblyAnimationService__ctor_mFE7BB99BDB3CC7DA6229F2F87F925E7C8378C41B,
	U3CMoveScrewImprovedU3Ed__14__ctor_mF7CAD27EE50C47CE7D9B100CA08BBD1818CDFA17,
	U3CMoveScrewImprovedU3Ed__14_System_IDisposable_Dispose_m8C2780039594A2BA7CABE0108A0BDDA2738D6EE6,
	U3CMoveScrewImprovedU3Ed__14_MoveNext_mA0D4AA02C62875E2B30B4B68AE49BD83FEDDB258,
	U3CMoveScrewImprovedU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5E42E26C3E4B6EB3606DC959E3B2ED1F247538F9,
	U3CMoveScrewImprovedU3Ed__14_System_Collections_IEnumerator_Reset_mB3378F9CAB2D4FBD28B57B19AB2E4A09552342EA,
	U3CMoveScrewImprovedU3Ed__14_System_Collections_IEnumerator_get_Current_mCA66715AE837146DF72DEEA7DDA288A029C70BFD,
	U3CMoveNutImprovedU3Ed__15__ctor_mDD0643FFE94EA0DE1C955C8BF1A4E3C6A5A40355,
	U3CMoveNutImprovedU3Ed__15_System_IDisposable_Dispose_mDADC295F6FF625100DC91523F1B1D93D9A05CD28,
	U3CMoveNutImprovedU3Ed__15_MoveNext_m7ACA9DC3B5514A5366E06476CA100C79F95070CF,
	U3CMoveNutImprovedU3Ed__15_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m81FDD57950948E47486F944A75DB02644169422C,
	U3CMoveNutImprovedU3Ed__15_System_Collections_IEnumerator_Reset_mDB3E2043E8BEF95FF79784AA22A37105DF1CB65B,
	U3CMoveNutImprovedU3Ed__15_System_Collections_IEnumerator_get_Current_m532C2F3BA34DD38F05EBF7C256BABA6533E37531,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AssemblyPart_get_PartTransform_m4DBAD54DBC01EA8AA0B9CE3F213F16BF39F4185B,
	AssemblyPart_get_PartName_m6214ABEAAF046BDED82DEF234113EC5242D85403,
	AssemblyPart_get_Type_m7F1E8F177BCDAF6F36A237322F53A5B0923E4461,
	AssemblyPart_get_ReferencePoints_m78FB08E4A8A88DB9C21CE9D33063B7186F578282,
	AssemblyPart_get_HasReferencePoints_m4101AA83A26052106AD881D2DC804625891542F5,
	AssemblyPart_get_ReferencePointCount_mF33DFB60760BFAF332ABD9E985F0CD490B7C9B3B,
	AssemblyPart_get_MountPoint_mD5D437BBFF699DBD34FB735B8A5C2A9B873CDBEC,
	AssemblyPart_GetReferencePoint_mA201236E27083AF2890401B12D8DD8EAE59A319A,
	AssemblyPart_GetReferencePointByName_mD7F8DDB6004E03A542114CC8A0741B3516BC5237,
	AssemblyPart_GetMountPoint_m2222F2090704F0CDC758B755808AEEC450942A74,
	AssemblyPart_OnValidate_m38D200EFECAD3B6FF377AC38D18AAC318BDE752E,
	AssemblyPart_ValidateReferencePoints_m89909ADC56847528A6D64C919908BA4011F9D627,
	AssemblyPart_OnDrawGizmosSelected_mA4CDB6064D0D756A45BA6A6803030B952DCABBF9,
	AssemblyPart_DrawReferencePointGizmo_m1CCA44422B01AFC722B02C7A9032345CC8264F44,
	AssemblyPart__ctor_m3A868BA0F7EC280E2368E4F589546FDD3695782A,
	AssemblyStep_CreateDirect_m12C41BB8FF5AC02BEF0B44BD7CDFBD717F12E0E8,
	AssemblyStep_CreateWithScrew_m34C4D028BD6F7243EA8B441F9A66089668760BA4,
	AssemblyStep_Create_m6DB78A29CAB7CEA6B3615B08A2B35907A4165D7A,
	AssemblyStep_AddMountPoint_m194EA8814100C8F34D6BB078603A47010B7B707D,
	AssemblyStep_ToString_m320E7A2E2902A63202043DA88272743C043F82EB,
	AssemblyStep_IsValid_mF9FCF7AB253203EA0B1C0D39E09B71CE913F6A41,
	AssemblyStep_RequiresFastener_mFD703603DDAD3E14B20E48D70E9EE9A5E3988974,
	AssemblyStep_HasAdditionalMountPoints_m5341C821D4FE304D28E15E715126EF409BEA18E3,
	FastenerInfo_get_Empty_mB6E6BDF922B577CDD26EF4EAFE797397DB6D0F09,
	FastenerInfo_CreateScrew_m81C9F9116D2CFC59BA2634D6D3365B9927E530C1,
	FastenerInfo_GetScrewPrefabName_mF8FF0A3357E0D93D0E92BEAB58C43B420954CC6F,
	FastenerInfo_GetNutPrefabName_mE1B530F590FD507BC8E6580B643F4362D2853EAE,
	AdditionalMountPoint_Create_m599AEDDB62836E0EE9104830BF83B4AE2D3D96C0,
	NULL,
	NULL,
	NULL,
	NULL,
	AssemblyStepData_ToString_mE8C413BE6001E1478285B8AA28545AB3EBC65360,
	ExternalSystemDataProvider_get_DataSourceType_m14CDFE335CB35D4AD1AC1DE3BBB0B2E25D4977DA,
	ExternalSystemDataProvider_get_IsConnected_m428A4A6807AE65917050F1D4BD1FDE5384743B94,
	ExternalSystemDataProvider_set_IsConnected_m6D819239718C5DCC0C40836B3077E444E3EC2178,
	ExternalSystemDataProvider_Start_mC5926556AE446A1DC315041738E77D2C3977BF05,
	ExternalSystemDataProvider_LoadAssemblySteps_m1472E0480FA42AFF7360021D4F824FB8C3D5A452,
	ExternalSystemDataProvider_TestConnection_mDB39828320D3F62AB5BCBC56EB6EF8248B5CE976,
	ExternalSystemDataProvider_RequestAssemblyDataFromExternalSystem_mC61C99CC5DB66CBE28CB449494EAF4F4E8AB73EE,
	ExternalSystemDataProvider_ParseAssemblyDataFromJson_m8DCB25C7EAC9468A6EE2E03D4C0250320A667893,
	ExternalSystemDataProvider_LoadFallbackData_m1B98A53A671D82EA503AE632E1B5415C5DDE1AC9,
	ExternalSystemDataProvider_SetAssemblyData_m3B14BDC51384FD61843D7F9DC1BF8CF9F7B8A166,
	ExternalSystemDataProvider_ClearCache_m9D6121B0F4F95CBF9C21A89CB25B173D131E83C7,
	ExternalSystemDataProvider__ctor_m9590CBEF733E2D7E55508B2FB9B25C5DBFD152BD,
	ExternalSystemDataProvider_U3CStartU3Eb__11_0_mA2C973F8D30BE3B3D3D798DCCC3516D0BCFDDCCB,
	U3CU3Ec__DisplayClass12_0__ctor_mC73E03CEA17878AD793BE79F4E4B3CAE982D06D2,
	U3CU3Ec__DisplayClass12_0_U3CLoadAssemblyStepsU3Eb__0_m16AF587FC247F8F9393586C243453E0879517F8D,
	U3CLoadAssemblyStepsU3Ed__12__ctor_m3C4467A00F1A52FA235CC197F8154C46821806AF,
	U3CLoadAssemblyStepsU3Ed__12_System_IDisposable_Dispose_m823495F27CE6FA0DDB5A212DE42F0B86008943C8,
	U3CLoadAssemblyStepsU3Ed__12_MoveNext_mE99C821ED07EFE7AF1BBB874E479E5033CABF493,
	U3CLoadAssemblyStepsU3Ed__12_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0E4CE5851A7B106D55ADAD5F7E9F1C6D597A441D,
	U3CLoadAssemblyStepsU3Ed__12_System_Collections_IEnumerator_Reset_mAD9121A9CE9C097EE3A7F42FD4D274661A6BC2A3,
	U3CLoadAssemblyStepsU3Ed__12_System_Collections_IEnumerator_get_Current_m04E3677891C88CDBD5394C91BD4E70518891BCC8,
	U3CTestConnectionU3Ed__13__ctor_mED859D27EA19C44ED202C9A522933A077C6B2122,
	U3CTestConnectionU3Ed__13_System_IDisposable_Dispose_m64E944147C46A76E1472CEE05499759C04CF0925,
	U3CTestConnectionU3Ed__13_MoveNext_m17B3C0EFCABCFD6194690E8D5E1EA25D24900CDC,
	U3CTestConnectionU3Ed__13_U3CU3Em__Finally1_mF77D5135A870834B987AED604F7F619AD1DC645B,
	U3CTestConnectionU3Ed__13_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m57C0DAED84E9128B87049D4DB24DC7FFEF2FF4CF,
	U3CTestConnectionU3Ed__13_System_Collections_IEnumerator_Reset_m0109DE10A1A3A1B19A8D19D94B86B6CA750FF7F7,
	U3CTestConnectionU3Ed__13_System_Collections_IEnumerator_get_Current_m0A71964B7E2DB3173D0C01DCF06CE10A79F61A59,
	U3CRequestAssemblyDataFromExternalSystemU3Ed__14__ctor_m646BB5DCB05AFE7A08F5EA4D5666CF4E426C0FC4,
	U3CRequestAssemblyDataFromExternalSystemU3Ed__14_System_IDisposable_Dispose_mD38B1871DF3B73A3E411F2E513100C439DA7CBF2,
	U3CRequestAssemblyDataFromExternalSystemU3Ed__14_MoveNext_m9011DA563EDA71964BD5306F8F12A8549F6E7956,
	U3CRequestAssemblyDataFromExternalSystemU3Ed__14_U3CU3Em__Finally1_mDBFECEC7CE9D75CCBE4A55DABA1B94ADF841E02D,
	U3CRequestAssemblyDataFromExternalSystemU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7CF71093660F98A17DFCDB1CF90366D9783825BC,
	U3CRequestAssemblyDataFromExternalSystemU3Ed__14_System_Collections_IEnumerator_Reset_m5A7B8010C71A615ED9E3C3B6F59146359ECEBC9D,
	U3CRequestAssemblyDataFromExternalSystemU3Ed__14_System_Collections_IEnumerator_get_Current_m73D5DF270A82D9EBDCAD575060CFED95C809EF74,
	Neo4jAssemblyController_add_OnPartSelected_m4D16A619571CE65FD08CFEDDBE34D00E3469E194,
	Neo4jAssemblyController_remove_OnPartSelected_m35A3259406D73B2A30DCA1699D81B0D376B7426F,
	Neo4jAssemblyController_add_OnAssemblyStepsLoaded_mEF161A9592C509DEC5BEE31A2DAB43AA2FE177E3,
	Neo4jAssemblyController_remove_OnAssemblyStepsLoaded_mB8CAA0AEC52E6FFED644C55D5B8FC8FF6A513C2C,
	Neo4jAssemblyController_add_OnStepExecuted_mA0F70E2031DA9BE90755EEB10F1CC206475F157F,
	Neo4jAssemblyController_remove_OnStepExecuted_mFF08276D8FE610DB524C365FD2D26D3DA6D2293E,
	Neo4jAssemblyController_add_OnAssemblyCompleted_m2BAC78501536C56C9D59EBD9AD15360EE42BE25C,
	Neo4jAssemblyController_remove_OnAssemblyCompleted_m6124236E0195C0B5275A79A7094326324CE87755,
	Neo4jAssemblyController_get_AnimationDuration_mD65987078E3F5628F74EDBC6FA7FB8D1AC9A7FA6,
	Neo4jAssemblyController_HasRemainingSteps_mA3C07C4AA8D348AF02248CDFCA02C4B860395570,
	Neo4jAssemblyController_get_AnimationSpeedRate_m4F8403F7507EF50FFC1AF78331AAD835FBCE46FC,
	Neo4jAssemblyController_set_AnimationSpeedRate_mEC2065E1CE70858518AACC29AF215DAD353C069F,
	Neo4jAssemblyController_Start_m3AECDA8E796F8C997D6FCBF00A3854965D62EE8D,
	Neo4jAssemblyController_InitializeDataSource_m4A02EBD71B98D99A666978180F80837642FB5544,
	Neo4jAssemblyController_InitializeVRInput_mF5AAAE73C17E0A6C8AE2B105BDE77F59AE76DB23,
	Neo4jAssemblyController_Update_m55852F29A8A74946F9E1C94B8F7772DDBAECA3A5,
	Neo4jAssemblyController_HandleVRPartSelection_m8D20EC8D130555634039D85720CC60A9C2500E88,
	Neo4jAssemblyController_LoadAssemblyStepsFromExternalSource_m220FD7EEB23B401993043C74AAB99DD339DB643E,
	Neo4jAssemblyController_ReceiveExternalAssemblyData_mADA684E475D76B1FB3BDD135696FAA1AB1F5E521,
	Neo4jAssemblyController_ParseExternalAssemblyData_m539B7AC68C9A8C7C0F0DC85ECCC0E54AD54500D1,
	Neo4jAssemblyController_ProcessExternalAssemblySteps_m9E85D256E06AB803A13D72A63B39F08DEB1BE0CF,
	Neo4jAssemblyController_RegisterEvents_m25E701F38AB55F43F3C27000F68194C07E2A5296,
	Neo4jAssemblyController_UnregisterEvents_mFAA72A59582464448EC861E7F37EA49AD36B925E,
	Neo4jAssemblyController_InitializeVRSystem_mAC7B055F97C12134DB0841F1A985BE7D00FA3AC8,
	Neo4jAssemblyController_WaitForVRPreparation_m54E9E7B24E841DD3B151F5CD649A72FE3D5C1BDA,
	Neo4jAssemblyController_OnDestroy_mAEAF19F1505C989BC014E45A134099F7BAD6C13A,
	Neo4jAssemblyController_InitializePartMapping_m682509B14C0F5C5E53EC3D2C7AE87D9BF4988C1D,
	Neo4jAssemblyController_SaveAllPartsInitialState_mF8E0AE3F5A1EF7EB7D69D072BEAD9583623F8FD2,
	Neo4jAssemblyController_SaveAllFastenersInitialState_m76BBA96BD40D615670BF2C04E489AEA4811E8582,
	Neo4jAssemblyController_RestoreAllPartsInitialState_mF422227C574FAE5053B7A1D338846B44E6208405,
	Neo4jAssemblyController_RestoreAllFastenersInitialState_mAA05BEFC1C4A70827ECEA501FE997AC0D8C4D959,
	Neo4jAssemblyController_SaveStepPartsInitialState_mC896D7D191AD4AF4378380D20610E7740D4AD93A,
	Neo4jAssemblyController_RestoreStepPartsInitialState_m4E32E569376ECCA5FA209A3CDC143684E4FD7FB1,
	Neo4jAssemblyController_SelectPartWithRaycast_m81F9DEE660CCC6206D273753209E8A25D69F0AAA,
	Neo4jAssemblyController_ResetAssemblySteps_mC66B1EE8CE40B790B643BAB96909241CC8B03381,
	Neo4jAssemblyController_QueryAssemblyRelationships_m510B66812C23BBC224DA63FF670E640873B240A7,
	Neo4jAssemblyController_ProcessAssemblyRelationships_mFF290E89F175D17B687084ACDE6CA8EDAF88641F,
	Neo4jAssemblyController_ExecuteNextStep_mACFB3484B07642D7E1AB5DD2FCEF0BF28EFD0C5A,
	Neo4jAssemblyController_ReplayLastStep_mE733ACCC650508A829BD8BC1B4B6583E4D03195D,
	Neo4jAssemblyController_ExecuteNextAssemblyStepCoroutine_m3CF8AF855E6F76DF59E55F173D5D9A09B96035EA,
	Neo4jAssemblyController_ReplayAssemblyStepCoroutine_m3297ABAA72F89163A828E1D6124F9FC1A330AF5E,
	Neo4jAssemblyController_ResetAssembly_mD59C8122A2FB6FB3EFEC64F7CD6269AC2E6EFE23,
	Neo4jAssemblyController_InstallFastener_m9359B647D3FE9CA58FA7E9178FEACEF6EEC6B353,
	Neo4jAssemblyController_GetScrewPrefabName_m11120109918806CCD7A9C806AEA1B0FDCF201DC3,
	Neo4jAssemblyController_GetNutPrefabName_m5E19A285FC2691F9273945519D3D4AF0ECA55D25,
	Neo4jAssemblyController_GetOrCreateFastener_m5668C21E677AD73F08DB03CD0BB4A446D65B32D1,
	Neo4jAssemblyController_GetFastenerFromScene_m71CCB551C77E2F8132E2178C1E00F39E20ABB059,
	Neo4jAssemblyController_CleanupFasteners_m2082E46B24B9678AB1A28D93A0BECE9EA2BA2C9C,
	Neo4jAssemblyController_ValidateReferencePointMappings_mD89452E7D5999153B80C25E96FC5F98824180A0A,
	Neo4jAssemblyController_GetReferencePointIndex_m0D83B603692A1137C3BC03DC391D1CC85EE8B728,
	Neo4jAssemblyController__ctor_m4ADC9376EA623CE58D22058DA0C60245D51BBA9F,
	AssemblyStep__ctor_m40EF831184A0632EF7E7D625F994BB6378CEE04E,
	AssemblyStep__ctor_m9E10466D3C2D06685921F7B5B8A0C782FF812EFE,
	AssemblyStep_ToString_mF97FA0F6A8BF46F54FA596AB0A2C5C7F43719B1B,
	U3CU3Ec__DisplayClass59_0__ctor_m674F3B8084624F71F29620390EBB50983E196BA8,
	U3CU3Ec__DisplayClass59_0_U3CLoadAssemblyStepsFromExternalSourceU3Eb__0_m4F0C3DBE566EB3831FD70B0AEAEB01AC61D340A5,
	U3CLoadAssemblyStepsFromExternalSourceU3Ed__59__ctor_m2C1F4EFC774BF14272FF4C1DE89DE90681AD6C55,
	U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_System_IDisposable_Dispose_m319F5BB430D02162EF3D195381B7E0DE3470B642,
	U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_MoveNext_m4C664DDEE23B0F7DB2C62D6A1D9AD7D437EF41EF,
	U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9E75C1167D32508D6D41500462CB256C3E65D4F5,
	U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_System_Collections_IEnumerator_Reset_m08F35C22B001B0C70C07A90DAFD20092C2B8B704,
	U3CLoadAssemblyStepsFromExternalSourceU3Ed__59_System_Collections_IEnumerator_get_Current_m6300756178A711F4FDAA4DD544522FEEC89132A3,
	U3CWaitForVRPreparationU3Ed__66__ctor_mA26F8D438CFBCC764A3973DF91BB079E5FC86820,
	U3CWaitForVRPreparationU3Ed__66_System_IDisposable_Dispose_m5490BA5AB8662E4178645EA3F755E64B9B077266,
	U3CWaitForVRPreparationU3Ed__66_MoveNext_mCA451A1C7C47A420465B31D6CB316F6958D32F42,
	U3CWaitForVRPreparationU3Ed__66_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD036D6150E98DC02DE3A8F510FDC7A8FDAFD89F0,
	U3CWaitForVRPreparationU3Ed__66_System_Collections_IEnumerator_Reset_m5F772293B065B07DF676A82107C0BBB0A5F92428,
	U3CWaitForVRPreparationU3Ed__66_System_Collections_IEnumerator_get_Current_m52AC34BA7D5DFF18B219A7BD2CD4EBE0BDF352B3,
	U3CQueryAssemblyRelationshipsU3Ed__77__ctor_m63FF621B2605D1CE12644713FAD955EF8AF70741,
	U3CQueryAssemblyRelationshipsU3Ed__77_System_IDisposable_Dispose_mF250A1D07A074FFBD2179196CB4C4A9A5D39F144,
	U3CQueryAssemblyRelationshipsU3Ed__77_MoveNext_m68B7BEE9C9852EC055960456DEB832C85417BDA4,
	U3CQueryAssemblyRelationshipsU3Ed__77_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m14ACC263AD0586D408DAF65D36ECC65EC0301CBA,
	U3CQueryAssemblyRelationshipsU3Ed__77_System_Collections_IEnumerator_Reset_m37782519971FBCD9FBD71AF51DF5E8441868B9B2,
	U3CQueryAssemblyRelationshipsU3Ed__77_System_Collections_IEnumerator_get_Current_m6A7ABA42EC552ED61B2FA04C827210B6B6B1887D,
	U3CExecuteNextAssemblyStepCoroutineU3Ed__81__ctor_m5316EDF961735308B4F4DE6657C8705AE634486C,
	U3CExecuteNextAssemblyStepCoroutineU3Ed__81_System_IDisposable_Dispose_mFA8525A8DD6ABC5567010D9ABD1A101F54B7A5FD,
	U3CExecuteNextAssemblyStepCoroutineU3Ed__81_MoveNext_m75E73CA60ABC40F906CA9BD24676B7375D36AE37,
	U3CExecuteNextAssemblyStepCoroutineU3Ed__81_U3CU3Em__Finally1_m0AC89C812FC7EBC2D1CE5369314AE31F4AEFB13A,
	U3CExecuteNextAssemblyStepCoroutineU3Ed__81_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m795F6DC87ECFB5D7634AB43CA5B8A81967CED80B,
	U3CExecuteNextAssemblyStepCoroutineU3Ed__81_System_Collections_IEnumerator_Reset_m90271590611B26E1C589AA1B03B223B7125CFD80,
	U3CExecuteNextAssemblyStepCoroutineU3Ed__81_System_Collections_IEnumerator_get_Current_m5AA9A1AA8D9E4AE14CECA949F637D68E2690EE69,
	U3CReplayAssemblyStepCoroutineU3Ed__82__ctor_m683C4A2977ACE95F8A4B3212D8B74DCDFDF9985F,
	U3CReplayAssemblyStepCoroutineU3Ed__82_System_IDisposable_Dispose_m5C94E4437FC7601FD1A3EB687903BC868F3D84FB,
	U3CReplayAssemblyStepCoroutineU3Ed__82_MoveNext_mEEF55F7F8BE7B275D426DB98664325A9C0750FFA,
	U3CReplayAssemblyStepCoroutineU3Ed__82_U3CU3Em__Finally1_m4D496C152DE3BBA6D1A114155AB7325053288EE7,
	U3CReplayAssemblyStepCoroutineU3Ed__82_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m0D0A92E990BE7D4B853008F555BF55C1C0DA9914,
	U3CReplayAssemblyStepCoroutineU3Ed__82_System_Collections_IEnumerator_Reset_mE6F070F38D905A98889F0349BD79FADA66F63596,
	U3CReplayAssemblyStepCoroutineU3Ed__82_System_Collections_IEnumerator_get_Current_mCEBE7A15C471402777371677167DE1FC168AD000,
	U3CInstallFastenerU3Ed__84__ctor_m4FD9BF41A9BFE32F2D0B379420BF7D3B47AC6532,
	U3CInstallFastenerU3Ed__84_System_IDisposable_Dispose_m415B97167EC0304AE3FAEA6F4A8F5EC96B942B9E,
	U3CInstallFastenerU3Ed__84_MoveNext_m83532E4BE67AD72FF1A99FF40711360D039B7E40,
	U3CInstallFastenerU3Ed__84_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m86C08B0FEEBD5B379113B97BD31D19E8D1184862,
	U3CInstallFastenerU3Ed__84_System_Collections_IEnumerator_Reset_m6B5303B72F2DA1B747CE3A057C9BE9D5E73A69EF,
	U3CInstallFastenerU3Ed__84_System_Collections_IEnumerator_get_Current_mB6CB41312E67EFED9789A3AABB5CD51B452A4B45,
	Neo4jAssemblyUI_Start_mCDBF12C4FCBB62A6578482CB3130B39E429DB8B1,
	Neo4jAssemblyUI_InitializeUI_m05FCB4604C7F9AFC19E8C254447704E0A83F8158,
	Neo4jAssemblyUI_UpdateStatusText_m073B99500339696C19FF9D540DFD383B45EFD509,
	Neo4jAssemblyUI_UpdateSelectedPartText_m42F4B327A9CDE05D64706788A964637867EE1C9F,
	Neo4jAssemblyUI_UpdateStepsCountText_m767BFA50F60CDD8717A5CB624DA986A4C484D3F5,
	Neo4jAssemblyUI_OnNextStepButtonClicked_m89A46D40A824A84DB2D5BCED0694AF59297F74C9,
	Neo4jAssemblyUI_OnResetButtonClicked_mC352298942248A3EAB6C13E668BDD2A640DEF32E,
	Neo4jAssemblyUI_OnAutoPlayToggleChanged_mD6E10ECFDD2336CE79F14D364972F22ECEE14BD5,
	Neo4jAssemblyUI_StartAutoPlay_m14DC99B95C55D759A7C8C9F09391D105DB4E4161,
	Neo4jAssemblyUI_StopAutoPlay_mEA3969A97E2AE15517BDDCF3CABA44AAD0B221B8,
	Neo4jAssemblyUI_AutoPlayCoroutine_mEC3062D30DE1321E94E76FB8A224816EB264C6B1,
	Neo4jAssemblyUI_EnableNextStepButton_m5F22F1526B195F1CACBF8E394D3D4E706C77E18F,
	Neo4jAssemblyUI_EnableReplayButton_mE8BAB509106ECCB7533C84A7402E6BF5A4E25846,
	Neo4jAssemblyUI_OnReplayButtonClicked_m59F31CA63E639C3FDD53F42CCAA3E3CA030769A8,
	Neo4jAssemblyUI_OnSpeedSliderChanged_m92803AE875B80617418DF329FB59CC4816F9AF59,
	Neo4jAssemblyUI_UpdateSpeedValueText_m204D5414571DCA126E0FC211D05C7309FF849FA8,
	Neo4jAssemblyUI_OnPartSelected_m24FF709F553104F87BF3472EEBD5E7B4BC484A21,
	Neo4jAssemblyUI_OnAssemblyStepsLoaded_mF3A4DDE5C3A0C4605A57BBFF65655881EA4EA879,
	Neo4jAssemblyUI_OnStepExecuted_m5B708537395770CEA2BA9E44A16EA6F625EB5CF3,
	Neo4jAssemblyUI_OnAssemblyCompleted_m5C7B8529034EF917149CDA74A7F93D781A7FBA88,
	Neo4jAssemblyUI__ctor_m6F3E7FB649ADDE8690BC38471688F5EBBA39A66C,
	U3CAutoPlayCoroutineU3Ed__25__ctor_mB81CF56B677430F174AEC46D49483F3A5010388E,
	U3CAutoPlayCoroutineU3Ed__25_System_IDisposable_Dispose_m23D8CD4DED386C38ED7E2EA7F9A79E4C141A13D3,
	U3CAutoPlayCoroutineU3Ed__25_MoveNext_mA9B99C152698F51E9517394AE85A0813E335A301,
	U3CAutoPlayCoroutineU3Ed__25_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m57C1D39FF1804321046B4A2BB2CEBAC609EA2EF1,
	U3CAutoPlayCoroutineU3Ed__25_System_Collections_IEnumerator_Reset_m8A4F92BE6F420A837C0CB7F05B4D398CDEBB8575,
	U3CAutoPlayCoroutineU3Ed__25_System_Collections_IEnumerator_get_Current_mB7BE77491FD1D8719447C92AB86E7EBE8F87A174,
	Neo4jConnector_ExecuteQuery_mEAAA9076CCEACB71D39D0CB2B2C435F2482E902A,
	Neo4jConnector_get_DatabaseUrl_m818FFB7AB8FBCCBF2B20198871C4BAF4F3A87406,
	Neo4jConnector_get_DatabaseUsername_m3A33077D70412DDA80A989E4ED3A80EA0988AADC,
	Neo4jConnector_get_DatabasePassword_m5FDE24B2B8E1ACF7487AFCEC1B3C77EE026C0AE9,
	Neo4jConnector_TestConnection_m35B27310317D2B22ADA87DE18797F6680A670975,
	Neo4jConnector_QueryConnections_m20F0AB449482A817B28427DA4182C5AF63780ADA,
	Neo4jConnector__ctor_m3A334A82D94E2CCDF167E43D7A2F762E6BCC9934,
	U3CExecuteQueryU3Ed__3__ctor_mF382C62277E30FCD9C5529C0F149ACDBD2ED51B3,
	U3CExecuteQueryU3Ed__3_System_IDisposable_Dispose_m987DD58EE3222A4AF70C7FE9CC9D6EA214487C59,
	U3CExecuteQueryU3Ed__3_MoveNext_m6C2962BA6BFD5B1A97CA394F19EF50DC6C6F71AE,
	U3CExecuteQueryU3Ed__3_U3CU3Em__Finally1_mCDA4EE30E34C7960A59D93912BDF64C1EABE8927,
	U3CExecuteQueryU3Ed__3_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA0A3756E3BDFC5451D48651394FD8F5B729CD492,
	U3CExecuteQueryU3Ed__3_System_Collections_IEnumerator_Reset_m20E488C05406BAFE351959C407556F69D5EE09D5,
	U3CExecuteQueryU3Ed__3_System_Collections_IEnumerator_get_Current_m40360A3B3B6ADB0982FB0CCB874DB747A142F5D3,
	U3CU3Ec__DisplayClass10_0__ctor_m2D6F0E70743D756A75C9B0210FDF2B01DCEA797C,
	U3CU3Ec__DisplayClass10_0_U3CTestConnectionU3Eb__0_mD98388D4B0828C0E8454B39CE13470D8E20D9B3D,
	U3CTestConnectionU3Ed__10__ctor_m04FE15BC317A8298369BEAD645E40BFD404767D4,
	U3CTestConnectionU3Ed__10_System_IDisposable_Dispose_mCA99EF51488D04A2E93023FCC08B2DA489A05EE7,
	U3CTestConnectionU3Ed__10_MoveNext_m96A27E3B5C8CB1E4819A172195EB3FA0F8607C4A,
	U3CTestConnectionU3Ed__10_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB6722C108C3DF5120114EF33DABBB98028A477A0,
	U3CTestConnectionU3Ed__10_System_Collections_IEnumerator_Reset_m47CA6D10F214B3C95F0C47E3385A560B639FE44C,
	U3CTestConnectionU3Ed__10_System_Collections_IEnumerator_get_Current_mA687283225AABB53C7277F963BE5FF3EC2F763E4,
	U3CU3Ec__DisplayClass11_0__ctor_m71B8992D0FB0926ACE415E53A4684A2DF6714732,
	U3CU3Ec__DisplayClass11_0_U3CQueryConnectionsU3Eb__0_mB885CD288657C90B46973ED4A83370E2EB43F1E1,
	U3CQueryConnectionsU3Ed__11__ctor_m4901A0BADFEAAD19E68A799040D9219B08A784B8,
	U3CQueryConnectionsU3Ed__11_System_IDisposable_Dispose_m9B6A9EAFA1DF81B82CA4736BF8C5CDDAEDEE53C4,
	U3CQueryConnectionsU3Ed__11_MoveNext_m9C9706538C675FFC6BBAFF18A15E02B14F0F87B0,
	U3CQueryConnectionsU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2AE7E242201CF1333C7E094F5478EF48E1FF84FE,
	U3CQueryConnectionsU3Ed__11_System_Collections_IEnumerator_Reset_mEC7096B0DC3ECFCBD13C0E0172E69C12B2AA13B1,
	U3CQueryConnectionsU3Ed__11_System_Collections_IEnumerator_get_Current_mF188E3532A258CACD997C4E0CEDA25586E84D3CA,
	PICODeploymentManager_Awake_m398A9B609C4E8098A44BA316F9B6F9F4892CF687,
	PICODeploymentManager_Start_m513829B8D2BF62E541D3FE0AF705EFCB2F5E488D,
	PICODeploymentManager_ConfigureEnvironment_mC5E0B52F460FB39BAD2CC61A7755B3D764B2BBF5,
	PICODeploymentManager_DetectPlatform_m4BCBE611C15930811EC55D0CA3898BB30BA2AD25,
	PICODeploymentManager_FindComponents_m2AC3FB0DA8784A5D6C521CA5D0B039EB3432E492,
	PICODeploymentManager_ConfigureForPICO_m756046194F9532F301AE2B2855F2C7F73B88F6E8,
	PICODeploymentManager_ConfigureForDevelopment_mCC48CBBFF4897FCFF273D76E47EB9086CE94E527,
	PICODeploymentManager_OptimizeForPICO_m590101ECBEA7FA00F93B945F73D89115837764F1,
	PICODeploymentManager_SetPrivateField_m782293148798ED51D338230F031D468CA3A51006,
	PICODeploymentManager_ValidateConfiguration_mA06D72971650E2CF927865E2D89F9DB4CD379578,
	PICODeploymentManager_LogCurrentConfiguration_m4654CFA8462DDCE883E072EA523387DD7490DB03,
	PICODeploymentManager_SwitchToPICOMode_mB6A30668470129DBAB3854E37345B0191D21710E,
	PICODeploymentManager_SwitchToDevelopmentMode_mE31EA7C035E616FB8C204469B7448EA4AB2B2C6A,
	PICODeploymentManager_get_IsPICODeployment_m499D3B221CB2EBFE1FD38A93A875C6EDA71F9859,
	PICODeploymentManager_GetConfigurationInfo_m991D723DCB734ECFB9CD217B0DA8F099D88B31E7,
	PICODeploymentManager__ctor_m96ABD81A1A0A99CA26934178D14E45362E75D80F,
	PICOVRInputAdapter_Start_m871A5B4EFAE0F6D3DF7460A683D070FAE7D3FD17,
	PICOVRInputAdapter_Update_m6BB22EC20ED960A5D818DF991517B25085452F68,
	PICOVRInputAdapter_InitializePICOInput_m9A4DCCE6BDE9CAD0161EDA915C8376B99B1AA324,
	PICOVRInputAdapter_HandlePICOInput_mF3940DD2E6CBE4CAB8899C35BD271933AADF8AB6,
	PICOVRInputAdapter_HandleControllerInput_mDDB099B089A49BC4ED1E51132DACC9A74B001DEA,
	PICOVRInputAdapter_OnTriggerPressed_mE549CF40673C509C9D9E9A81D05FCA1ABF6D7C26,
	PICOVRInputAdapter_OnPrimaryButtonPressed_m5B191F23E444682348C83342EE77E5F7733CBED5,
	PICOVRInputAdapter_OnSecondaryButtonPressed_mBE6FE162441EBEF8CBC7258253688BDC42639150,
	PICOVRInputAdapter_OnMenuButtonPressed_m07CD6C45894DC25455589BFB78C4C75BEB70A195,
	PICOVRInputAdapter_HandleFallbackInput_m725E2F706B69D03843CCECD9CACE628846D07366,
	PICOVRInputAdapter_ProvideTactileFeedback_mA106D6E843A8B64A4B8F1A1713D176EADB2F9B00,
	PICOVRInputAdapter_ShowInputMapping_mD80922B53B9A1B8A9923F45FB7350B8F658E45B4,
	PICOVRInputAdapter_TestAllVRFunctions_m422DC00A07F0828D9B4715651B75700B74E40E08,
	PICOVRInputAdapter_TestSequence_m1F1934F4900216A04B1000AB2543D2B8A6A6DE48,
	PICOVRInputAdapter_SetPICOInputEnabled_m3997B8537C9BDB14CE2F8835E6FAABDC20DC34C5,
	PICOVRInputAdapter_OnDestroy_mC342C42272011A4436B1CF27A9119CDB0CC2EF8B,
	PICOVRInputAdapter__ctor_m7A5E3D1662C082337811679E8795E15FB3B0202F,
	U3CTestSequenceU3Ed__20__ctor_mA9F3034EE53E73484D7BA64BB19DC98ABC050D63,
	U3CTestSequenceU3Ed__20_System_IDisposable_Dispose_mF196E225B0C9F0EEBC722201DDB2CDF6B71C37D5,
	U3CTestSequenceU3Ed__20_MoveNext_mEA6FF436BB67B5643100C293E7DED82F37B5536B,
	U3CTestSequenceU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8B4EF2C347B47C55F065B2B5F2B97A7A2DA0B431,
	U3CTestSequenceU3Ed__20_System_Collections_IEnumerator_Reset_mC495B7926E34C34C35A2DDE558F9160DCA50CB06,
	U3CTestSequenceU3Ed__20_System_Collections_IEnumerator_get_Current_mB9626F117F50976F9A521FDB5F507F5DD724EE7F,
	SimpleAssemblyDataReceiver_Start_mB44AE545C79D458750DBEE53F78418DC34163055,
	SimpleAssemblyDataReceiver_ReceiveSimpleAssemblyStep_m751135B0629BE3E50283B250FE3E9D6BA623B337,
	SimpleAssemblyDataReceiver_ReceiveAssemblyStepWithScrew_m87F12585CCFD4136F3B9DE8A367A6B051BD831B7,
	SimpleAssemblyDataReceiver_TryAddStepToController_m89E596EAEFA6294F5C076400486EDBB1B423C77B,
	SimpleAssemblyDataReceiver_ClearAllSteps_m817241E6B6F8B229EF35A686EBDD8283CDA50F9D,
	SimpleAssemblyDataReceiver_StartAssemblyAnimation_mD334AECAA1620754F45521B94BCE7D035CD8F42E,
	SimpleAssemblyDataReceiver_GetSystemStatus_mE12B7F1DC369CC97FF75AA09133188F5E2548636,
	SimpleAssemblyDataReceiver_ReceiveAssemblyDataJSON_m67F383928F0E66CA5A0DD51CB8C534C62DA78C8F,
	SimpleAssemblyDataReceiver_ReceiveAssemblyDataArray_m5E561D92167351D9352518004CAA3C5008D735F1,
	SimpleAssemblyDataReceiver_ParseAndReceiveStep_m0372DAFEAEAD2350244888876E8FBA058EEA1B0A,
	SimpleAssemblyDataReceiver_TestSimpleAssembly_mE5581D0D3574FD6F73497D42A0EF5614434C4B7B,
	SimpleAssemblyDataReceiver_TestScrewAssembly_m259B3BD72BE9EBAF72CE540C8D0D482021D92726,
	SimpleAssemblyDataReceiver_TestMultipleAssembly_m6243B7111800784F8D077E28386A821F4744ED16,
	SimpleAssemblyDataReceiver_TestClearSteps_mC8257389399356339D993907E42103EA4D4731DA,
	SimpleAssemblyDataReceiver_TestStartAnimation_m5023F5E501A2FE7524A3FDF4E5B9C11656E91A5B,
	SimpleAssemblyDataReceiver__ctor_mCA7A0DCFEE31FF8AE677EF481BD4960C1B118BF6,
	SimpleExternalDataReceiver_Start_mECA24017984741ED3C4CF74C3C90DFDE417E2417,
	SimpleExternalDataReceiver_ReceiveAssemblyData_m7AF8687829EA89619C310A2227EBE18FB25688CF,
	SimpleExternalDataReceiver_ReceiveSimpleAssemblyStep_mE520344B7689530EB470781513F8A325343BE3D4,
	SimpleExternalDataReceiver_ReceiveAssemblyStepWithFastener_m1984DFABC245B69573A8A4A7E2032CD82CA99F9D,
	SimpleExternalDataReceiver_LoadTestData_m0FC21159F061F075440F99AAF799A2649056FCD6,
	SimpleExternalDataReceiver_CreateSampleAssemblyData_m2108FA0D0233FBA46269FE7F1124445B43A90E3D,
	SimpleExternalDataReceiver_TestSimpleAssemblyStep_m63592CCDBFE2ABD91516C20F4458EE3B5F03C1B2,
	SimpleExternalDataReceiver_TestScrewAssemblyStep_m09B2C7220E1CEA8C5CD5C6C5C394B59291B06166,
	SimpleExternalDataReceiver_ClearAssemblySteps_m05F35556FEC69C68310A9F7240ABA243154F2B88,
	SimpleExternalDataReceiver_IsReadyToReceiveData_m5B34E4D6E78899F1FA1686BE5716E45FDFAB7821,
	SimpleExternalDataReceiver_GetAssemblyStatus_m8BF8F36E42416F776DE765767DD5BBC2A3A7F508,
	SimpleExternalDataReceiver_Update_mABB95B818A82D5CBA18DEFB12504B29CB06F99CB,
	SimpleExternalDataReceiver__ctor_m430FD562BDA65E920723B815DF9F5E4F8992225D,
	Test_Start_mA902842AF55C0A063D71F22B280F28BF0FB01497,
	Test_Update_m68AE52707CAA861E0DBDC29971974A5BF62D82D1,
	Test_OnDrawGizmos_m6481F8828A0F683CCAE82CCB2C6CB8A94F05E6E8,
	Test__ctor_mB84DF4A3888723C395E76E3879FDFB8AA1EFEDCB,
	VRAssemblyDebugger_Start_m88057DC24FD49105662F4E7E745676D3B7362827,
	VRAssemblyDebugger_Update_mEE80CE3E4E82FFB2C7D1BBD866F3561C6A3C577F,
	VRAssemblyDebugger_FindComponents_m9609506EED6196374D2A6551B8361EF71DBBE20E,
	VRAssemblyDebugger_ValidateSetup_m1200B28D916E56DA6D6A94CD18B92BDA862F7CF5,
	VRAssemblyDebugger_LogSystemStatus_m67504E1AA45FE48557C8AEE8E518A2B406845D26,
	VRAssemblyDebugger_HandleDebugInput_m95DC4AEC014F5083F4C6B3667F9181BB3D68087A,
	VRAssemblyDebugger_TestPositioning_mE4BE9287A0C26F70480A633E0AA84F01AD8EF095,
	VRAssemblyDebugger_TestCameraBasedPositioning_m8805EAF07380827CDF88D5AAF4C1A997FCED85AD,
	VRAssemblyDebugger_GetVRSimulatorCamera_m42AE5CF74DEF231BDA5DFC445E0C78DD7894017F,
	VRAssemblyDebugger_TestOrientation_m8F0A58E8070694652BF410D17CD1ECAC13FFB120,
	VRAssemblyDebugger_ShowCurrentCameraInfo_mACE249A98EA7F9D20B8A794EF3EAFB5A9E85DBBF,
	VRAssemblyDebugger_ShowTargetAssemblyPointInfo_mDA6C48E266723BDE26C51399073099CF693FCAF4,
	VRAssemblyDebugger_TestOnlyOrientationStep_m8B8B5D222D95300345C45D99402858AD873CAD66,
	VRAssemblyDebugger_TestSpecificAssemblyPointOrientation_m5CF4EB0F31A75F6F7B1B4D25A1D628F0C74760B4,
	VRAssemblyDebugger_VerifyAssemblyPointOrientation_mA1EF2FC7FA25374B28C61701E6D87DA824571E65,
	VRAssemblyDebugger_DiagnoseOrientationIssue_mAFFF4A3D6D5A704B1940C563634D21D0A245B0FC,
	VRAssemblyDebugger_ResetPosition_m1DEA543BFBA73DA26F0BAE298036516BB32A3A33,
	VRAssemblyDebugger_ToggleDebugMode_mF30A6483F7D73F8DE00EA69AD570FEE3CA1AC22B,
	VRAssemblyDebugger_ForceInitializePositioner_mA640224DBD231DE04B6D914DF72CF66D2705AA1C,
	VRAssemblyDebugger_GetPrivateField_m25929B441736549ED4A599156FF67034D765AEA4,
	VRAssemblyDebugger_SetRelativePosition_m708C810BBC71BCD7A7CB44F87BC5916D45BD4B4E,
	VRAssemblyDebugger_EditorGUIStyle_m35445EEFA87748810503E4CB9FEE18A444015057,
	VRAssemblyDebugger__ctor_m846C941FB4DC221B0C49AE40AEA08D325BDF0136,
	U3CTestOnlyOrientationStepU3Ed__28__ctor_m9B662B94B759AC3B9F552E18AB7AB013101B1AB1,
	U3CTestOnlyOrientationStepU3Ed__28_System_IDisposable_Dispose_m3E2C3C2CD915EEF1D58B5DB881A5002DD759BF0B,
	U3CTestOnlyOrientationStepU3Ed__28_MoveNext_m78AF374949E36ABCB83706E8894069978470B4B5,
	U3CTestOnlyOrientationStepU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m44B4CCBEEF7749175E732B612ABBD49F0C0CFEFC,
	U3CTestOnlyOrientationStepU3Ed__28_System_Collections_IEnumerator_Reset_m3590A923984CE409A45AF837D1B56CDCA672A6C8,
	U3CTestOnlyOrientationStepU3Ed__28_System_Collections_IEnumerator_get_Current_m230BEBBF16972C25F2A2FA0D950A3C34AD46C0D5,
	U3CTestSpecificAssemblyPointOrientationU3Ed__29__ctor_m5A6F84491B8F67D391E2BFA2D4B975587423C7DA,
	U3CTestSpecificAssemblyPointOrientationU3Ed__29_System_IDisposable_Dispose_m9F953536DBFB57E85D98A21E9440BEBB7535B17E,
	U3CTestSpecificAssemblyPointOrientationU3Ed__29_MoveNext_m68332CED94D7FA13E233246364AEFF987BB7F35F,
	U3CTestSpecificAssemblyPointOrientationU3Ed__29_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8D12D60712C0259F9A0F546EA7BD12623ECA146F,
	U3CTestSpecificAssemblyPointOrientationU3Ed__29_System_Collections_IEnumerator_Reset_mF04B89F437DADBE1B555DF45F4471373FE045AFF,
	U3CTestSpecificAssemblyPointOrientationU3Ed__29_System_Collections_IEnumerator_get_Current_m70C8B298768F8D3616C6993EADE553B9C97E9A7D,
	VRAssemblyInputManager_Start_mA071D9867E841360986E8C25E0B0689565C15B51,
	VRAssemblyInputManager_Update_m5F5EFC6D39C57E239CEFA69E578E9789DBEB4A5D,
	VRAssemblyInputManager_HandleUserInput_mD95622AC11C59557AAD9693685AC4E4AF0B9F9B6,
	VRAssemblyInputManager_TriggerViewAdjustment_mCF5B8656FFFF2F4313D81F47D48EF3D824D3AEEA,
	VRAssemblyInputManager_SetInputEnabled_mBAFD23791634AB7C79B70274AC68511F8DB47B56,
	VRAssemblyInputManager_SetAdjustViewKey_m4CD9D688CC342CB065496781B0263907C71D3415,
	VRAssemblyInputManager_IsInputEnabled_mB36B4B78547223AC46C3B29212BBAD8AD8EDFDA6,
	VRAssemblyInputManager_GetAdjustViewKey_m1F671ABA14E0F0F762D8DF3431A60F6ECE65FB0A,
	VRAssemblyInputManager_ShowConfigInfo_mAE157D5244A6CAA8CB37FD237DABCD39A11815F8,
	VRAssemblyInputManager_ManualTriggerViewAdjustment_mBA2B0E3CB91C06DF5386F52DEF8259F0E30B2699,
	VRAssemblyInputManager__ctor_mE112945EA6B078E0A6C7847779FC55D210937E1C,
	VRAssemblyManager_Start_mDA576639DB8A0362297780138D4F3BC36920A994,
	VRAssemblyManager_InitializeVRAssembly_m61B42336F65A0C38B9B418AC8570C935F1DA2467,
	VRAssemblyManager_ValidateComponents_m0F445CC8D29914F78350C91C215AC8A0702AFE15,
	VRAssemblyManager_RegisterEvents_m2955263D52A07D862808B447F67DB13A10D1CC3A,
	VRAssemblyManager_StartVRAssembly_mDD774F0335B0D92CC78ECD48E28FE47DF484BFE2,
	VRAssemblyManager_VRAssemblySequence_m4F60D5FAF4CA6701136808FE51BD120DB956FBCF,
	VRAssemblyManager_OnAssemblyStepStart_m68CEC5F770F53F8920BC948047D761031F8C6D56,
	VRAssemblyManager_HandleVRAssemblyStep_m0230321D5CC43CA35079CDA265EE3A58A81F0DFB,
	VRAssemblyManager_get_IsVRPreparationComplete_mB97875686DCB650689742F75CD81C51FD8346F74,
	VRAssemblyManager_WaitForVRPreparationComplete_mDB066982391FD0FFCF4B8D5740CA3D5A23705A48,
	VRAssemblyManager_OnAssemblyStepEnd_mAB514E9B1D4F5C641513091A2E6EA1B88EE73021,
	VRAssemblyManager_ShouldProvideGuidance_m31AA3A4E040B7D4DDD7282F100EE48617A63248B,
	VRAssemblyManager_CalculateOptimalViewingPosition_m60BD83EDB1B475046C19BB370054F21C9EB514DD,
	VRAssemblyManager_WaitForUserConfirmation_mE0967F620CDABE033D93E2E06F8235D0CF79EE9D,
	VRAssemblyManager_SetVRMode_m569524A5A1C7422D5C9B7186A742C0AD971D1016,
	VRAssemblyManager_SetAutoPositioning_mBE16B86A3539819DDBE0A4C6B7CC7E923714E3F8,
	VRAssemblyManager_SetPreviewEnabled_m74C5E6007111F93F5D1AEAD155144F1A35815495,
	VRAssemblyManager_SetGuidanceEnabled_m8EF37827F65350446E0B3A23699BB276AECF6E1E,
	VRAssemblyManager_EmergencyStop_m81E3F4576F8183D2094D9B11DBFB734B9E2E9614,
	VRAssemblyManager_get_IsVRActive_m7DAF896FB9B21197C36955BD5E67E4700AFD03DB,
	VRAssemblyManager_Update_m7BD02B95DCF357D82EFFB9FC7EE883C46CA96266,
	VRAssemblyManager_OnDestroy_mC7E461C5726252E563A6CB849A40DF1AA8D2C26D,
	VRAssemblyManager__ctor_mB8DFC25C0B600A60B3AE7F237FFAEF2A280163CB,
	U3CVRAssemblySequenceU3Ed__20__ctor_m15C1BA205A65889F3B1AA4687FA21CF3CBDB15E5,
	U3CVRAssemblySequenceU3Ed__20_System_IDisposable_Dispose_m4E450CF9D5A7DAA93754195466037BBA4332AC7A,
	U3CVRAssemblySequenceU3Ed__20_MoveNext_m94504F439833F8C405887E9B22DCA8D74C74F110,
	U3CVRAssemblySequenceU3Ed__20_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7C9ADD12540525BB4E1E21A8EA0298FA27BDD0EE,
	U3CVRAssemblySequenceU3Ed__20_System_Collections_IEnumerator_Reset_m25E4812F67F15F87525A091CFC2800258B5EC185,
	U3CVRAssemblySequenceU3Ed__20_System_Collections_IEnumerator_get_Current_m37BD81E157F96F2B0D238706E54D60333BA4FF8D,
	U3CHandleVRAssemblyStepU3Ed__22__ctor_m5B8BD705CB5456122031EAC852D509B48AFC7BDC,
	U3CHandleVRAssemblyStepU3Ed__22_System_IDisposable_Dispose_mC252729B34F64E9BE86FD0F29859A4342EEFFFC3,
	U3CHandleVRAssemblyStepU3Ed__22_MoveNext_mC4148A804FBEEDFE908BBDC1A5E0812BC93B3A44,
	U3CHandleVRAssemblyStepU3Ed__22_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m91CB3B7FBE56222FB39BB88518933448D3CE5638,
	U3CHandleVRAssemblyStepU3Ed__22_System_Collections_IEnumerator_Reset_m616ED41E4B560E64832BE9DAC1166B4A548B9A79,
	U3CHandleVRAssemblyStepU3Ed__22_System_Collections_IEnumerator_get_Current_mFAF826A344407DA9BFFA9CFF231756CF4BDF86D6,
	U3CWaitForVRPreparationCompleteU3Ed__25__ctor_mB6081A3F963A624E483D6E2CB20EB43B42D6D259,
	U3CWaitForVRPreparationCompleteU3Ed__25_System_IDisposable_Dispose_m14D28E34281E8A0D815BD510A4BE3EEDC50BD334,
	U3CWaitForVRPreparationCompleteU3Ed__25_MoveNext_m974547382090787B2AC4948000C97135A3D19717,
	U3CWaitForVRPreparationCompleteU3Ed__25_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m87E49B1C17012F82B835A84F695CCE93669C23C9,
	U3CWaitForVRPreparationCompleteU3Ed__25_System_Collections_IEnumerator_Reset_m0DE129C8A70A9F4C2EC4EE4010E1BDE0D735630F,
	U3CWaitForVRPreparationCompleteU3Ed__25_System_Collections_IEnumerator_get_Current_m9AF6C3D510AEDD059A65F4808C80017060DA52A6,
	U3CWaitForUserConfirmationU3Ed__29__ctor_m828B8C111734364C413EEA80DBAE7818B6693AAB,
	U3CWaitForUserConfirmationU3Ed__29_System_IDisposable_Dispose_m8B19E83C5666C051EB8ABA41181E537E72D7852C,
	U3CWaitForUserConfirmationU3Ed__29_MoveNext_m9A99851260094FB73B76C0B38B120393B41E223D,
	U3CWaitForUserConfirmationU3Ed__29_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mE2C104580BEDF91066B06522A1CC39F5FF5DE8B9,
	U3CWaitForUserConfirmationU3Ed__29_System_Collections_IEnumerator_Reset_m20CAE4230AA07AF2942969D9850D2EF181B074DA,
	U3CWaitForUserConfirmationU3Ed__29_System_Collections_IEnumerator_get_Current_m9F68820E433A8DAA48D59E06547A2A4C41734743,
	VRAssemblyOrientationHelper_CalculateOrientationToCamera_mF47564ED969D38B30467970E816E9B3173192493,
	VRAssemblyOrientationHelper_CalculateOptimalOrientationToCamera_m049E4AF65711CD58D2666CFFCCBD2AB9CBAA8642,
	VRAssemblyOrientationHelper_TestAssemblyFaceOrientation_mEC996FE985F0125881DC009D65528AA547AC7695,
	VRAssemblyOrientationHelper_ApplyOrientationToAssemblyRoot_mFB1217FF274605584C302693443A8DE70B8395AD,
	VRAssemblyOrientationHelper__ctor_mE949CDCE7B99F7F2DA680CBF98BE4940A8722DAF,
	VRAssemblyPositioner_Start_mFFF6952551522C27AD0B0E56278CFC8B8941FD26,
	VRAssemblyPositioner_InitializePositioner_m9124E7DB33BAAF7E4A553CB33D0D19AACAFFA47A,
	VRAssemblyPositioner_FindAssemblyRoot_m76F73E3280D092430590A3C5935EA6906082FC54,
	VRAssemblyPositioner_FindCommonParent_mFD993971AE6B55D04A2F1BB88F3BFF82BE71DBF5,
	VRAssemblyPositioner_FindCommonAncestor_m8BF526F0357F659DBA58046175FF095B320458B7,
	VRAssemblyPositioner_CalculateOptimalAssemblyPosition_mCCCA0B80E9FB0D9CBC24BA71FAE44CDC2E9C39C0,
	VRAssemblyPositioner_CalculateOptimalPositionForAssemblyPoint_mF76630F9A037F2F91553F2827D908FA293BDDBEB,
	VRAssemblyPositioner_CalculateOptimalAssemblyRotation_m1B4D78A138191B6394B7C26C3C2D0A7E4FF01FF2,
	VRAssemblyPositioner_CalculateSimpleOrientationToCamera_mB325A81E6266A102E64FD0399CA9E1600FFE81F3,
	VRAssemblyPositioner_AlignMountPointToCameraByRotatingAssemblyRoot_m4C5C316E1F8245C13EC6254EA57E8036216077DB,
	VRAssemblyPositioner_AdjustViewForUser_mC79C505C188EA75655EABB206EF0FA4C2E151118,
	VRAssemblyPositioner_AdjustOrientationForAssemblyStep_m7CB1A2538FBC14BE0B4DAFCF6F0279783B6B0D6F,
	VRAssemblyPositioner_FindAssemblyPartByName_mFC89B480A960886DB31C7EE3AF53E4813A11E33A,
	VRAssemblyPositioner_GetCurrentCamera_mCFCABE3ADCC01A83A7D5AA9293E9268E4383E727,
	VRAssemblyPositioner_CalculateConfigurableOrientationToCamera_mA0A6EE3AFD791D09A5C27FF83B58706DC252B755,
	VRAssemblyPositioner_MoveAssemblyToOptimalPosition_m64DD6F7BC72200DCB2B5D1495437C304CF1BF1FA,
	VRAssemblyPositioner_AdjustOrientationForAssemblyStep_m893501C12F225D2ADF3A2AA5A69285FBB35B833E,
	VRAssemblyPositioner_AdjustOrientationForAssemblyStepSimple_m45A85A5F4AEE0991EED1A32389FA0BC4C9C5C4A5,
	VRAssemblyPositioner_RotateAssemblyToOrientation_m903DDB21A7FB97B74763E9CD47A81045A9985342,
	VRAssemblyPositioner_RestoreOriginalPosition_m229244D2F6DC89EE58101C4E71A089FF5CC5EBAB,
	VRAssemblyPositioner_MoveAssemblyToPosition_m3265AEF75C780D7AF67AF754063FC623CFD9D381,
	VRAssemblyPositioner_OnAssemblyStart_m1CC06174648066EB18079253DAEC0CF93F6CDCE4,
	VRAssemblyPositioner_OnAssemblyStepStart_mB2FDD80F3FF3212DA2D1DAB197AACFAC04C4DC54,
	VRAssemblyPositioner_OnAssemblyEnd_m05C00CAB697658AA0CA5145709B5828DD36B3807,
	VRAssemblyPositioner_OnDrawGizmos_mD45B6543C4D4D7B39E5FB7C31F5FEF7B7D814AFE,
	VRAssemblyPositioner__ctor_mBDA40D1EDDAD06C5DF5C442F6384186127C9F720,
	U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21__ctor_m0D0B0C195FCC2C55AA072ECC3862628B281A9D11,
	U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_System_IDisposable_Dispose_mD275EFBB76EF555B83760CB9046C83B871785FBB,
	U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_MoveNext_mC27FF8AD8F88F10E3E52A291E183C5A0CA66D378,
	U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m72870BED84F078D5D9CEF398B90449A8118FB154,
	U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_System_Collections_IEnumerator_Reset_mBD2FD28D6BB83701D6B287AF9D6FD9EBDB1744FE,
	U3CAlignMountPointToCameraByRotatingAssemblyRootU3Ed__21_System_Collections_IEnumerator_get_Current_m6D3579E96F47F1CA0C3D4FADF6932540CE116777,
	U3CAdjustViewForUserU3Ed__22__ctor_m006678A83626B4118E9B48C8834C283A399CF69A,
	U3CAdjustViewForUserU3Ed__22_System_IDisposable_Dispose_mB593F54BDCD8AC0E1CC9500E2D7D43EE916CCA8E,
	U3CAdjustViewForUserU3Ed__22_MoveNext_mF5D76945924B72DD55643866906B3FAAC8665913,
	U3CAdjustViewForUserU3Ed__22_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9525B598EB0788482F6E660F0DD6398E62D3A450,
	U3CAdjustViewForUserU3Ed__22_System_Collections_IEnumerator_Reset_m17778E32EB826F2FBC94DD0D4442326D189DACAE,
	U3CAdjustViewForUserU3Ed__22_System_Collections_IEnumerator_get_Current_m1A8EEC0A5344E81D4FB3DABC780690563123AFC5,
	U3CAdjustOrientationForAssemblyStepU3Ed__23__ctor_m75AC51E639A3509D82202FAF8ECCEAC579740E26,
	U3CAdjustOrientationForAssemblyStepU3Ed__23_System_IDisposable_Dispose_mB6CBC1270D197E10FCCB502F48CBCDE46D7EE528,
	U3CAdjustOrientationForAssemblyStepU3Ed__23_MoveNext_m2A0113A7AD49513801B6DC2923BA555882E62C7E,
	U3CAdjustOrientationForAssemblyStepU3Ed__23_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m68E2F9F06421CDDC956139C23A66C6226F60D96A,
	U3CAdjustOrientationForAssemblyStepU3Ed__23_System_Collections_IEnumerator_Reset_m27AF98F6C2E32EA7C3DB046B287D21D4BD916D91,
	U3CAdjustOrientationForAssemblyStepU3Ed__23_System_Collections_IEnumerator_get_Current_mC92DDC079FD5FA257BE79FF918079A209B053A5F,
	U3CMoveAssemblyToOptimalPositionU3Ed__27__ctor_mF2989CE76E89429FECCFD292F46546426A0BD51D,
	U3CMoveAssemblyToOptimalPositionU3Ed__27_System_IDisposable_Dispose_m040CC09EF1C1C55E0A1429C1C40AA6D4ACD18992,
	U3CMoveAssemblyToOptimalPositionU3Ed__27_MoveNext_m5C84A6F53876BCAAB8462568964A8CD5B9959E1B,
	U3CMoveAssemblyToOptimalPositionU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC25F303E68D8FD213AD91A8AB6A1664B9F46AC1F,
	U3CMoveAssemblyToOptimalPositionU3Ed__27_System_Collections_IEnumerator_Reset_m65FF3DC73365597E0EF08CB82AB70FE8FBCF9E2E,
	U3CMoveAssemblyToOptimalPositionU3Ed__27_System_Collections_IEnumerator_get_Current_m029D021F3BBF7BAFD5E3F6877BF280E47EE6737D,
	U3CAdjustOrientationForAssemblyStepU3Ed__28__ctor_m478B426DA67564F70769C7A8DCC77DE206C9296B,
	U3CAdjustOrientationForAssemblyStepU3Ed__28_System_IDisposable_Dispose_mCF1920AC9A0761E522D9FFBE517B830A0E6E09EA,
	U3CAdjustOrientationForAssemblyStepU3Ed__28_MoveNext_m7F684594D13601CDB11CD33A337A026CCFCBE4EF,
	U3CAdjustOrientationForAssemblyStepU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mA6FDFBF40E74D2CF45C6C8AA05D54FE498D0CA7D,
	U3CAdjustOrientationForAssemblyStepU3Ed__28_System_Collections_IEnumerator_Reset_m08D72A9C82C45E47C81AD2A5F18EECE757317490,
	U3CAdjustOrientationForAssemblyStepU3Ed__28_System_Collections_IEnumerator_get_Current_m7D7EC06E9FAFA81FF34F504DD9D2F105C59CCF5F,
	U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29__ctor_mADF6F2936523932BC402AD3721A098844C8419B5,
	U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_System_IDisposable_Dispose_mAD796BF9E9853F1DC3634FABD1C54A8918968F96,
	U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_MoveNext_mDC6ACD100243A12AC9AD74433CA7052F8C161322,
	U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m686C03651D73FBCE812B60EBC0AA79BB8C76707B,
	U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_System_Collections_IEnumerator_Reset_m45A1412C3E752EC244BA907D757D5351F56AB467,
	U3CAdjustOrientationForAssemblyStepSimpleU3Ed__29_System_Collections_IEnumerator_get_Current_m88D91229D28C156D57D2358A3771A1C0DDFEA2DD,
	U3CRotateAssemblyToOrientationU3Ed__30__ctor_m7B8DAFB80E21B1B53DEBFBD384F9BF86AAA0F147,
	U3CRotateAssemblyToOrientationU3Ed__30_System_IDisposable_Dispose_m939C4A22BD5B47EDB386FCB36DB15CA9103F11BF,
	U3CRotateAssemblyToOrientationU3Ed__30_MoveNext_m89840F02335F25724577553BB0F98938DC5396A7,
	U3CRotateAssemblyToOrientationU3Ed__30_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9957A50D7FFE8FCBF787F150F08B29E8C9A12C3E,
	U3CRotateAssemblyToOrientationU3Ed__30_System_Collections_IEnumerator_Reset_mA3B3F7E80BA89970F06B335F2DEF61DC5382F552,
	U3CRotateAssemblyToOrientationU3Ed__30_System_Collections_IEnumerator_get_Current_mAF40BAC1D918D12C264ED6A163C0D7FE04467025,
	U3CRestoreOriginalPositionU3Ed__31__ctor_m06EFB24057F46B530E96E42095C5E2362D416F07,
	U3CRestoreOriginalPositionU3Ed__31_System_IDisposable_Dispose_m2A69E59EC0674E66CE7BEDA074798713FF27CD37,
	U3CRestoreOriginalPositionU3Ed__31_MoveNext_m80D2FA6C72C39A0A2F83614CCBF460D87D756AF0,
	U3CRestoreOriginalPositionU3Ed__31_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m85C10E5BD35459696CD125F61ABB83EDF4E4FCD4,
	U3CRestoreOriginalPositionU3Ed__31_System_Collections_IEnumerator_Reset_m32B03686DB82B27D976FD318D1F224ED9B619BAA,
	U3CRestoreOriginalPositionU3Ed__31_System_Collections_IEnumerator_get_Current_m84B7DA2C6FBE436E8E986594089D5AF24D864FBB,
	U3CMoveAssemblyToPositionU3Ed__32__ctor_mF47F7D46AF4B998252FC4221BD01E1F5A76D08DE,
	U3CMoveAssemblyToPositionU3Ed__32_System_IDisposable_Dispose_mFAD0EF442D75C85F4B3AB09EC97831B6A2A7A86D,
	U3CMoveAssemblyToPositionU3Ed__32_MoveNext_m92BD3A350F9DBD14408C6194EF811A8929866C41,
	U3CMoveAssemblyToPositionU3Ed__32_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7ED8174506B440633059D41488C7870D6F75D53D,
	U3CMoveAssemblyToPositionU3Ed__32_System_Collections_IEnumerator_Reset_mE6810E007FA9A72966ABA8F23CC76723DABE127B,
	U3CMoveAssemblyToPositionU3Ed__32_System_Collections_IEnumerator_get_Current_m8E567E0EFA0301F933E85C3639F4E49431E7D0A0,
	VRAssemblyPreview_Start_mED668E216CD1BF3238274F38FB189F86749D67D4,
	VRAssemblyPreview_InitializePreview_mDFF4DA69A92C3C640A66CE0485E05AB370ACF703,
	VRAssemblyPreview_FindVRControllerTransform_mAFFDB44E12C416D7C144012FDDFF63D731B7CA0E,
	VRAssemblyPreview_CreateTransparentPreviewMaterial_mE497510E391F2D45B8B5792CB5CDBEC2F665AAB4,
	VRAssemblyPreview_CreateAssemblyPreview_mD5A7DCD6F4AF1B208E0BF28B8723C3D1FA161067,
	VRAssemblyPreview_CreatePreviewPart_m4CFA31DC394DC9C74857A26BB7A4B0AC5C4803CE,
	VRAssemblyPreview_RemoveUnnecessaryComponents_m563B40629121E9F57F2F9371B41AF992EDFC9260,
	VRAssemblyPreview_ApplyPreviewMaterial_m3384BA6F9D4268A6D2E2F156094CB725B3729724,
	VRAssemblyPreview_CalculatePreviewPosition_m82C1750BB96AB66FB89ABFCE5AB1B9040D9F3061,
	VRAssemblyPreview_StartAutoRotation_m94C67A95F47BAE5E3A0E4592258CBF29F5FE9E7E,
	VRAssemblyPreview_AutoRotatePreview_m36965A4615E1D102B093C8ADF3BC80401625343F,
	VRAssemblyPreview_StopAutoRotation_m997BA0A1A20DF95CD892A377F0BAF4DFCC6AA4D7,
	VRAssemblyPreview_HighlightPart_mED7B531F23C5CA7259EA2981C38FD8BCF70EBE26,
	VRAssemblyPreview_ApplyHighlightMaterial_mF7D0CB782613B0A798605598ACBD2A84D4AFC6D7,
	VRAssemblyPreview_UpdatePreviewLabel_mDDACD5208B82FAA0FB2E138005D6F656C62C7709,
	VRAssemblyPreview_ClearPreview_m4C94B8C33901A3F0ACF93AC9431C3D0C97BC840D,
	VRAssemblyPreview_SetPreviewVisible_m748E67C64D66E4A7E23E38DAE1FC705D6C0937A7,
	VRAssemblyPreview_Update_m08B0D97AC62AB87B51812F77552F9325944AA62B,
	VRAssemblyPreview_CreateStepPreview_m0C8F9ACC6A716360C2C05840DE6C831A88F7107F,
	VRAssemblyPreview_OnAssemblyStepEnd_mEDDD449732FEF79B41FB9C25EC54F56ABEE5BCA9,
	VRAssemblyPreview_OnDestroy_mF713FB4825A4B3722D6293AE530BD67592A58C42,
	VRAssemblyPreview__ctor_m64135A87EB8C83AEEAE0361EDA4D9B23F679ED71,
	U3CAutoRotatePreviewU3Ed__27__ctor_mA2DA3759648FF5E918D549B13DF0350BBCE2697D,
	U3CAutoRotatePreviewU3Ed__27_System_IDisposable_Dispose_m677C9339AED9A4AE57E1E5B145D36F20BE8D224D,
	U3CAutoRotatePreviewU3Ed__27_MoveNext_m1D3C137D39CCB4D977B7264816433D6110B35BA6,
	U3CAutoRotatePreviewU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m8F92CD0884F8448279C86F50188C9BE659B93638,
	U3CAutoRotatePreviewU3Ed__27_System_Collections_IEnumerator_Reset_m80E579BC4C4F965234CD610773047C40FF719F5B,
	U3CAutoRotatePreviewU3Ed__27_System_Collections_IEnumerator_get_Current_mC019D63C87CE3E229D18D6E48A62D08E6295B529,
	VRAssemblyStepManager_Start_m72D6B27C41F36AFE890F7B37056B3ABE02FB78DF,
	VRAssemblyStepManager_PrepareAssemblyStep_mC750835B08545AACBDA36F421C977E18BAAD81B3,
	VRAssemblyStepManager_PrepareAssemblyStep_m28CA76769B5FF321AC452C60258EF6280381AD09,
	VRAssemblyStepManager_SetAutoAdjustOrientation_m1C11B0B943C64F84C28303644EB74763F19EB50D,
	VRAssemblyStepManager_SetDefaultOrientationAxis_mCAA7E4A6207F9682DE1C53B60B6CD085A602DDC8,
	VRAssemblyStepManager_SetDelayBeforeAnimation_m35708D0C4A5C72490B86021A7A3D43CD8A9C2ED2,
	VRAssemblyStepManager_GetCurrentConfig_m441D5C2FFB1F9895ECC368223B9A20EB199CF5B4,
	VRAssemblyStepManager_ShowConfigInfo_m1DA49B5FF6DAAF830F30F97ADB163D06F47BFA53,
	VRAssemblyStepManager_TestPrepareAssemblyStep_m7A7F21A20B8C077A72E132692860D1F721C252BA,
	VRAssemblyStepManager__ctor_m1653B6CD8E821AE2C032729D4A23943C122C08D6,
	U3CPrepareAssemblyStepU3Ed__6__ctor_mE7075AE756C42E03383ABE6827CB451A2D02007E,
	U3CPrepareAssemblyStepU3Ed__6_System_IDisposable_Dispose_m02AC0D6BD3E36876681CE177F5F02859A0965580,
	U3CPrepareAssemblyStepU3Ed__6_MoveNext_m39E448C266CCAEC4CA68E7F237C5BE944CC5D689,
	U3CPrepareAssemblyStepU3Ed__6_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC153B0CD5B76ABE23D9B62949080B20BF56F057A,
	U3CPrepareAssemblyStepU3Ed__6_System_Collections_IEnumerator_Reset_m982FF1DAFB00EAC4B78A875EB23D6EF877AF136A,
	U3CPrepareAssemblyStepU3Ed__6_System_Collections_IEnumerator_get_Current_mE2B078D3C6BF8242FF514A3A11A69C6B77DA7044,
	U3CPrepareAssemblyStepU3Ed__7__ctor_m4CA782BAFF0957AC85A3C7BE7DD61D4DCDC43B1C,
	U3CPrepareAssemblyStepU3Ed__7_System_IDisposable_Dispose_mC4B289C29A307D5B6CB38523CED148B3B53D0929,
	U3CPrepareAssemblyStepU3Ed__7_MoveNext_m12D236973EB066E62EDC93CCF70130E0D2F6A039,
	U3CPrepareAssemblyStepU3Ed__7_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m28B749622E1767C8347A58468F1438DD18C21ED8,
	U3CPrepareAssemblyStepU3Ed__7_System_Collections_IEnumerator_Reset_mD1C68AF192BFAEF82C17C4654A2C796FEE9207FF,
	U3CPrepareAssemblyStepU3Ed__7_System_Collections_IEnumerator_get_Current_m688E40F03DE06B7464051366E169A2447EC14E95,
	VRInputManager_add_OnPartSelected_m5C3070EA0886F30681B28F729759E44945503631,
	VRInputManager_remove_OnPartSelected_mE5B70AD10CA05140511B584946DAFFF157DCD00D,
	VRInputManager_add_OnNextStepRequested_m2AED815A84BB90AED60FA2ADBDB9BE01850C5128,
	VRInputManager_remove_OnNextStepRequested_mDC65B60380B65586D84B0AE6486F1D699B3CCA4D,
	VRInputManager_add_OnResetRequested_m1C1B61023DB588C405AB2FDE0C8D89E0358BFB36,
	VRInputManager_remove_OnResetRequested_mF0F568735A599F592A11B3E3F8BA09AD20DFE065,
	VRInputManager_add_OnReplayRequested_m35F9442BEBC3708327BB67E82AC1AD933A6FBC44,
	VRInputManager_remove_OnReplayRequested_mC7C89A4ED9CEB8D3AAE425FE22981108BFAC25FB,
	VRInputManager_Start_mEDCE09DC068C2A7676C78F6BC85EC184FA2F8CBA,
	VRInputManager_Update_m7292786F9775C1AD6BE6BFFEADDE8C0779FE6607,
	VRInputManager_InitializeVRInput_m014D294AD01E88C13A296A9320ACB776A9DC9344,
	VRInputManager_HandleVRInput_m2945939741DC28FDAC21E86422F0B328B023B04C,
	VRInputManager_HandleFallbackInput_m78EE683656B46278F07043FF6E01BE34654F7C7D,
	VRInputManager_HandleControllerInput_m9A3A2BC6C842ED3485AE7F633C8A8E78C25B190B,
	VRInputManager_HandlePartSelection_m31C91C31BE9209E526B636D4994BDB56852DA231,
	VRInputManager_SelectPart_m89687CF7C30B69579DB6ABB6E87502C2F5DC3A84,
	VRInputManager_HandleMenuInput_mB767C0F4AE4A0A081A0052ED5A3FCDCE13CFC4AE,
	VRInputManager_TriggerHapticFeedback_m914B5A1B27AD7FF6AF0C69D147C977C5726379DD,
	VRInputManager_GetSelectedPart_mBC791B9E65C1B02EEDE52A970D2F851CFE3DF8D9,
	VRInputManager_GetActiveInteractor_m881063E8E4974B5F28149A27A909EB288536E84E,
	VRInputManager_SetInteractionLayerMask_m40C012C138859D2A930F2F5F2E07F89E110A5FB3,
	VRInputManager_SetHapticFeedback_m84F62D86924CB54B7922FE04A2494933842313C0,
	VRInputManager_TriggerNextStep_m9D4087D1FAEDD1B31D3FDFF307777D43E3D32B6A,
	VRInputManager_TriggerReset_m6078607B188F8C35D0D12E816ED32BA5784C4491,
	VRInputManager_TriggerReplay_m0E79D4ECB60ACD5797AA0702C8E7215EFFB8E19D,
	VRInputManager_OnDestroy_m83E19ABE0AEB47324FE2F3526117192E9ADEBC1E,
	VRInputManager__ctor_m1A0FBE8ECE1AEDE907F35442F5634CBAD5A9A546,
	VRSimulator_Start_mD3E50DCE68A0531D10D9B0890923C8AD19E2E42C,
	VRSimulator_Update_m9327341FF575FC0397A7F26DEFC504F1461B811C,
	VRSimulator_InitializeVRSimulator_m08012E382E2E65F7682B95B68FE09F3962808B6D,
	VRSimulator_CreateControllerSimulators_m9515B3DF2549CD6DDAE872426520560CFA1CEB72,
	VRSimulator_CreateControllerVisual_mBAC43F3090A61B7A16382DE351F9D71F0D24A0AC,
	VRSimulator_HandleVRSimulation_mD04E9283442A2697F29376F31694486BF0EB0CCB,
	VRSimulator_HandleHeadMovement_m89D0144BFCC732DFD5F8B431B5E3F405D888BAC6,
	VRSimulator_HandleHeadRotation_m0BAB512A2F654EC70E7E3CCC1E1AA85EC1FC8386,
	VRSimulator_HandleControllerSimulation_m491E50B6FA22C44B18B5B46C8E54DF3D364402D7,
	VRSimulator_ClampToVRBounds_m346D4C4FAFE0D69D220BC73482F7A0A41850FE21,
	VRSimulator_GetVRCamera_m3DFB20A1FE02C03F6739D3946B00529BBDE6B986,
	VRSimulator_GetControllerTransform_m1C1EA8393D526442039540E5A31909D6F980738B,
	VRSimulator_ResetVRPosition_m62A6C813FFAAF87F3CE95C74B625011322F2B72A,
	VRSimulator_ToggleVRSimulation_mE3CB63194F7580CF49D2D50207B6D93767B2F87D,
	VRSimulator_SetUserHeight_m5EFF0E65925D0C9D487C436847E13B11075A9AF2,
	VRSimulator_OnDrawGizmos_m68FA100330BD0AD428DCBC8DF436445AAC857061,
	VRSimulator_OnGUI_m7DD4CA686C0C1D92F4ADD9CFAB89EF3DF332486D,
	VRSimulator_EditorGUIStyle_m678D64F4A00ABC95B7E5EC7074CCD8FF99135800,
	VRSimulator__ctor_m7D11F1D5F20D30258E8D21FC8DC28D626C180C31,
	VRSystemDebugger_Start_mF4D9B8DF58E6FD69789E8744925FE6ED5D5F5CA5,
	VRSystemDebugger_Update_m6FC02EC2526241D70C8CDD5450C225273B55CAFF,
	VRSystemDebugger_InitializeDebugger_m00E152AC644E455ED18EB8DB4775542780A1EDBB,
	VRSystemDebugger_CheckVRSystemStatus_m1DA318C9E0EE0C15FF2EF29267550B7ECD8AA38E,
	VRSystemDebugger_TestPositionAdjustment_mFC5A3F99CFF09B56C21BEBBA3628982E72CE988E,
	VRSystemDebugger_TestPreviewFunction_mD94D3AB447BE71F612E62305D67F31E55F28697C,
	VRSystemDebugger_TestGuidanceFunction_m4AB64ED31A52BF0BD75AD70E226A915951FE55EC,
	VRSystemDebugger_ResetVRFunctions_m1D0D51958DBE77D2F4AC514BE83FCEF6F771E8EA,
	VRSystemDebugger_ToggleDebugMode_m3A95E2806E7651F21F95183F1C151B0465F6DC07,
	VRSystemDebugger_UpdateDebugInfo_m4C02BAD48D1BB8392EB8760B9B95FF82FF8AE3C6,
	VRSystemDebugger_UpdateStatusText_m6A59DD8A2FB1777300016A3ACDF79E8C237B45F4,
	VRSystemDebugger_CreateDebugUI_m0892B8F0361D6914BCF045FBACB4D6CEE94969BA,
	VRSystemDebugger_CreateDebugButton_mD9CD27AF600971B2A1605A75897024DDA83B7E3B,
	VRSystemDebugger_ShowHelp_m51B9B9CF53B8D9C5477FC883F2F8E3EAA211F581,
	VRSystemDebugger_OnGUI_m6406E1978F3D72C836069F62E7483C38D821E569,
	VRSystemDebugger__ctor_m13E74A955BFAC048E5CF671518C3BAD1287A56F1,
	U3CU3Ec__DisplayClass28_0__ctor_mF171D5F5DC51408281799B284EFE62F2752CA5B2,
	U3CU3Ec__DisplayClass28_0_U3CCreateDebugButtonU3Eb__0_mBBC5C967B55B62451B75696B24EA7C15F61D6F82,
	VRUserGuidance_Start_m2C26A5944C138C6EB9D103D0240709E0409E0DA2,
	VRUserGuidance_InitializeGuidance_m111726F47FA961DDD0D503F7BEF48608DAFA63BE,
	VRUserGuidance_CreateDefaultGuidanceElements_m0AFE2F40630B4E4C06B0A43CAEBCC18278A63355,
	VRUserGuidance_CreateDefaultArrow_mB91943240AA44802B4DE5AAC6BDF2608DDAB7F1A,
	VRUserGuidance_CreateDefaultFloorIndicator_mA45E47D528074ADD517DB3183F94D65E07BCF562,
	VRUserGuidance_CreateGlowMaterial_m495FF8571A8E0BDEDE67C09F3DEFEE7E4E63E3B5,
	VRUserGuidance_GuideUserToOptimalPosition_mC79A19264F09BC6BCEF153E0CC101D8126AD4F79,
	VRUserGuidance_CreateFloorIndicator_m49DCB1913C2AE2108BAE7A25987E44FA61C959AF,
	VRUserGuidance_CreateDirectionArrow_m49DDE0E2C40D72DA7D005ABC9FF2056B175AAEE9,
	VRUserGuidance_GuidanceAnimationCoroutine_m3B8D4FD6BF51349E6E5987D0C6D7886C4F5E7E96,
	VRUserGuidance_WaitForUserPosition_m25A0AD600F78E2A12D386122D5318A9D40137B3D,
	VRUserGuidance_HighlightAssemblyArea_m8F23D0E3987831C7C6E06FA181BB7786A9F02894,
	VRUserGuidance_HighlightCoroutine_m266744DF7580311A49F5D1B52BF4068ACBDD071C,
	VRUserGuidance_ShowGuidanceMessage_mC6CD298B1CF141D06FAC053F87B89F593B523BD6,
	VRUserGuidance_HideGuidanceMessage_mA90A93C1FC49799AE7431E38E0AE7BED37A8D988,
	VRUserGuidance_PlayVoiceGuidance_mF07DE289690B3B6D0D078DF1852F39F7601022E6,
	VRUserGuidance_StopCurrentGuidance_mBD5A25783D8D2B21DBE870B5C3F799BF2BF3CCE0,
	VRUserGuidance_GuideForAssemblyStep_mE182EFF483C7A344834439E154DCF1DBD145356D,
	VRUserGuidance_GuideToBackView_mCA2A1CF9AA944EACA2EC5D7C50E05A92ACD8CCFC,
	VRUserGuidance_OnDestroy_m7BF288A0E12B51E4B715EA2C23E9E3A46FC00E68,
	VRUserGuidance__ctor_m9F6E3AFCB34E45E926F7ABAA32999A0B63591C9A,
	U3CGuideUserToOptimalPositionU3Ed__24__ctor_m1383C15052E14C47D77A90AAE7423BAF6E590D0D,
	U3CGuideUserToOptimalPositionU3Ed__24_System_IDisposable_Dispose_mEB8F8FF06B92E65B2167F12F36A9A618A6CD0F04,
	U3CGuideUserToOptimalPositionU3Ed__24_MoveNext_m81B10167D70F0B0245058FFBEFB3F5883B2DE8EC,
	U3CGuideUserToOptimalPositionU3Ed__24_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mB81D6CD4EF4AAF55F4E784DFC0C52997104AB727,
	U3CGuideUserToOptimalPositionU3Ed__24_System_Collections_IEnumerator_Reset_m6AE49C806C145AF9AF743741D6813856FB20E436,
	U3CGuideUserToOptimalPositionU3Ed__24_System_Collections_IEnumerator_get_Current_m05DA6C906386DC256EF371B3DC563E92E1BCF2D3,
	U3CGuidanceAnimationCoroutineU3Ed__27__ctor_m41C3F2CB62ADDF6131E88E9D478201915A12D72B,
	U3CGuidanceAnimationCoroutineU3Ed__27_System_IDisposable_Dispose_m3E698714563A7AB7FE0851759B017FFBE7571D8E,
	U3CGuidanceAnimationCoroutineU3Ed__27_MoveNext_mE7639A38359A58563755BBA6746487EF891FB5C3,
	U3CGuidanceAnimationCoroutineU3Ed__27_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mCB8CA2A7022D7B8157A2FFBEF84D936A7F2D1218,
	U3CGuidanceAnimationCoroutineU3Ed__27_System_Collections_IEnumerator_Reset_m22B7737FA1DE2913391936766502E491C30B285A,
	U3CGuidanceAnimationCoroutineU3Ed__27_System_Collections_IEnumerator_get_Current_m398E02A923F8C6F2634C18740BF345F5E32A34AF,
	U3CWaitForUserPositionU3Ed__28__ctor_m0BECD0EF5CF852EADD90DF2D5232960ACDB06907,
	U3CWaitForUserPositionU3Ed__28_System_IDisposable_Dispose_m945E3580AB7FA758B5E6D66ACFFEEDDA3B144734,
	U3CWaitForUserPositionU3Ed__28_MoveNext_m69132C50DDCD695621E34947CA71BCC29A972CAC,
	U3CWaitForUserPositionU3Ed__28_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m1D1F574D4CBF44FC28EFCDBD7EFE663E1C7F8B3C,
	U3CWaitForUserPositionU3Ed__28_System_Collections_IEnumerator_Reset_mE174ADE025D5BDE79204F7F552D442729054DD6D,
	U3CWaitForUserPositionU3Ed__28_System_Collections_IEnumerator_get_Current_mA076668FDC8A7D8845AD524FFEEC97CB07220914,
	U3CHighlightCoroutineU3Ed__30__ctor_m4801E771114AF06F0F1DF0AD483BF36DF6F73DFA,
	U3CHighlightCoroutineU3Ed__30_System_IDisposable_Dispose_m1355F4D82BABB77482C78A8B8D47CB9F54A4E42D,
	U3CHighlightCoroutineU3Ed__30_MoveNext_mA4F4F16B04D4A5CA97C46D94E1C65F499625AA85,
	U3CHighlightCoroutineU3Ed__30_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m58CAB82258849F24804D018BC8BDAFD7891FD733,
	U3CHighlightCoroutineU3Ed__30_System_Collections_IEnumerator_Reset_m4541F0BC302E8F10FFF049C61292FB3086C8E04D,
	U3CHighlightCoroutineU3Ed__30_System_Collections_IEnumerator_get_Current_m7B462A3F8AD8BF9C023937252F26709B026C22CC,
	NULL,
	JSONNode_get_Item_m77F15891BEC7ED659BFBC392555178B558747AD8,
	JSONNode_set_Item_mC6F47073D979B943286B2EAB1A6D0380AFE58A09,
	JSONNode_get_Item_m466B08DF2E30B20606697EC7AE043C2791DC6768,
	JSONNode_set_Item_m045530804B67FC5E2E57E497219F27ED70CE437E,
	JSONNode_get_Value_m2A9961ACC3D4BCBB028012CD79B619DCBD82A839,
	JSONNode_set_Value_mE8CD0E68E0E2B0A716F56B0FE9B988EC2BAD773A,
	JSONNode_get_Count_m260DDA50B8AFB98F5946E54B9EADD05891A82C8B,
	JSONNode_get_IsNumber_m6B495FE576572E9FC7999740C63980BCB65AD768,
	JSONNode_get_IsString_mBDE2CAF25E51CDA450074BE9DC81D834903BA392,
	JSONNode_get_IsBoolean_m13F16853C0F6D76D0AB6B7E866923A0632C108A2,
	JSONNode_get_IsNull_m6443A7B3540D725ED3ACA0038A74CE0346A31F8D,
	JSONNode_get_IsArray_m52DCDB47E4CB2673FDCECCD3BE9DD2D90B5C948F,
	JSONNode_get_IsObject_m237FE2EA3382DD9762ED426B49F46183F5EF39AB,
	JSONNode_get_Inline_m7A5B6C07F44EFEEDD80FD72580C32C0579041F4C,
	JSONNode_set_Inline_m18362F10F03DDCD1FF29B4868C3EA793D39AE7F6,
	JSONNode_Add_mB05F1A32B54A9A1223F9AC6A6A737836FA1F4E7E,
	JSONNode_Add_mDAF96580EAF3B9FF23888A8549BED7A98439075D,
	JSONNode_Remove_mF56C4223700DF4F1D5AE12BCD69C492C2487FA59,
	JSONNode_Remove_m7B5E0BC0A29C35857D7B10857A8C52C0E3DFB615,
	JSONNode_Remove_mE2CFD05512C25BD11EA4160CAAF88B8154D9DBE5,
	JSONNode_Clear_mD9B59BDE8D07A352AB775FD4A8FB262D406EB848,
	JSONNode_Clone_mE7849A4FBD98462D93E715826B0D01DE7FC822C3,
	JSONNode_get_Children_m3E2D70DBCA2C8311F65A47B766668728392B1F89,
	JSONNode_get_DeepChildren_m891CB892AEA834980686ED760B952A86DC1E8725,
	JSONNode_HasKey_mBEF13E4AC99F2F0D76D4CD87405BB71586C4486B,
	JSONNode_GetValueOrDefault_m751E871953EBA8094B4DE73CC261C884720811F6,
	JSONNode_ToString_m4CC464630B0AEEDD82AEB6B069690949AF569345,
	JSONNode_ToString_m1F607CB90F49115510B7CF5228733578E9AD41F2,
	NULL,
	NULL,
	JSONNode_get_Linq_m8569DB478533504290D9A09ECA0DF12F116122DA,
	JSONNode_get_Keys_mC3401CC91BBD9D1166EF8EFC0C87A820FC543D1B,
	JSONNode_get_Values_mF8FB164A48A169146D00EBA3F51D4C8E380C1930,
	JSONNode_get_AsDouble_m9A8E3EC46E4545BCBFA26B99C0F013067D2F0AE4,
	JSONNode_set_AsDouble_mCDBB05BD0AE82EEF0C4842F5A9205B8F4C858015,
	JSONNode_get_AsInt_mE4A3FCC1D91362D077C2ACF418ACAB43771B1FE6,
	JSONNode_set_AsInt_m12FCF0B7E45E17EA0456AE44EFEF0C8731603F50,
	JSONNode_get_AsFloat_m0D044C1F3FC35086783A4BAF506EA96DC997D050,
	JSONNode_set_AsFloat_m55FCE24DF60B37724DACCCF0A759522B2561DE92,
	JSONNode_get_AsBool_m902380F5939671ACBBB7EFA01A48F1A082B1FD9C,
	JSONNode_set_AsBool_m6097FD196A8C7BB156125363D1C1D3EF0EB67CD3,
	JSONNode_get_AsLong_m31250905C6F4BED9B1059009E064D07D609C4C18,
	JSONNode_set_AsLong_m8D29780DEA1458A2F9C33805432DB1554950ECF4,
	JSONNode_get_AsULong_mA34C3D1DA0D3339D0B63F386867ADE3E460909DD,
	JSONNode_set_AsULong_m2BC120C5B1842E17BC0E6E5714511391DD504091,
	JSONNode_get_AsArray_m2D0890FDDA140528CAB44B1B6B3E34B26383ACC7,
	JSONNode_get_AsObject_m72F6D406BECA2FB0A24B20E0A353FDB8E409CA1B,
	JSONNode_op_Implicit_m71A2D2EECDD79DC3A3DAF6510BB2F8ED57DE6AAC,
	JSONNode_op_Implicit_m6019D30B60A2033906907488CB6236EC9A7B7B6B,
	JSONNode_op_Implicit_m098A31323C3D89615E0EBD709D83AB4F684453CF,
	JSONNode_op_Implicit_m17B71B28DE136B73EF2B97DA87BB4A4BB27332E9,
	JSONNode_op_Implicit_m4A9267CC71FC4FDD39ABAE262B7B2D334EB0FFBD,
	JSONNode_op_Implicit_m808991B4DFA11ECBF7080226CFC3069A7E6673E8,
	JSONNode_op_Implicit_m6AC3F8DDC02644CA8D85EC90758373D1B7EC4322,
	JSONNode_op_Implicit_mD9A5824FE62046D01AD966EC503DEB775B2C7482,
	JSONNode_op_Implicit_m94EF6FC36942EA4A49ABFCA42FC12BCE914990FA,
	JSONNode_op_Implicit_mD25BDDE21767954AE9D36F16948B6F77173EC2F6,
	JSONNode_op_Implicit_m64294932E998EC6A35EF99F1CD4D36BFB9A8FB1E,
	JSONNode_op_Implicit_mD5974501A6FBBD60D6E7331940440498D67A7A05,
	JSONNode_op_Implicit_mCE1B7A6233218E114687A876F778B4A1CBF22B74,
	JSONNode_op_Implicit_m29CA9621387E0DDDECCCAAB240A140A854567FDF,
	JSONNode_op_Implicit_mB9C34D74CF1854E402B4AD106AB2084169287E1E,
	JSONNode_op_Equality_mD30EBFA5F9398107FCC5CE51B05CE4CFFBCC6A8E,
	JSONNode_op_Inequality_m91693B2A4AC881F8703CC1D1050371B8EC552CF7,
	JSONNode_Equals_mE1B8A846783529B1E54786975A6A2396089A88DE,
	JSONNode_GetHashCode_m0A263555D1F0E6766A61692A7E1BC3546B2BC984,
	JSONNode_get_EscapeBuilder_mA695C85FBFBCF3863E2AC3B63821B469CC632DB1,
	JSONNode_Escape_m5C811748A36C7258315C1D2036712855F184ADDD,
	JSONNode_ParseElement_m3478B79AC164A87E0B2088067EDEC6DE146DAA56,
	JSONNode_Parse_m7198C73C509B06CD8A96576D7D2A5A125DC7D0B4,
	JSONNode__ctor_mF8F2893483161D3B7B9877B63C69063D26A5C353,
	JSONNode__cctor_m00855C5266A7EF25B6EBE62476F1FAD5C7046065,
	Enumerator_get_IsValid_mBC273331DC1699FF46BD3621AE5059A54AD98BA6,
	Enumerator__ctor_mF21239C69620D815F8CD34F022BE18E9DAF9CB10,
	Enumerator__ctor_mAC4ED0FA4B083E2652E865A41EA5C74A49478EFE,
	Enumerator_get_Current_mDE6750203413E1069D0520793D6AA0B2527CB20E,
	Enumerator_MoveNext_m238CF072385A1106BEDEFCE33BA2B0DBE999758A,
	ValueEnumerator__ctor_mCC61CE3EDCF1AC94A84E031F2E89F8054C94A015,
	ValueEnumerator__ctor_m122732DF448B45E8E82956E07AC8314C60E28C29,
	ValueEnumerator__ctor_m7BA4BAD5FEBAC4054F71575B728DC27EC4080F0A,
	ValueEnumerator_get_Current_mAA24A52FDEB7160BD268193175388EACB41B7CE2,
	ValueEnumerator_MoveNext_m5B596A2EF2FF395EDA8F5CAB97C0789498D250C9,
	ValueEnumerator_GetEnumerator_m765261287A2C0AEF757B94994826F43951387E4C,
	KeyEnumerator__ctor_m6EA81E2BED4CA5194A7306D8B324F7356E37F80A,
	KeyEnumerator__ctor_mA6338E82A9F8AA19A1744352B4FE54103AD70405,
	KeyEnumerator__ctor_m526EA1364C367B83C931F4208CDD816BD02810EA,
	KeyEnumerator_get_Current_mB4E0F33D7E23A7F365D12B3530DE7FB6B7A1F7E3,
	KeyEnumerator_MoveNext_m42FE2CEE808A7E065895BA333B7FBD2F3AEE032F,
	KeyEnumerator_GetEnumerator_mD4687B4D6D10E4D6870CBBECC680689A62A95C0B,
	LinqEnumerator__ctor_m9FD8AB1580F3D94C5C36D070DBE85E023ED36E30,
	LinqEnumerator_get_Current_m28F0BE4D9B5736F5BD79197C1895EAC1592EBAAF,
	LinqEnumerator_System_Collections_IEnumerator_get_Current_m6B6C12C7E8CD21DF513FCDCB4E88E454790B6FF0,
	LinqEnumerator_MoveNext_mCA8604B6E8D857CF16003E674048C05E29447819,
	LinqEnumerator_Dispose_m5D6A54C4B712D138739726323D5BEA50A4E12E32,
	LinqEnumerator_GetEnumerator_m4A9F0720F0C0964F91032AB8B8776F09DC70A90B,
	LinqEnumerator_Reset_m56B65E398518EF57070307FDC48069DFE37BC57B,
	LinqEnumerator_System_Collections_IEnumerable_GetEnumerator_mB63F02D713868ABF87DAB18ABFD5D832F4D805A4,
	U3Cget_ChildrenU3Ed__43__ctor_mA2E1AC1211A03DAFF45B69AF872ED71E58F4D458,
	U3Cget_ChildrenU3Ed__43_System_IDisposable_Dispose_m0C7490DE49A53AB049729E66293845681AB08395,
	U3Cget_ChildrenU3Ed__43_MoveNext_m33A56DB8F47EADE4EB91E3FBFF4D01F1CF255839,
	U3Cget_ChildrenU3Ed__43_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m85EB3E729C5EE85E2103FED7453D79C1D132C2EB,
	U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerator_Reset_m755BAC68C65681AA8266C6AC37D2308771D54067,
	U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerator_get_Current_m04BDDA2EB2EC20489BB50BDDB46313F624F90CF9,
	U3Cget_ChildrenU3Ed__43_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_m96326AFEFC6998DB0E90D15633CFE23661C21916,
	U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerable_GetEnumerator_m39BF4FF795523B96CA4FA6383244D82117D96C46,
	U3Cget_DeepChildrenU3Ed__45__ctor_m89830CB6F115E0AD956EF880354CAFBAD7AF9E5A,
	U3Cget_DeepChildrenU3Ed__45_System_IDisposable_Dispose_mCE52C471742B7A6DA19AF43E9096545012D560DD,
	U3Cget_DeepChildrenU3Ed__45_MoveNext_m644F556E82CCF23C7B91E0B0266F4716E18C2F5E,
	U3Cget_DeepChildrenU3Ed__45_U3CU3Em__Finally1_mBA31C43EB8ACB72C8A163B470D786ACB361CF740,
	U3Cget_DeepChildrenU3Ed__45_U3CU3Em__Finally2_mC829190BED7A6B48F2F4C64848495925A3C58EEE,
	U3Cget_DeepChildrenU3Ed__45_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m6E1A05C1C6A7BF9748F1768E2B2AB1B140F49983,
	U3Cget_DeepChildrenU3Ed__45_System_Collections_IEnumerator_Reset_mB10807E87C7440A590E9580E6A5B329ACCAD49E4,
	U3Cget_DeepChildrenU3Ed__45_System_Collections_IEnumerator_get_Current_m2A8CD7D70A8ACF8A362378B75EAF7B41BC9FCEF6,
	U3Cget_DeepChildrenU3Ed__45_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mAD2929E624663DCA925B762F05FCF8CDDE1FC6C8,
	U3Cget_DeepChildrenU3Ed__45_System_Collections_IEnumerable_GetEnumerator_m8BB12003DCC4402BDA35F5B5AE1B82EF7C1A4856,
	JSONArray_get_Inline_mBA0C9AEBB7420DBDFD977C0F54CC237E8F2BE3E5,
	JSONArray_set_Inline_m731089F5D0FA649ED210518DC299635A8D86A1DC,
	JSONArray_get_Tag_m360EB078D7897D6D52783B8CDA6B736D014E97BC,
	JSONArray_get_IsArray_mA7B4EF5B0128FB64ACEB7EAC66FA3522991980AF,
	JSONArray_GetEnumerator_m6AF64AE0DD2A5AAB8C0E271BF0CAB8AA1FD32E17,
	JSONArray_get_Item_m8BE9047FC512840E6A4594560EDF86BB4E0FF657,
	JSONArray_set_Item_mBCD05590C34BC589B786E753B9FE796EBA3F6725,
	JSONArray_get_Item_mE18312128B02B505BA656D7F444B05A6769710AE,
	JSONArray_set_Item_mE4E0DE5133E60AF49E46FEDAD00D2A04349C0855,
	JSONArray_get_Count_mB71218A2D8288D0665C467844F7351D301FDAFDD,
	JSONArray_Add_mD1FBE0F0FC20E7415014B7FF21939592EBB0C9A1,
	JSONArray_Remove_m79500DBD9751A04C02756470A4D22DDCF9C97FEC,
	JSONArray_Remove_m64C3EBFE3DB5BE130232769DC43000E84589E674,
	JSONArray_Clear_m86E2E8BE6493C5C555525B9935AFF9E53BB72C2B,
	JSONArray_Clone_mA05BA59E71672A88208218DF12C4E5F7A8773502,
	JSONArray_get_Children_m733AE4C5816E51E6F86441110606489A0406AA91,
	JSONArray_WriteToStringBuilder_m9F23115433028794DCAC019F82EEFD946990D994,
	JSONArray__ctor_m92FFF2DC8E1425398814F50D4B253EB459B8477F,
	U3Cget_ChildrenU3Ed__24__ctor_m4FA6CFA96B1189496D9E219499A0C05F713A6D28,
	U3Cget_ChildrenU3Ed__24_System_IDisposable_Dispose_m91E6F93E3940835795BCA9BFD783592E29BDEE5A,
	U3Cget_ChildrenU3Ed__24_MoveNext_m9C8F57C9E0722A9D843A2BA0259E7EE30778CF6B,
	U3Cget_ChildrenU3Ed__24_U3CU3Em__Finally1_m8E8730694C83B14CFFB30D810166D12563C1DFF2,
	U3Cget_ChildrenU3Ed__24_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m6958E538A455210191F2E06BA531D4AE5F0E97F0,
	U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerator_Reset_mE122AA2BA93A72C8C8733C4F7EC6A7B8CFB42FCD,
	U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerator_get_Current_m508CF18DF3857321EA1CFDC62E0406DBEF6FDF7F,
	U3Cget_ChildrenU3Ed__24_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_m7679E5F774E9512FC2DA58B2D0236A66983BC632,
	U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerable_GetEnumerator_m7593480F6CC6218E2EA7CD84ED3A56FF6274AB32,
	JSONObject_get_Inline_mCDF2154366BEFF9E547918F999E7F3C7C4865F84,
	JSONObject_set_Inline_m7F048A7565E5A53FDB610D44B7CA75A314CB7A7A,
	JSONObject_get_Tag_mD57D6BCAD1C677B88693FD508129CFAD661F4FBD,
	JSONObject_get_IsObject_m9F72861BE5A0DB2888AA3CBEC82718E08DD71E93,
	JSONObject_GetEnumerator_m8912E3D1EA302655BB5701B53EB19437238BABDA,
	JSONObject_get_Item_m219B9BA37D800A5DFEAA14E4EECA375B3565BF96,
	JSONObject_set_Item_m1AC7334DBA67D0CB6C9549B83B3FFA75CF226AEF,
	JSONObject_get_Item_m5C2EDBE7B154A3FC1CC43616C4C40255B4D95652,
	JSONObject_set_Item_mFB6E61E3FA394B7D2CA01CC957A6A253642D109B,
	JSONObject_get_Count_m9109E9A81559A9006EE160CA6A0F3291C71F2D08,
	JSONObject_Add_m25BD208A0AC0F0223FD93FBCB42785B12A6E1A18,
	JSONObject_Remove_m34280FDB4512E61F42781475E492BE98514830C9,
	JSONObject_Remove_mD1B01E22A9C1FEE83A00ECDFD8E0D8A422F8E4C2,
	JSONObject_Remove_m51B998A7997D184A1A20359D512C6B5A1B825404,
	JSONObject_Clear_m74686B9AF4B75949F959B81AAF8DE5076C60B3FE,
	JSONObject_Clone_mF3146F5687820508FD22051B23EFA20430B811C1,
	JSONObject_HasKey_m79E034D14422C265C62C6C50C8E6F8337749457E,
	JSONObject_GetValueOrDefault_m969ABBC8049DB2DF4EC53968CDF7DF45666873BC,
	JSONObject_get_Children_m03D7227DE57F0BE2977FC0436C0DE48858650B7C,
	JSONObject_WriteToStringBuilder_m931DC8805C6B8F09617958EFDAEA957751EB2EAE,
	JSONObject__ctor_m8007967452F5257DC9F5DF2B78B411BFD4B6D6AB,
	U3CU3Ec__DisplayClass21_0__ctor_m6976B4CF7F93E28364B390F81E55DAD60BB141C1,
	U3CU3Ec__DisplayClass21_0_U3CRemoveU3Eb__0_m8B35D441B276B749481FF797FC51A256A7A56105,
	U3Cget_ChildrenU3Ed__27__ctor_mC18696B4562A62E4AA0969D6399C8C0631E35DC8,
	U3Cget_ChildrenU3Ed__27_System_IDisposable_Dispose_mC5CC72D1E22DD570C8E2EB525332F70406CDB9AA,
	U3Cget_ChildrenU3Ed__27_MoveNext_mF000F683CB97030C47BF22BD34472814A0C7630C,
	U3Cget_ChildrenU3Ed__27_U3CU3Em__Finally1_mF5ECB5874D716A4939B7F1DB00D93DC58CEA824D,
	U3Cget_ChildrenU3Ed__27_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_mD5BCAEE8B6A2ADEAF8EC61432A9619287942CD66,
	U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerator_Reset_m7F54C4A2495814DE04F74FB9E9296EA2B68BFF6D,
	U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerator_get_Current_mF24C3141BA1436A87068A46004816112F281FF9E,
	U3Cget_ChildrenU3Ed__27_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mB7F1824F0A6AD34C4EFEB913F04662B64CEF262C,
	U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerable_GetEnumerator_m02800F9D77652D9E15E570729565FE79BCC2B3F8,
	JSONString_get_Tag_m68B0FF9ADDC3E203E5D60BB10639AEABACA34D44,
	JSONString_get_IsString_m933985E37AE8A887A2039A9BAC7698F083BCD6E3,
	JSONString_GetEnumerator_m1CB9E437FC8622F3FE05D0AC12024D144747E0B8,
	JSONString_get_Value_mEAD2BD372A2C517E83233BA5F6E309745AA5E9B4,
	JSONString_set_Value_mB974D9B82AB8F9FAB84DCA99B8BD4B7C1C08ED00,
	JSONString__ctor_m1DD5FB9A4147F72A0ED5F773FF82FA269241AD19,
	JSONString_Clone_m59FCBC159496A334397171CF5127205C82C30A73,
	JSONString_WriteToStringBuilder_mDF24D860FBF8E71F6F04799DD70F7700CE41D818,
	JSONString_Equals_m1C60B537E558E6DF85ACF3EF9FF43BF9A3CF5435,
	JSONString_GetHashCode_m979A74F84B4C0F45BF63D75DE1146490F743EE00,
	JSONString_Clear_m3E9CBF4AB37C6FD0011E19CA99E074FEA129FED7,
	JSONNumber_get_Tag_m7C6E217E85B6161812496B63E5D371B910AAC856,
	JSONNumber_get_IsNumber_mFABFD0C9C4905CFB34A62700A1BD335F53E4214E,
	JSONNumber_GetEnumerator_m4D13E84756AEED9FCD7EFEEE4D01187DD049C596,
	JSONNumber_get_Value_mBC5AB046D134B1E54C228C9C1C2231F8448CD56D,
	JSONNumber_set_Value_m2264762BBD76F39DDC5DF3160910A44FBEFDE54C,
	JSONNumber_get_AsDouble_m8C004121700A7E7EB2B77ED223187227E33DE60B,
	JSONNumber_set_AsDouble_m8E17AF8C0E9AE0EF6E25D86CB1B119904ADC0558,
	JSONNumber_get_AsLong_mF96069F806F51121CBFE8847D9E0D312F05986BB,
	JSONNumber_set_AsLong_m541EF4E20CD8683CA860E0B969CECF7B71E2A357,
	JSONNumber_get_AsULong_mD1EB0D23B9143C4CC1AA4BF75F17E326C08785CA,
	JSONNumber_set_AsULong_m320EA0ACC4B63183B5223CFCF0B25B8DA383C0DA,
	JSONNumber__ctor_m1CE3527102D15EBC3A183E3519895E291CAC1D90,
	JSONNumber__ctor_m39FDDE1A9EFEE9C4F2498E531D12B97AA49A1BA5,
	JSONNumber_Clone_m1C9DD94EB3011E55E840B55B4D4F3EAB63AF8A52,
	JSONNumber_WriteToStringBuilder_mD311BC3C1EE3E159C43801EB214F084E567367F2,
	JSONNumber_IsNumeric_m9039F8DA776517548A2A6BEA7377B419C0525887,
	JSONNumber_Equals_mC04BB811CCAF20E70AE696AE74ECFDF5DA888688,
	JSONNumber_GetHashCode_m976ADFE41037830524798C7E6AFE08006B5F77AD,
	JSONNumber_Clear_mEB7835A2B2D433CE017CFD91CAE974ADB27CE72C,
	JSONBool_get_Tag_m82CE84C4C89E157D4DB036B9F0745343C005C338,
	JSONBool_get_IsBoolean_m2671AE98710859611DF47E6BC58E6582C3A5B445,
	JSONBool_GetEnumerator_mA07A10A6111713F7AD09FF03D09A6028556094D9,
	JSONBool_get_Value_mBEA89869448B0B597758D5BF2A3B576CA0BB64E3,
	JSONBool_set_Value_mC960EE4083CA91D0059BE24661AFC06E131E2CFC,
	JSONBool_get_AsBool_mE04224144EAD0A9AD2F3B14BC0C68557A3BF22AC,
	JSONBool_set_AsBool_m88EDF61A5ABBFF3ECF723312852E14F3C60AE365,
	JSONBool__ctor_mBB02E388CFB96B99E84561FCFF68147F00391C58,
	JSONBool__ctor_m8CFB6AA78095EA003AB9B5EDD8932E8E0B01A1B9,
	JSONBool_Clone_m0B98A17130A9A6FCEC5A92408F551E344CB80274,
	JSONBool_WriteToStringBuilder_m82C70C80863730E8A22EE7A5B099C765F2E1D91E,
	JSONBool_Equals_m2671F40DA8F1128BA1451FE7066515C6E0C50D45,
	JSONBool_GetHashCode_mC5B59375A9EE9978A5ADD1A24ECEE3FC920836DB,
	JSONBool_Clear_m7841012AB307EA72DCFA23305AF45E45ACF7B7DE,
	JSONNull_CreateOrGet_mDC16038413CE71B027A7F9AB1546AF8666D3D3BD,
	JSONNull__ctor_m909243259F39D10FA6FEB176474DEF9C9972D76B,
	JSONNull_get_Tag_m89A7F368EA6269874235F85E43AE82254AAFD41E,
	JSONNull_get_IsNull_m1174212D6379871AC361EF06FA05DD510FC55595,
	JSONNull_GetEnumerator_m16D254C74386D1A0AB2EFD1DE0EAF409C73B7686,
	JSONNull_get_Value_mB15431220D7D0B45CE002A204DF9E070CF78DBE0,
	JSONNull_set_Value_mAF0CD2E912EF772E0892EB4ABB77294F689CF20A,
	JSONNull_get_AsBool_m6F3817CD49ED7CC10C180D31D84ED4B0151C78CE,
	JSONNull_set_AsBool_m5717BC3921B7DE0683E9160B3816628B5CBC663D,
	JSONNull_Clone_m103493F0850508FB95CCA260491BAA283658289F,
	JSONNull_Equals_m8A39CAD3A41E9584C434B90A1360C62B3E158DE6,
	JSONNull_GetHashCode_m74BE6286F06C6E7D5E35381E8BD27215117D9061,
	JSONNull_WriteToStringBuilder_mB5B78BFA6A4943319926C1B2AE93F68C7B9B5FFD,
	JSONNull__cctor_m00A365175E9F31A2842DA242EE490783F0EAC483,
	JSONLazyCreator_get_Tag_m1CB86FEA25328F1BE9CC01F6D020C9450E9F466E,
	JSONLazyCreator_GetEnumerator_m720BF0642A079A8BD44F6D650CF4D833DEF67757,
	JSONLazyCreator__ctor_m0B3625D19DDD8DBDBB45822FAABCE266FA4EE694,
	JSONLazyCreator__ctor_m02E2D630C60045F25A3AC001B7A17DF2D5D197B4,
	NULL,
	JSONLazyCreator_get_Item_m562D16AE7F1F0CACA5ED050B390B63F98EBC77B1,
	JSONLazyCreator_set_Item_m42894F9D00193BC7138C5D451E1B0BBD1BFE1084,
	JSONLazyCreator_get_Item_mF7AE3ADFBE062BF3B83FECCE0EF10F10996DE0CD,
	JSONLazyCreator_set_Item_m0107997E3B3CB75FACD86FB487C5D9416171CBEC,
	JSONLazyCreator_Add_mA8451EE34FEA0205B6BD6527AB46E5926451F49F,
	JSONLazyCreator_Add_mDC69A4E203B73054072D1575EC4CF20D95064F61,
	JSONLazyCreator_op_Equality_m46508F81FB60FE9DCA683335676093A23D59D799,
	JSONLazyCreator_op_Inequality_m06C76EEC055AE314ED6E4FE7A49719AC7ACA397D,
	JSONLazyCreator_Equals_m753939907CFDB1548B0DAAB38E4737EF17B50066,
	JSONLazyCreator_GetHashCode_m878E7AFF42AE5C43F4F643B6AEB25662491316F9,
	JSONLazyCreator_get_AsInt_mE1404FBC99CE4E8EF4ABBE0BDF661206BAC2C44D,
	JSONLazyCreator_set_AsInt_m13146E53FD6A2F7573B752BFF079E0AF6A5FAE74,
	JSONLazyCreator_get_AsFloat_m2600D4B0E1179583EFE268070C66EAC11D380E04,
	JSONLazyCreator_set_AsFloat_m9DCF79C70D4ED3728C12B709A6D95A0F0A057DE0,
	JSONLazyCreator_get_AsDouble_m41D6DF89CD7CEC00F36962068EE072D391EC0B38,
	JSONLazyCreator_set_AsDouble_mB7ABE38136DBEDA7CC9AC12A381322D6C49ADED9,
	JSONLazyCreator_get_AsLong_mFBA0000985629FA20509FA45A6A8B751C9CAC2B8,
	JSONLazyCreator_set_AsLong_mBD4640D2F347DEF793A631A44026A03D3D5D73A4,
	JSONLazyCreator_get_AsULong_m09F6B8D28F383D9A0F857339A6663B24D6AB97A2,
	JSONLazyCreator_set_AsULong_m5514AFD97B29BBA5D1A4EC80F7086929DE977A7D,
	JSONLazyCreator_get_AsBool_m7D8AF5879C2C8036916AA6B15E22CB4B80412CF4,
	JSONLazyCreator_set_AsBool_m4DB409DB959182CAA610147A51A2ECDBAFEA6092,
	JSONLazyCreator_get_AsArray_m493C069A3624597885A7B6E00C82E829A84B47C4,
	JSONLazyCreator_get_AsObject_mE01B43B261A6A56F4FCE40AB11F3AAF90B7C292D,
	JSONLazyCreator_WriteToStringBuilder_mC9975859B1C42C9F5E507E604121D10B2FB2D93D,
	JSON_Parse_mEE6C962A58074E33C05C49D74221F1852E7963CE,
};
extern void AssemblyStep_AddMountPoint_m194EA8814100C8F34D6BB078603A47010B7B707D_AdjustorThunk (void);
extern void AssemblyStep_ToString_m320E7A2E2902A63202043DA88272743C043F82EB_AdjustorThunk (void);
extern void AssemblyStep_IsValid_mF9FCF7AB253203EA0B1C0D39E09B71CE913F6A41_AdjustorThunk (void);
extern void AssemblyStep_RequiresFastener_mFD703603DDAD3E14B20E48D70E9EE9A5E3988974_AdjustorThunk (void);
extern void AssemblyStep_HasAdditionalMountPoints_m5341C821D4FE304D28E15E715126EF409BEA18E3_AdjustorThunk (void);
extern void AssemblyStepData_ToString_mE8C413BE6001E1478285B8AA28545AB3EBC65360_AdjustorThunk (void);
extern void AssemblyStep__ctor_m40EF831184A0632EF7E7D625F994BB6378CEE04E_AdjustorThunk (void);
extern void AssemblyStep__ctor_m9E10466D3C2D06685921F7B5B8A0C782FF812EFE_AdjustorThunk (void);
extern void AssemblyStep_ToString_mF97FA0F6A8BF46F54FA596AB0A2C5C7F43719B1B_AdjustorThunk (void);
extern void Enumerator_get_IsValid_mBC273331DC1699FF46BD3621AE5059A54AD98BA6_AdjustorThunk (void);
extern void Enumerator__ctor_mF21239C69620D815F8CD34F022BE18E9DAF9CB10_AdjustorThunk (void);
extern void Enumerator__ctor_mAC4ED0FA4B083E2652E865A41EA5C74A49478EFE_AdjustorThunk (void);
extern void Enumerator_get_Current_mDE6750203413E1069D0520793D6AA0B2527CB20E_AdjustorThunk (void);
extern void Enumerator_MoveNext_m238CF072385A1106BEDEFCE33BA2B0DBE999758A_AdjustorThunk (void);
extern void ValueEnumerator__ctor_mCC61CE3EDCF1AC94A84E031F2E89F8054C94A015_AdjustorThunk (void);
extern void ValueEnumerator__ctor_m122732DF448B45E8E82956E07AC8314C60E28C29_AdjustorThunk (void);
extern void ValueEnumerator__ctor_m7BA4BAD5FEBAC4054F71575B728DC27EC4080F0A_AdjustorThunk (void);
extern void ValueEnumerator_get_Current_mAA24A52FDEB7160BD268193175388EACB41B7CE2_AdjustorThunk (void);
extern void ValueEnumerator_MoveNext_m5B596A2EF2FF395EDA8F5CAB97C0789498D250C9_AdjustorThunk (void);
extern void ValueEnumerator_GetEnumerator_m765261287A2C0AEF757B94994826F43951387E4C_AdjustorThunk (void);
extern void KeyEnumerator__ctor_m6EA81E2BED4CA5194A7306D8B324F7356E37F80A_AdjustorThunk (void);
extern void KeyEnumerator__ctor_mA6338E82A9F8AA19A1744352B4FE54103AD70405_AdjustorThunk (void);
extern void KeyEnumerator__ctor_m526EA1364C367B83C931F4208CDD816BD02810EA_AdjustorThunk (void);
extern void KeyEnumerator_get_Current_mB4E0F33D7E23A7F365D12B3530DE7FB6B7A1F7E3_AdjustorThunk (void);
extern void KeyEnumerator_MoveNext_m42FE2CEE808A7E065895BA333B7FBD2F3AEE032F_AdjustorThunk (void);
extern void KeyEnumerator_GetEnumerator_mD4687B4D6D10E4D6870CBBECC680689A62A95C0B_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[26] = 
{
	{ 0x060000EE, AssemblyStep_AddMountPoint_m194EA8814100C8F34D6BB078603A47010B7B707D_AdjustorThunk },
	{ 0x060000EF, AssemblyStep_ToString_m320E7A2E2902A63202043DA88272743C043F82EB_AdjustorThunk },
	{ 0x060000F0, AssemblyStep_IsValid_mF9FCF7AB253203EA0B1C0D39E09B71CE913F6A41_AdjustorThunk },
	{ 0x060000F1, AssemblyStep_RequiresFastener_mFD703603DDAD3E14B20E48D70E9EE9A5E3988974_AdjustorThunk },
	{ 0x060000F2, AssemblyStep_HasAdditionalMountPoints_m5341C821D4FE304D28E15E715126EF409BEA18E3_AdjustorThunk },
	{ 0x060000FC, AssemblyStepData_ToString_mE8C413BE6001E1478285B8AA28545AB3EBC65360_AdjustorThunk },
	{ 0x06000153, AssemblyStep__ctor_m40EF831184A0632EF7E7D625F994BB6378CEE04E_AdjustorThunk },
	{ 0x06000154, AssemblyStep__ctor_m9E10466D3C2D06685921F7B5B8A0C782FF812EFE_AdjustorThunk },
	{ 0x06000155, AssemblyStep_ToString_mF97FA0F6A8BF46F54FA596AB0A2C5C7F43719B1B_AdjustorThunk },
	{ 0x06000399, Enumerator_get_IsValid_mBC273331DC1699FF46BD3621AE5059A54AD98BA6_AdjustorThunk },
	{ 0x0600039A, Enumerator__ctor_mF21239C69620D815F8CD34F022BE18E9DAF9CB10_AdjustorThunk },
	{ 0x0600039B, Enumerator__ctor_mAC4ED0FA4B083E2652E865A41EA5C74A49478EFE_AdjustorThunk },
	{ 0x0600039C, Enumerator_get_Current_mDE6750203413E1069D0520793D6AA0B2527CB20E_AdjustorThunk },
	{ 0x0600039D, Enumerator_MoveNext_m238CF072385A1106BEDEFCE33BA2B0DBE999758A_AdjustorThunk },
	{ 0x0600039E, ValueEnumerator__ctor_mCC61CE3EDCF1AC94A84E031F2E89F8054C94A015_AdjustorThunk },
	{ 0x0600039F, ValueEnumerator__ctor_m122732DF448B45E8E82956E07AC8314C60E28C29_AdjustorThunk },
	{ 0x060003A0, ValueEnumerator__ctor_m7BA4BAD5FEBAC4054F71575B728DC27EC4080F0A_AdjustorThunk },
	{ 0x060003A1, ValueEnumerator_get_Current_mAA24A52FDEB7160BD268193175388EACB41B7CE2_AdjustorThunk },
	{ 0x060003A2, ValueEnumerator_MoveNext_m5B596A2EF2FF395EDA8F5CAB97C0789498D250C9_AdjustorThunk },
	{ 0x060003A3, ValueEnumerator_GetEnumerator_m765261287A2C0AEF757B94994826F43951387E4C_AdjustorThunk },
	{ 0x060003A4, KeyEnumerator__ctor_m6EA81E2BED4CA5194A7306D8B324F7356E37F80A_AdjustorThunk },
	{ 0x060003A5, KeyEnumerator__ctor_mA6338E82A9F8AA19A1744352B4FE54103AD70405_AdjustorThunk },
	{ 0x060003A6, KeyEnumerator__ctor_m526EA1364C367B83C931F4208CDD816BD02810EA_AdjustorThunk },
	{ 0x060003A7, KeyEnumerator_get_Current_mB4E0F33D7E23A7F365D12B3530DE7FB6B7A1F7E3_AdjustorThunk },
	{ 0x060003A8, KeyEnumerator_MoveNext_m42FE2CEE808A7E065895BA333B7FBD2F3AEE032F_AdjustorThunk },
	{ 0x060003A9, KeyEnumerator_GetEnumerator_mD4687B4D6D10E4D6870CBBECC680689A62A95C0B_AdjustorThunk },
};
static const int32_t s_InvokerIndices[1111] = 
{
	6215,
	6215,
	6215,
	4880,
	6215,
	1840,
	6215,
	6215,
	6215,
	6215,
	6215,
	6088,
	6116,
	4507,
	383,
	2647,
	1168,
	790,
	385,
	6215,
	6215,
	6215,
	4324,
	784,
	4324,
	381,
	789,
	2662,
	6215,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	6147,
	4935,
	1577,
	1170,
	382,
	1165,
	384,
	791,
	1168,
	2662,
	379,
	1180,
	4507,
	789,
	789,
	937,
	379,
	28,
	6215,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	6215,
	4935,
	6215,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	6215,
	4935,
	6215,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	6215,
	4935,
	6215,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	6215,
	4935,
	6215,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	6215,
	4935,
	6215,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	6088,
	6215,
	6215,
	437,
	788,
	788,
	4507,
	788,
	788,
	6215,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	6088,
	6088,
	6059,
	6088,
	5994,
	6059,
	6088,
	4306,
	4312,
	4306,
	6215,
	6215,
	6215,
	1323,
	6215,
	7021,
	6524,
	6525,
	944,
	6088,
	5994,
	5994,
	5994,
	10926,
	9426,
	9804,
	9804,
	7497,
	0,
	0,
	0,
	0,
	6088,
	6088,
	5994,
	4782,
	6215,
	2044,
	4312,
	2044,
	4312,
	4312,
	2647,
	6215,
	6215,
	4782,
	6215,
	2153,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6215,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6215,
	6088,
	6215,
	6088,
	4880,
	4880,
	4880,
	4880,
	4880,
	4880,
	4880,
	4880,
	6147,
	5994,
	6147,
	4935,
	6215,
	6215,
	6215,
	6215,
	4880,
	4312,
	2647,
	4312,
	4880,
	6215,
	6215,
	6215,
	1162,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	5124,
	6215,
	6215,
	6215,
	4312,
	4880,
	6215,
	6215,
	6088,
	4327,
	6215,
	800,
	4312,
	4312,
	2042,
	2042,
	6215,
	6215,
	1840,
	6215,
	944,
	214,
	6088,
	6215,
	4880,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6215,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6215,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	6215,
	6215,
	4880,
	4880,
	2398,
	6215,
	6215,
	4782,
	6215,
	6215,
	6088,
	4782,
	4782,
	6215,
	4935,
	4935,
	4880,
	4853,
	1288,
	6215,
	6215,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	2044,
	6088,
	6088,
	6088,
	4312,
	2044,
	6215,
	4853,
	6215,
	5994,
	6215,
	6088,
	6215,
	6088,
	6215,
	4880,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	6215,
	4880,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	1355,
	6215,
	6215,
	6215,
	6215,
	5994,
	6088,
	6215,
	6215,
	6215,
	6215,
	6215,
	2647,
	4880,
	4880,
	4880,
	4880,
	6215,
	6215,
	6215,
	6215,
	6088,
	4782,
	6215,
	6215,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	6215,
	944,
	213,
	4774,
	6215,
	6215,
	6088,
	4880,
	4880,
	4880,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	2647,
	443,
	213,
	6215,
	6215,
	6215,
	6215,
	6215,
	5994,
	6088,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6088,
	6215,
	6215,
	6215,
	6088,
	6088,
	6215,
	6215,
	6215,
	6215,
	6215,
	2044,
	4987,
	6088,
	6215,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	6215,
	6215,
	6215,
	6215,
	4782,
	4853,
	5994,
	6059,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6088,
	1355,
	1162,
	5994,
	6088,
	6215,
	1577,
	2115,
	4312,
	4782,
	4782,
	4782,
	4782,
	6215,
	5994,
	6215,
	6215,
	6215,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	2070,
	2070,
	6215,
	942,
	6215,
	6215,
	6215,
	6088,
	4312,
	2044,
	6207,
	458,
	4371,
	2070,
	776,
	6088,
	1154,
	4312,
	6088,
	1179,
	6088,
	2044,
	2044,
	2048,
	6088,
	2058,
	6215,
	2647,
	6215,
	6215,
	6215,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	6215,
	6215,
	6088,
	6215,
	2044,
	2044,
	4880,
	4880,
	6207,
	6215,
	6088,
	6215,
	4880,
	4880,
	4880,
	6215,
	4782,
	6215,
	1355,
	6215,
	6215,
	6215,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	6215,
	2042,
	1154,
	4782,
	4782,
	4935,
	5979,
	6215,
	6215,
	6215,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4880,
	4880,
	4880,
	4880,
	4880,
	4880,
	4880,
	4880,
	6215,
	6215,
	6215,
	6215,
	6215,
	2647,
	4880,
	2647,
	6215,
	4880,
	6088,
	6088,
	4862,
	4782,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	2634,
	6215,
	6215,
	6215,
	6215,
	6215,
	6088,
	4301,
	6215,
	6215,
	4935,
	6215,
	6215,
	6088,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	953,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6215,
	6088,
	6088,
	6088,
	2057,
	4324,
	4324,
	6088,
	2059,
	2653,
	2045,
	4880,
	6215,
	4880,
	6215,
	2731,
	4880,
	6215,
	6215,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	0,
	4306,
	2419,
	4312,
	2647,
	6088,
	4880,
	6059,
	5994,
	5994,
	5994,
	5994,
	5994,
	5994,
	5994,
	4782,
	2647,
	4880,
	4312,
	4306,
	4312,
	6215,
	6088,
	6088,
	6088,
	3416,
	2044,
	6088,
	4306,
	0,
	0,
	6088,
	6302,
	6303,
	6011,
	4803,
	6059,
	4853,
	6147,
	4935,
	5994,
	4782,
	6060,
	4854,
	6203,
	4983,
	6088,
	6088,
	9804,
	9804,
	9795,
	9630,
	9814,
	9897,
	9800,
	9692,
	9801,
	9723,
	9818,
	10043,
	9791,
	9570,
	9754,
	8279,
	8279,
	3416,
	6059,
	10880,
	9804,
	8517,
	9804,
	6215,
	10922,
	5994,
	4586,
	4588,
	5852,
	5994,
	4586,
	4588,
	5116,
	6088,
	5994,
	6303,
	4586,
	4588,
	5116,
	6088,
	5994,
	6302,
	4880,
	5852,
	6088,
	5994,
	6215,
	6088,
	6215,
	6088,
	4853,
	6215,
	5994,
	6088,
	6215,
	6088,
	6088,
	6088,
	4853,
	6215,
	5994,
	6215,
	6215,
	6088,
	6215,
	6088,
	6088,
	6088,
	5994,
	4782,
	6059,
	5994,
	6301,
	4306,
	2419,
	4312,
	2647,
	6059,
	2647,
	4306,
	4312,
	6215,
	6088,
	6088,
	918,
	6215,
	4853,
	6215,
	5994,
	6215,
	6088,
	6215,
	6088,
	6088,
	6088,
	5994,
	4782,
	6059,
	5994,
	6301,
	4312,
	2647,
	4306,
	2419,
	6059,
	2647,
	4312,
	4306,
	4312,
	6215,
	6088,
	3416,
	2044,
	6088,
	918,
	6215,
	6215,
	3195,
	4853,
	6215,
	5994,
	6215,
	6088,
	6215,
	6088,
	6088,
	6088,
	6059,
	5994,
	6301,
	6088,
	4880,
	4880,
	6088,
	918,
	3416,
	6059,
	6215,
	6059,
	5994,
	6301,
	6088,
	4880,
	6011,
	4803,
	6060,
	4854,
	6203,
	4983,
	4803,
	4880,
	6088,
	918,
	9570,
	3416,
	6059,
	6215,
	6059,
	5994,
	6301,
	6088,
	4880,
	5994,
	4782,
	4782,
	4880,
	6088,
	918,
	3416,
	6059,
	6215,
	10880,
	6215,
	6059,
	5994,
	6301,
	6088,
	4880,
	5994,
	4782,
	6088,
	3416,
	6059,
	918,
	10922,
	6059,
	6301,
	4880,
	2647,
	0,
	4306,
	2419,
	4312,
	2647,
	4880,
	2647,
	8279,
	8279,
	3416,
	6059,
	6059,
	4853,
	6147,
	4935,
	6011,
	4803,
	6060,
	4854,
	6203,
	4983,
	5994,
	4782,
	6088,
	6088,
	918,
	9804,
};
static const Il2CppTokenRangePair s_rgctxIndices[1] = 
{
	{ 0x0600043D, { 0, 1 } },
};
extern const uint32_t g_rgctx_T_t13CA6E5FFB26617441B8E8F9AEE48D6EB182AE56;
static const Il2CppRGCTXDefinition s_rgctxValues[1] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t13CA6E5FFB26617441B8E8F9AEE48D6EB182AE56 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule;
const Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule = 
{
	"Assembly-CSharp.dll",
	1111,
	s_methodPointers,
	26,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	1,
	s_rgctxIndices,
	1,
	s_rgctxValues,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
