<libraries>
  <library
      name="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle@@:unityLibrary::release"
      jars="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\.transforms\85cf22a9d73fe541b41462a1b0d8c11c\transformed\out\jars\classes.jar;D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\.transforms\85cf22a9d73fe541b41462a1b0d8c11c\transformed\out\jars\libs\R.jar;D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\.transforms\85cf22a9d73fe541b41462a1b0d8c11c\transformed\out\jars\libs\gson-2.10.1.jar;D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\.transforms\85cf22a9d73fe541b41462a1b0d8c11c\transformed\out\jars\libs\unity-classes.jar"
      resolved="Gradle:unityLibrary:unspecified"
      folder="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\.transforms\85cf22a9d73fe541b41462a1b0d8c11c\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="__local_aars__:D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\gson-2.10.1.jar:unspecified@jar"
      jars="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\gson-2.10.1.jar"
      resolved="__local_aars__:D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\gson-2.10.1.jar:unspecified"/>
  <library
      name="__local_aars__:D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\unity-classes.jar:unspecified@jar"
      jars="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\unity-classes.jar"
      resolved="__local_aars__:D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\unity-classes.jar:unspecified"/>
  <library
      name=":loader-1.0.5.ForUnitySDK:@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\jars\classes.jar"
      resolved=":loader-1.0.5.ForUnitySDK:"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":tob_api-release:@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\e98fe9f09d47c6feaf8dbcd90ff6aeae\transformed\jetified-tob_api-release\jars\classes.jar"
      resolved=":tob_api-release:"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\e98fe9f09d47c6feaf8dbcd90ff6aeae\transformed\jetified-tob_api-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":capturelib-0.0.7:@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\1856e97ae0c789b2e3c99d404301ae2f\transformed\jetified-capturelib-0.0.7\jars\classes.jar"
      resolved=":capturelib-0.0.7:"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\1856e97ae0c789b2e3c99d404301ae2f\transformed\jetified-capturelib-0.0.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":CameraRenderingPlugin:@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\5ce2a8156cddd915ab3bf06eca6f7e26\transformed\jetified-CameraRenderingPlugin\jars\classes.jar"
      resolved=":CameraRenderingPlugin:"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\5ce2a8156cddd915ab3bf06eca6f7e26\transformed\jetified-CameraRenderingPlugin"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":PxrPlatform:@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\b9bf0a006a43dd005e719c2210f86e13\transformed\jetified-PxrPlatform\jars\classes.jar"
      resolved=":PxrPlatform:"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\b9bf0a006a43dd005e719c2210f86e13\transformed\jetified-PxrPlatform"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":tobservicelib-release:@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\301c7f59021cf8aab87447de349bedb3\transformed\jetified-tobservicelib-release\jars\classes.jar"
      resolved=":tobservicelib-release:"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\301c7f59021cf8aab87447de349bedb3\transformed\jetified-tobservicelib-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":BAuthLib-1.0.0:@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\2de471c9c2cbf5697eeff1427be18f9f\transformed\jetified-BAuthLib-1.0.0\jars\classes.jar"
      resolved=":BAuthLib-1.0.0:"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\2de471c9c2cbf5697eeff1427be18f9f\transformed\jetified-BAuthLib-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle@@:unityLibrary:xrmanifest.androidlib::release"
      jars="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\xrmanifest.androidlib\build\.transforms\8a177dfb3bbe33cc143891be57943d86\transformed\out\jars\classes.jar;D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\xrmanifest.androidlib\build\.transforms\8a177dfb3bbe33cc143891be57943d86\transformed\out\jars\libs\R.jar"
      resolved="Gradle.unityLibrary:xrmanifest.androidlib:unspecified"
      folder="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\xrmanifest.androidlib\build\.transforms\8a177dfb3bbe33cc143891be57943d86\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>
