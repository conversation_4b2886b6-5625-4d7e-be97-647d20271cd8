{"DataTable": {"attribute_marked_count_always_link_assembly": 0, "attribute_swept_count_always_link_assembly": 1, "attribute_total_count_always_link_assembly": 1, "attribute_marked_count_preserve": 163, "attribute_total_count_preserve": 101, "attribute_total_count_preserve_body": 0, "attribute_marked_count_required_member": 0, "attribute_swept_count_required_member": 8, "attribute_total_count_required_member": 8, "attribute_marked_count_require_derived": 0, "attribute_swept_count_require_derived": 0, "attribute_total_count_require_derived": 0, "attribute_marked_count_require_implementors": 0, "attribute_swept_count_require_implementors": 0, "attribute_total_count_require_implementors": 0, "attribute_total_count_required_interface": 0, "attribute_marked_count_require_attribute_usages": 0, "attribute_total_count_require_attribute_usages": 0, "attribute_total_count_dynamic_dependency": 0, "attribute_marked_count_dynamic_dependency": 11, "attribute_swept_count_dynamic_dependency": -11, "assembly_counts_total_in": 98, "assembly_counts_link": 34, "assembly_counts_copy": 12, "assembly_counts_delete": 52, "assembly_counts_total_out": 46, "unresolved_stubbing_total_count": 0, "unresolved_stubbing_missing_interface_method_count": 0, "unresolved_stubbing_missing_abstract_class_method_count": 0, "unresolved_stubbing_missing_type_count": 0, "unresolved_stubbing_missing_method_count": 0, "unrecognized_reflection_access_total_count": 265, "unrecognized_reflection_access_core_count": 161, "unrecognized_reflection_access_unity_count": 100, "unrecognized_reflection_access_user_count": 4, "recognized_reflection_access_total_count": 35, "recognized_reflection_access_core_count": 29, "recognized_reflection_access_unity_count": 2, "recognized_reflection_access_user_count": 4, "link_xml_total_count": 10, "link_xml_embedded_count": 1, "link_xml_embedded_unity_count": 0, "link_xml_embedded_user_count": 0, "link_xml_file_count": 9, "link_xml_assembly_preserve_all_total_count": 0, "link_xml_assembly_preserve_all_unity_count": 0, "link_xml_assembly_preserve_all_core_count": 0, "link_xml_assembly_preserve_all_user_count": 0, "engine_module_total_in": 65, "engine_module_deleted": 36, "engine_module_total_out": 29, "option_rule_set": "Minimal", "option_enable_report": false, "option_enable_snapshot": false, "option_enable_engine_module_stripping": true, "option_unity_root_strategy": "AllNonEngineAndNonClassLibraries", "option_enable_ildump": false}}