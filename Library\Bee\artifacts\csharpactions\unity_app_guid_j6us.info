{"System.Object": null, "Bee.TundraBackend.CSharpActionInvocationInformation": {"typeFullName": "AndroidPlayerBuildProgram.Actions.GuidGenerator", "methodName": "Run", "assemblyLocation": "C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.44f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\AndroidPlayerBuildProgram.exe", "targets": ["D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/unity_app_guid"], "inputs": ["Library/PlayerDataCache/Android6/Data/boot.config", "Library/PlayerDataCache/Android6/Data/data.unity3d", "Library/PlayerDataCache/Android6/Data/RuntimeInitializeOnLoads.json", "Library/PlayerDataCache/Android6/Data/ScriptingAssemblies.json", "Temp/StagingArea/Data/UnitySubsystems/PxrPlatform/UnitySubsystemsManifest.json", "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/Android/il2cppOutput/data/Metadata/global-metadata.dat", "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/artifacts/Android/il2cppOutput/data/Resources/mscorlib.dll-resources.dat", "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Data/unity default resources"], "targetDirectories": []}}