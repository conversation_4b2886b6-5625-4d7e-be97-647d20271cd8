<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config=":unityLibrary:xrmanifest.androidlib$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\xrmanifest.androidlib\build\intermediates\packaged_res\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config=":unityLibrary:xrmanifest.androidlib" from-dependency="true" generated-set=":unityLibrary:xrmanifest.androidlib$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\xrmanifest.androidlib\build\intermediates\packaged_res\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config=":unityLibrary$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\packaged_res\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config=":unityLibrary" from-dependency="true" generated-set=":unityLibrary$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\packaged_res\release"><file path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\packaged_res\release\values\values.xml" qualifiers=""><item name="unitySurfaceView" type="id"/><style name="BaseUnityTheme" parent="android:Theme.Holo.Light.NoActionBar.Fullscreen">
</style><style name="UnityThemeSelector" parent="BaseUnityTheme">
	<item name="android:windowBackground">@android:color/black</item>
</style><style name="UnityThemeSelector.Translucent" parent="@style/UnityThemeSelector">
    <item name="android:windowIsTranslucent">true</item>
    <item name="android:windowBackground">@android:color/transparent</item>
</style></file><file path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\packaged_res\release\values-v21\values-v21.xml" qualifiers="v21"><style name="BaseUnityTheme" parent="android:Theme.Material.Light.NoActionBar.Fullscreen">
</style></file><file path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\packaged_res\release\values-v28\values-v28.xml" qualifiers="v28"><style name="BaseUnityTheme" parent="android:Theme.Material.Light.NoActionBar.Fullscreen">
    <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
</style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\res"/><source path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\build\generated\res\rs\release"/><source path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\res"><file name="app_icon" path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\res\mipmap-anydpi-v26\app_icon.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="app_icon_round" path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\res\mipmap-anydpi-v26\app_icon_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="app_icon" path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\res\mipmap-mdpi\app_icon.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_background" path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\res\mipmap-mdpi\ic_launcher_background.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\res\mipmap-mdpi\ic_launcher_foreground.png" qualifiers="mdpi-v4" type="mipmap"/><file path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">VRAssembly</string><string name="game_view_content_description">Game view</string></file><file path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\res\values\styles.xml" qualifiers=""><style name="UnityThemeSelector" parent="BaseUnityTheme">
	<item name="android:windowBackground">@android:color/black</item>
</style><style name="BaseUnityTheme" parent="android:Theme.Holo.Light.NoActionBar.Fullscreen">
</style><style name="UnityThemeSelector.Translucent" parent="@style/UnityThemeSelector">
    <item name="android:windowIsTranslucent">true</item>
    <item name="android:windowBackground">@android:color/transparent</item>
</style></file><file path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\res\values-v21\styles.xml" qualifiers="v21"><style name="BaseUnityTheme" parent="android:Theme.Material.Light.NoActionBar.Fullscreen">
</style></file><file path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\res\values-v28\styles.xml" qualifiers="v28"><style name="BaseUnityTheme" parent="android:Theme.Material.Light.NoActionBar.Fullscreen">
    <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
</style></file></source><source path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\build\generated\res\rs\release"/><source path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>