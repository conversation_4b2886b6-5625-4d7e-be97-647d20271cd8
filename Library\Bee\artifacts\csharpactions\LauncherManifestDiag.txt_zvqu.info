{"AndroidPlayerBuildProgram.Actions.GenerateManifests+Arguments": {"Configuration": {"TargetSDKVersion": 29, "LauncherManifestTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer\\Apk\\LauncherManifest.xml", "LauncherManifestTemplateUsed": false, "LibraryManifestTemplatePath": "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer\\Apk\\UnityManifest.xml", "LibraryManifestCustomTemplateUsed": false, "LauncherManifestPath": "launcher\\src\\main\\AndroidManifest.xml", "LibraryManifestPath": "unityLibrary\\src\\main\\AndroidManifest.xml", "TVCompatibility": false, "BannerEnabled": true, "IsGame": true, "PreferredInstallLocation": "PreferExternal", "TextureSupport": "ASTC", "GamepadSupportLevel": "SupportsDPad", "TargetDevices": "AllDevices", "SupportedAspectRatioMode": 1, "MaxAspectRatio": 2.1, "ForceInternetPermission": false, "UseLowAccuracyLocation": false, "ForceSDCardPermission": false, "PreserveFramebufferAlpha": false, "DefaultInterfaceOrientation": "AutoRotation", "AllowedAutorotateToPortrait": true, "AllowedAutorotateToPortraitUpsideDown": true, "AllowedAutorotateToLandscapeLeft": true, "AllowedAutorotateToLandscapeRight": true, "ARCoreEnabled": false, "SplashScreenScale": "Center", "VREnabled": false, "RenderOutsideSafeArea": true, "GraphicsDevices": ["Vulkan", "OpenGLES3"], "OpenGLRequireES31": false, "OpenGLRequireES31AEP": false, "OpenGLRequireES32": false, "StartInFullscreen": true, "ChromeOSInputEmulation": true, "DefaultWindowWidth": 1920, "DefaultWindowHeight": 1080, "MinimumWindowWidth": 400, "MinimumWindowHeight": 300, "ResizableWindow": false, "FullScreenMode": "FullScreenWindow", "AutoRotationBehavior": "User"}, "Services": {"EnableUnityConnect": true, "EnablePerformanceReporting": true, "EnableAnalytics": true, "EnableCrashReporting": false}, "ProjectPath": "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/Android/Prj/IL2CPP/Gradle", "FeatureChecklist": ["Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/PICO.Platform-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Core-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.InputSystem.ForUI-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.Mathematics-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.TextMeshPro-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.XR.CoreUtils-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.XR.Interaction.Toolkit-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.XR.Management-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.XR.PICO-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.ImageConversionModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SpatialTracking-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsNativeModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestTextureModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt"], "Development": false, "UsingObb": false, "GradleResourcesInformation": {"TargetSDKVersion": 29, "RoundIconsAvailable": false, "RoundIconsSupported": true, "AdaptiveIconsSupported": true, "AdaptiveIconsAvailable": false}, "LauncherManifestDiagnosticsPath": "Library/Bee/artifacts/Android/Manifest/LauncherManifestDiag.txt", "LibraryManifestDiagnosticsPath": "Library/Bee/artifacts/Android/Manifest/LibraryManifestDiag.txt"}, "Bee.TundraBackend.CSharpActionInvocationInformation": {"typeFullName": "AndroidPlayerBuildProgram.Actions.GenerateManifests", "methodName": "Run", "assemblyLocation": "C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.44f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\AndroidPlayerBuildProgram.exe", "targets": ["Library/Bee/artifacts/Android/Manifest/LauncherManifestDiag.txt", "Library/Bee/artifacts/Android/Manifest/LibraryManifestDiag.txt", "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml", "D:/nwu/Assembly/UnityProjects/VRAssembly/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/AndroidManifest.xml"], "inputs": ["Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/PICO.Platform-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Core-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.InputSystem.ForUI-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.Mathematics-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.TextMeshPro-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.XR.CoreUtils-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.XR.Interaction.Toolkit-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.XR.Management-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/Unity.XR.PICO-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.ImageConversionModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SpatialTracking-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsNativeModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestTextureModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt", "Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt", "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk/LauncherManifest.xml", "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk/UnityManifest.xml"], "targetDirectories": []}}