<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item name="unitySurfaceView" type="id"/>
    <string name="app_name">VRAssembly</string>
    <string name="game_view_content_description">Game view</string>
    <style name="BaseUnityTheme" parent="android:Theme.Holo.Light.NoActionBar.Fullscreen">
</style>
    <style name="UnityThemeSelector" parent="BaseUnityTheme">
	<item name="android:windowBackground">@android:color/black</item>
</style>
    <style name="UnityThemeSelector.Translucent" parent="@style/UnityThemeSelector">
    <item name="android:windowIsTranslucent">true</item>
    <item name="android:windowBackground">@android:color/transparent</item>
</style>
</resources>