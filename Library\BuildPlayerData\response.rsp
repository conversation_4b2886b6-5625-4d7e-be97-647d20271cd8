-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\Assembly-CSharp.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\PICO.TobSupport.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\UnityEngine.TestRunner.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\Unity.Timeline.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\Unity.TextMeshPro.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\Unity.XR.Interaction.Toolkit.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\Pico.Spatializer.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\Unity.VisualScripting.Flow.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\Unity.InputSystem.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\UnityEngine.SpatialTracking.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\UnityEngine.UI.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\Unity.VisualScripting.Core.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\Unity.Mathematics.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\Cinemachine.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\Unity.XR.PICO.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\UnityEngine.XR.LegacyInputHelpers.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\Unity.InputSystem.ForUI.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\Unity.XR.CoreUtils.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\Unity.XR.Management.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\PICO.Platform.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\Pico.Spatializer.Example.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies\Unity.VisualScripting.State.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PackageCache\com.unity.testtools.codecoverage@1.2.6\lib\ReportGenerator\ReportGeneratorMerged.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PackageCache\com.unity.ext.nunit@1.0.6\net35\unity-custom\nunit.framework.dll"
-a="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PackageCache\com.unity.visualscripting@1.9.4\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll"
-s="C:\Program Files\Unity\Hub\Editor\2021.3.44f1c1\Editor\Data\Managed\UnityEngine"
-s="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PlayerScriptAssemblies"
-s="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PackageCache\com.unity.testtools.codecoverage@1.2.6\lib\ReportGenerator"
-s="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PackageCache\com.unity.ext.nunit@1.0.6\net35\unity-custom"
-s="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\PackageCache\com.unity.visualscripting@1.9.4\Runtime\VisualScripting.Flow\Dependencies\NCalc"
-s="C:\Program Files\Unity\Hub\Editor\2021.3.44f1c1\Editor\Data\Managed\UnityEngine"
-s="C:\Program Files\Unity\Hub\Editor\2021.3.44f1c1\Editor\Data\NetStandard\ref\2.1.0"
-s="C:\Program Files\Unity\Hub\Editor\2021.3.44f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard"
-s="C:\Program Files\Unity\Hub\Editor\2021.3.44f1c1\Editor\Data\NetStandard\Extensions\2.0.0"
-s="C:\Program Files\Unity\Hub\Editor\2021.3.44f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx"
-o="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\BuildPlayerData\Player"
-rn="RuntimeInitializeOnLoads.json"
-tn="TypeDb-All.json"
