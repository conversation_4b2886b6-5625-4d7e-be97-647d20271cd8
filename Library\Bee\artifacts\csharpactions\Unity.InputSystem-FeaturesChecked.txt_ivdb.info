{"System.Boolean": true, "Bee.TundraBackend.CSharpActionInvocationInformation": {"typeFullName": "PlayerBuildProgramLibrary.FeatureExtractor", "methodName": "Run", "assemblyLocation": "C:\\Program Files\\Unity\\Hub\\Editor\\2021.3.44f1c1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll", "targets": ["Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt"], "inputs": ["Library/Bee/artifacts/Android/Features/FeatureCheckList.txt", "Library/Bee/artifacts/Android/ManagedStripped/Unity.InputSystem.dll"], "targetDirectories": []}}