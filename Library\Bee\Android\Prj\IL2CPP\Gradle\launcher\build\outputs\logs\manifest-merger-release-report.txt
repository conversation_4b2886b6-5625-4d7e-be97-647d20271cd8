-- Merging decision tree log ---
manifest
ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
MERGED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-161:12
MERGED from [:loader-1.0.5.ForUnitySDK:] C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\AndroidManifest.xml:2:1-20:12
MERGED from [:tob_api-release:] C:\Users\<USER>\.gradle\caches\transforms-3\e98fe9f09d47c6feaf8dbcd90ff6aeae\transformed\jetified-tob_api-release\AndroidManifest.xml:2:1-7:12
MERGED from [:capturelib-0.0.7:] C:\Users\<USER>\.gradle\caches\transforms-3\1856e97ae0c789b2e3c99d404301ae2f\transformed\jetified-capturelib-0.0.7\AndroidManifest.xml:2:1-7:12
MERGED from [:CameraRenderingPlugin:] C:\Users\<USER>\.gradle\caches\transforms-3\5ce2a8156cddd915ab3bf06eca6f7e26\transformed\jetified-CameraRenderingPlugin\AndroidManifest.xml:2:1-7:12
MERGED from [:PxrPlatform:] C:\Users\<USER>\.gradle\caches\transforms-3\b9bf0a006a43dd005e719c2210f86e13\transformed\jetified-PxrPlatform\AndroidManifest.xml:2:1-7:12
MERGED from [:tobservicelib-release:] C:\Users\<USER>\.gradle\caches\transforms-3\301c7f59021cf8aab87447de349bedb3\transformed\jetified-tobservicelib-release\AndroidManifest.xml:2:1-9:12
MERGED from [:BAuthLib-1.0.0:] C:\Users\<USER>\.gradle\caches\transforms-3\2de471c9c2cbf5697eeff1427be18f9f\transformed\jetified-BAuthLib-1.0.0\AndroidManifest.xml:2:1-13:12
MERGED from [:unityLibrary:xrmanifest.androidlib] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\xrmanifest.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:2:1-12:12
INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
	package
		INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
		ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
		INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
		ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
		INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:70-116
	android:versionCode
		INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
		ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12
		INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:11-69
	android:installLocation
		ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:117-157
supports-screens
ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:3-163
	android:largeScreens
		ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:78-105
	android:smallScreens
		ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:21-48
	android:normalScreens
		ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:49-77
	android:xlargeScreens
		ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:106-134
	android:anyDensity
		ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:3:135-160
application
ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:3-83
MERGED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:29:5-159:19
MERGED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:29:5-159:19
MERGED from [:unityLibrary:xrmanifest.androidlib] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\xrmanifest.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:7:5-10:19
MERGED from [:unityLibrary:xrmanifest.androidlib] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\xrmanifest.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:7:5-10:19
	android:requestLegacyExternalStorage
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:31:9-52
	android:extractNativeLibs
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:30:9-41
	android:label
		ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:16-48
	android:icon
		ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:4:49-80
uses-sdk
INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
MERGED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:6:5-44
MERGED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:6:5-44
MERGED from [:loader-1.0.5.ForUnitySDK:] C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\AndroidManifest.xml:7:5-44
MERGED from [:loader-1.0.5.ForUnitySDK:] C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\AndroidManifest.xml:7:5-44
MERGED from [:tob_api-release:] C:\Users\<USER>\.gradle\caches\transforms-3\e98fe9f09d47c6feaf8dbcd90ff6aeae\transformed\jetified-tob_api-release\AndroidManifest.xml:5:5-44
MERGED from [:tob_api-release:] C:\Users\<USER>\.gradle\caches\transforms-3\e98fe9f09d47c6feaf8dbcd90ff6aeae\transformed\jetified-tob_api-release\AndroidManifest.xml:5:5-44
MERGED from [:capturelib-0.0.7:] C:\Users\<USER>\.gradle\caches\transforms-3\1856e97ae0c789b2e3c99d404301ae2f\transformed\jetified-capturelib-0.0.7\AndroidManifest.xml:5:5-44
MERGED from [:capturelib-0.0.7:] C:\Users\<USER>\.gradle\caches\transforms-3\1856e97ae0c789b2e3c99d404301ae2f\transformed\jetified-capturelib-0.0.7\AndroidManifest.xml:5:5-44
MERGED from [:CameraRenderingPlugin:] C:\Users\<USER>\.gradle\caches\transforms-3\5ce2a8156cddd915ab3bf06eca6f7e26\transformed\jetified-CameraRenderingPlugin\AndroidManifest.xml:5:5-44
MERGED from [:CameraRenderingPlugin:] C:\Users\<USER>\.gradle\caches\transforms-3\5ce2a8156cddd915ab3bf06eca6f7e26\transformed\jetified-CameraRenderingPlugin\AndroidManifest.xml:5:5-44
MERGED from [:PxrPlatform:] C:\Users\<USER>\.gradle\caches\transforms-3\b9bf0a006a43dd005e719c2210f86e13\transformed\jetified-PxrPlatform\AndroidManifest.xml:5:5-44
MERGED from [:PxrPlatform:] C:\Users\<USER>\.gradle\caches\transforms-3\b9bf0a006a43dd005e719c2210f86e13\transformed\jetified-PxrPlatform\AndroidManifest.xml:5:5-44
MERGED from [:tobservicelib-release:] C:\Users\<USER>\.gradle\caches\transforms-3\301c7f59021cf8aab87447de349bedb3\transformed\jetified-tobservicelib-release\AndroidManifest.xml:5:5-7:41
MERGED from [:tobservicelib-release:] C:\Users\<USER>\.gradle\caches\transforms-3\301c7f59021cf8aab87447de349bedb3\transformed\jetified-tobservicelib-release\AndroidManifest.xml:5:5-7:41
MERGED from [:BAuthLib-1.0.0:] C:\Users\<USER>\.gradle\caches\transforms-3\2de471c9c2cbf5697eeff1427be18f9f\transformed\jetified-BAuthLib-1.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [:BAuthLib-1.0.0:] C:\Users\<USER>\.gradle\caches\transforms-3\2de471c9c2cbf5697eeff1427be18f9f\transformed\jetified-BAuthLib-1.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [:unityLibrary:xrmanifest.androidlib] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\xrmanifest.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-43
MERGED from [:unityLibrary:xrmanifest.androidlib] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\xrmanifest.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:5:5-43
INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
		ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
		INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
		ADDED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
		INJECTED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml
uses-feature#0x00030000
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:5-54
	android:glEsVersion
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:19-51
uses-feature#android.hardware.vulkan.version
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:9:5-11:36
	android:required
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:9-33
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:10:9-55
supports-gl-texture#GL_KHR_texture_compression_astc_ldr
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:5-79
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:26-76
uses-permission#android.permission.INTERNET
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:15:5-67
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:15:22-64
uses-feature#android.hardware.touchscreen
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:17:5-19:36
	android:required
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:19:9-33
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:18:9-52
uses-feature#android.hardware.touchscreen.multitouch
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:20:5-22:36
	android:required
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:22:9-33
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:21:9-63
uses-feature#android.hardware.touchscreen.multitouch.distinct
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:23:5-25:36
	android:required
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:25:9-33
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:24:9-72
uses-permission#android.permission.WRITE_SETTINGS
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:27:5-73
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:27:22-70
activity#com.unity3d.player.UnityPlayerActivity
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:32:9-52:20
MERGED from [:unityLibrary:xrmanifest.androidlib] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\xrmanifest.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:9-9:20
MERGED from [:unityLibrary:xrmanifest.androidlib] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\xrmanifest.androidlib\build\intermediates\merged_manifest\release\AndroidManifest.xml:8:9-9:20
	android:screenOrientation
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:38:13-49
	android:launchMode
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:36:13-44
	android:hardwareAccelerated
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:35:13-48
	android:resizeableActivity
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:37:13-47
	android:configChanges
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:34:13-194
	android:theme
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:39:13-54
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:33:13-66
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:40:13-44:29
action#android.intent.action.MAIN
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:41:17-69
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:41:25-66
category#android.intent.category.LAUNCHER
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:43:17-77
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:43:27-74
meta-data#unityplayer.UnityActivity
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:46:13-48:40
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:48:17-37
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:47:17-57
meta-data#android.notch_support
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:49:13-51:40
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:51:17-37
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:50:17-53
meta-data#unity.splash-mode
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:54:9-56:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:56:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:55:13-45
meta-data#unity.splash-enable
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:57:9-59:36
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:59:13-33
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:58:13-47
meta-data#unity.launch-fullscreen
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:60:9-62:36
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:62:13-33
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:61:13-51
meta-data#notch.config
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:63:9-65:50
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:65:13-47
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:64:13-40
meta-data#unity.auto-report-fully-drawn
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:66:9-68:36
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:68:13-33
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:67:13-57
meta-data#pvr.app.type
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:69:9-71:34
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:71:13-31
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:70:13-40
meta-data#pxr.sdk.version_code
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:72:9-74:36
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:74:13-33
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:73:13-48
meta-data#pvr.sdk.version
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:75:9-77:49
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:77:13-46
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:76:13-43
meta-data#enable_cpt
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:78:9-80:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:80:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:79:13-38
meta-data#Enable_AdaptiveHandModel
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:81:9-83:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:83:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:82:13-52
meta-data#Hand_Tracking_HighFrequency
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:84:9-86:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:86:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:85:13-55
meta-data#rendering_mode
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:87:9-89:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:89:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:88:13-42
meta-data#display_rate
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:90:9-92:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:92:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:91:13-40
meta-data#color_Space
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:93:9-95:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:95:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:94:13-39
meta-data#MRCsupport
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:96:9-98:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:98:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:97:13-38
meta-data#pvr.LateLatching
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:99:9-101:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:101:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:100:13-44
meta-data#pvr.LateLatchingDebug
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:102:9-104:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:104:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:103:13-49
meta-data#pvr.app.splash
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:105:9-107:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:107:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:106:13-42
meta-data#PICO.swift.feature
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:108:9-110:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:110:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:109:13-46
meta-data#adaptive_resolution
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:111:9-113:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:113:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:112:13-47
meta-data#enable_mr_safeguard
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:114:9-116:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:116:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:115:13-47
meta-data#enable_vst
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:117:9-119:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:119:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:118:13-38
meta-data#enable_anchor
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:120:9-122:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:122:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:121:13-41
meta-data#mr_map_mgr_auto_start
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:123:9-125:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:125:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:124:13-49
meta-data#enable_spatial_anchor
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:126:9-128:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:128:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:127:13-49
meta-data#enable_cloud_anchor
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:129:9-131:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:131:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:130:13-47
meta-data#enable_mesh_anchor
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:132:9-134:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:134:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:133:13-46
meta-data#enable_scene_anchor
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:135:9-137:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:137:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:136:13-47
meta-data#pvr.SuperResolution
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:138:9-140:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:140:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:139:13-47
meta-data#pvr.NormalSharpening
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:141:9-143:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:143:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:142:13-48
meta-data#pvr.QualitySharpening
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:144:9-146:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:146:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:145:13-49
meta-data#pvr.FixedFoveatedSharpening
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:147:9-149:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:149:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:148:13-55
meta-data#pvr.SelfAdaptiveSharpening
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:150:9-152:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:152:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:151:13-54
meta-data#pvr.app.secure_mr
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:153:9-155:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:155:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:154:13-45
meta-data#controller
ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:156:9-158:33
	android:value
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:158:13-30
	android:name
		ADDED from [:unityLibrary] D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\merged_manifest\release\AndroidManifest.xml:157:13-38
uses-permission#org.khronos.openxr.permission.OPENXR
ADDED from [:loader-1.0.5.ForUnitySDK:] C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\AndroidManifest.xml:13:5-76
	android:name
		ADDED from [:loader-1.0.5.ForUnitySDK:] C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\AndroidManifest.xml:13:22-73
uses-permission#org.khronos.openxr.permission.OPENXR_SYSTEM
ADDED from [:loader-1.0.5.ForUnitySDK:] C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\AndroidManifest.xml:14:5-83
	android:name
		ADDED from [:loader-1.0.5.ForUnitySDK:] C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\AndroidManifest.xml:14:22-80
queries
ADDED from [:loader-1.0.5.ForUnitySDK:] C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\AndroidManifest.xml:16:5-18:15
provider
ADDED from [:loader-1.0.5.ForUnitySDK:] C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\AndroidManifest.xml:17:9-118
	android:authorities
		ADDED from [:loader-1.0.5.ForUnitySDK:] C:\Users\<USER>\.gradle\caches\transforms-3\4bf41e0dc3dae5d81fedbb56d62f6b3b\transformed\jetified-loader-1.0.5.ForUnitySDK\AndroidManifest.xml:17:19-115
uses-permission#com.pvr.tobactivate.permission.AUTH_CHECK
ADDED from [:BAuthLib-1.0.0:] C:\Users\<USER>\.gradle\caches\transforms-3\2de471c9c2cbf5697eeff1427be18f9f\transformed\jetified-BAuthLib-1.0.0\AndroidManifest.xml:11:5-81
	android:name
		ADDED from [:BAuthLib-1.0.0:] C:\Users\<USER>\.gradle\caches\transforms-3\2de471c9c2cbf5697eeff1427be18f9f\transformed\jetified-BAuthLib-1.0.0\AndroidManifest.xml:11:22-78
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
IMPLIED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12 reason: com.UnityTechnologies.XR.Manifest has a targetSdkVersion < 4
uses-permission#android.permission.READ_PHONE_STATE
IMPLIED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12 reason: com.UnityTechnologies.XR.Manifest has a targetSdkVersion < 4
uses-permission#android.permission.READ_EXTERNAL_STORAGE
IMPLIED from D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\launcher\src\main\AndroidManifest.xml:2:1-5:12 reason: com.UnityTechnologies.XR.Manifest requested WRITE_EXTERNAL_STORAGE
