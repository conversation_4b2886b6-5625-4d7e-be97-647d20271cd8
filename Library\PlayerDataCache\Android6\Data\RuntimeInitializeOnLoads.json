{"root": [{"assemblyName": "Unity.XR.Interaction.Toolkit", "nameSpace": "UnityEngine.XR.Interaction.Toolkit.Inputs.Simulation", "className": "SimulatedInputLayoutLoader", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.XR.Interaction.Toolkit", "nameSpace": "UnityEngine.XR.Interaction.Toolkit.Inputs.Simulation", "className": "XRDeviceSimulatorLoader", "methodName": "Initialize", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.XR.Interaction.Toolkit", "nameSpace": "UnityEngine.XR.Interaction.Toolkit.Inputs.Interactions", "className": "SectorInteraction", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.XR.Interaction.Toolkit", "nameSpace": "UnityEngine.XR.Interaction.Toolkit.Inputs.Composites", "className": "Vector3FallbackComposite", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.XR.Interaction.Toolkit", "nameSpace": "UnityEngine.XR.Interaction.Toolkit.Inputs.Composites", "className": "QuaternionFallbackComposite", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.XR.Interaction.Toolkit", "nameSpace": "UnityEngine.XR.Interaction.Toolkit.Inputs.Composites", "className": "IntegerFallbackComposite", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.XR.Interaction.Toolkit", "nameSpace": "UnityEngine.XR.Interaction.Toolkit.Inputs.Composites", "className": "ButtonFallbackComposite", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitializeInPlayer", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitialUpdate", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.VisualScripting.Core", "nameSpace": "Unity.VisualScripting", "className": "RuntimeVSUsageUtility", "methodName": "RuntimeInitializeOnLoadBeforeSceneLoad", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineStoryboard", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineCore", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "UpdateTracker", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineImpulseManager", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Unity.XR.PICO", "nameSpace": "Unity.XR.PXR", "className": "PXR_Loader", "methodName": "RuntimeLoadPicoPlugin", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.XR.Management", "nameSpace": "UnityEngine.XR.Management", "className": "XRGeneralSettings", "methodName": "AttemptInitializeXRSDKOnLoad", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.XR.Management", "nameSpace": "UnityEngine.XR.Management", "className": "XRGeneralSettings", "methodName": "AttemptStartXRSDKOnBeforeSplashScreen", "loadTypes": 3, "isUnityClass": true}]}