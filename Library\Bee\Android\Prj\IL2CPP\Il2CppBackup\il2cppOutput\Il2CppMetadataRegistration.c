﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern Il2CppGenericClass* const g_Il2CppGenericTypes[];
extern const Il2CppGenericInst* const g_Il2CppGenericInstTable[];
extern const Il2CppGenericMethodFunctionsDefinitions g_Il2CppGenericMethodFunctions[];
extern const Il2CppType* const  g_Il2CppTypeTable[];
extern const Il2CppMethodSpec g_Il2CppMethodSpecTable[];
IL2CPP_EXTERN_C_CONST int32_t* g_FieldOffsetTable[];
IL2CPP_EXTERN_C_CONST Il2CppTypeDefinitionSizes* g_Il2CppTypeDefinitionSizesTable[];
IL2CPP_EXTERN_C const Il2CppMetadataRegistration g_MetadataRegistration;
const Il2CppMetadataRegistration g_MetadataRegistration = 
{
	5361,
	g_Il2CppGenericTypes,
	3363,
	g_Il2CppGenericInstTable,
	40473,
	g_Il2CppGenericMethodFunctions,
	17986,
	g_Il2CppTypeTable,
	48874,
	g_Il2CppMethodSpecTable,
	5933,
	g_FieldOffsetTable,
	5933,
	g_Il2CppTypeDefinitionSizesTable,
	0,
	NULL,
};
