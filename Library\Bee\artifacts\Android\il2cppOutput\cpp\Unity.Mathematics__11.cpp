﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



// System.IntPtr[]
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
// System.Diagnostics.StackTrace[]
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
// System.ArgumentException
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263;
// System.Collections.IDictionary
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
// System.Runtime.Serialization.SafeSerializationManager
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
// System.String
struct String_t;
// System.Void
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;

IL2CPP_EXTERN_C RuntimeClass* ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* BitConverter_t6E99605185963BC12B3D369E13F2B88997E64A27_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteralFB182D98F776AC1C061FA5C163FE7F6E7C08B5BD;
IL2CPP_EXTERN_C String_t* _stringLiteralFEB995DBF7004A48F3EDB181C599FB99B4A380FB;
IL2CPP_EXTERN_C const RuntimeMethod* Plane_CheckPlaneIsNormalized_m6408EC4BE1D7A0ADB6832288E286D9C70404B888_RuntimeMethod_var;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif

// Unity.Mathematics.Geometry.Math
struct Math_t02C7364C06982BA8F186B38E3C811582A40AB897  : public RuntimeObject
{
};

// System.String
struct String_t  : public RuntimeObject
{
	// System.Int32 System.String::_stringLength
	int32_t ____stringLength_4;
	// System.Char System.String::_firstChar
	Il2CppChar ____firstChar_5;
};

// System.ValueType
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
// Native definition for P/Invoke marshalling of System.ValueType
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
// Native definition for COM marshalling of System.ValueType
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};

// System.Boolean
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	// System.Boolean System.Boolean::m_value
	bool ___m_value_0;
};

// System.Double
struct Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F 
{
	// System.Double System.Double::m_value
	double ___m_value_0;
};

// System.Int32
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	// System.Int32 System.Int32::m_value
	int32_t ___m_value_0;
};

// System.IntPtr
struct IntPtr_t 
{
	// System.Void* System.IntPtr::m_value
	void* ___m_value_0;
};

// System.Single
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	// System.Single System.Single::m_value
	float ___m_value_0;
};

// System.UInt32
struct UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B 
{
	// System.UInt32 System.UInt32::m_value
	uint32_t ___m_value_0;
};

// System.Void
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};

// Unity.Mathematics.bool3
struct bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 
{
	// System.Boolean Unity.Mathematics.bool3::x
	bool ___x_0;
	// System.Boolean Unity.Mathematics.bool3::y
	bool ___y_1;
	// System.Boolean Unity.Mathematics.bool3::z
	bool ___z_2;
};

// Unity.Mathematics.float3
struct float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E 
{
	// System.Single Unity.Mathematics.float3::x
	float ___x_0;
	// System.Single Unity.Mathematics.float3::y
	float ___y_1;
	// System.Single Unity.Mathematics.float3::z
	float ___z_2;
};

// Unity.Mathematics.float4
struct float4_t89D9A294E7A79BD81BFBDD18654508532958555E 
{
	// System.Single Unity.Mathematics.float4::x
	float ___x_0;
	// System.Single Unity.Mathematics.float4::y
	float ___y_1;
	// System.Single Unity.Mathematics.float4::z
	float ___z_2;
	// System.Single Unity.Mathematics.float4::w
	float ___w_3;
};

// Unity.Mathematics.uint3
struct uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B 
{
	// System.UInt32 Unity.Mathematics.uint3::x
	uint32_t ___x_0;
	// System.UInt32 Unity.Mathematics.uint3::y
	uint32_t ___y_1;
	// System.UInt32 Unity.Mathematics.uint3::z
	uint32_t ___z_2;
};

// Unity.Mathematics.math/IntFloatUnion
struct IntFloatUnion_t549256A9DD754252DD18383D9CE7EA55EBBD6D96 
{
	union
	{
		#pragma pack(push, tp, 1)
		struct
		{
			// System.Int32 Unity.Mathematics.math/IntFloatUnion::intValue
			int32_t ___intValue_0;
		};
		#pragma pack(pop, tp)
		struct
		{
			int32_t ___intValue_0_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			// System.Single Unity.Mathematics.math/IntFloatUnion::floatValue
			float ___floatValue_1;
		};
		#pragma pack(pop, tp)
		struct
		{
			float ___floatValue_1_forAlignmentOnly;
		};
	};
};

// System.Exception
struct Exception_t  : public RuntimeObject
{
	// System.String System.Exception::_className
	String_t* ____className_1;
	// System.String System.Exception::_message
	String_t* ____message_2;
	// System.Collections.IDictionary System.Exception::_data
	RuntimeObject* ____data_3;
	// System.Exception System.Exception::_innerException
	Exception_t* ____innerException_4;
	// System.String System.Exception::_helpURL
	String_t* ____helpURL_5;
	// System.Object System.Exception::_stackTrace
	RuntimeObject* ____stackTrace_6;
	// System.String System.Exception::_stackTraceString
	String_t* ____stackTraceString_7;
	// System.String System.Exception::_remoteStackTraceString
	String_t* ____remoteStackTraceString_8;
	// System.Int32 System.Exception::_remoteStackIndex
	int32_t ____remoteStackIndex_9;
	// System.Object System.Exception::_dynamicMethods
	RuntimeObject* ____dynamicMethods_10;
	// System.Int32 System.Exception::_HResult
	int32_t ____HResult_11;
	// System.String System.Exception::_source
	String_t* ____source_12;
	// System.Runtime.Serialization.SafeSerializationManager System.Exception::_safeSerializationManager
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager_13;
	// System.Diagnostics.StackTrace[] System.Exception::captured_traces
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces_14;
	// System.IntPtr[] System.Exception::native_trace_ips
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips_15;
	// System.Int32 System.Exception::caught_in_unmanaged
	int32_t ___caught_in_unmanaged_16;
};
// Native definition for P/Invoke marshalling of System.Exception
struct Exception_t_marshaled_pinvoke
{
	char* ____className_1;
	char* ____message_2;
	RuntimeObject* ____data_3;
	Exception_t_marshaled_pinvoke* ____innerException_4;
	char* ____helpURL_5;
	Il2CppIUnknown* ____stackTrace_6;
	char* ____stackTraceString_7;
	char* ____remoteStackTraceString_8;
	int32_t ____remoteStackIndex_9;
	Il2CppIUnknown* ____dynamicMethods_10;
	int32_t ____HResult_11;
	char* ____source_12;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager_13;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces_14;
	Il2CppSafeArray/*NONE*/* ___native_trace_ips_15;
	int32_t ___caught_in_unmanaged_16;
};
// Native definition for COM marshalling of System.Exception
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className_1;
	Il2CppChar* ____message_2;
	RuntimeObject* ____data_3;
	Exception_t_marshaled_com* ____innerException_4;
	Il2CppChar* ____helpURL_5;
	Il2CppIUnknown* ____stackTrace_6;
	Il2CppChar* ____stackTraceString_7;
	Il2CppChar* ____remoteStackTraceString_8;
	int32_t ____remoteStackIndex_9;
	Il2CppIUnknown* ____dynamicMethods_10;
	int32_t ____HResult_11;
	Il2CppChar* ____source_12;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager_13;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces_14;
	Il2CppSafeArray/*NONE*/* ___native_trace_ips_15;
	int32_t ___caught_in_unmanaged_16;
};

// Unity.Mathematics.Geometry.MinMaxAABB
struct MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 
{
	// Unity.Mathematics.float3 Unity.Mathematics.Geometry.MinMaxAABB::Min
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___Min_0;
	// Unity.Mathematics.float3 Unity.Mathematics.Geometry.MinMaxAABB::Max
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___Max_1;
};

// Unity.Mathematics.Geometry.Plane
struct Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 
{
	// Unity.Mathematics.float4 Unity.Mathematics.Geometry.Plane::NormalAndDistance
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___NormalAndDistance_0;
};

// Unity.Mathematics.float3x3
struct float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79 
{
	// Unity.Mathematics.float3 Unity.Mathematics.float3x3::c0
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___c0_0;
	// Unity.Mathematics.float3 Unity.Mathematics.float3x3::c1
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___c1_1;
	// Unity.Mathematics.float3 Unity.Mathematics.float3x3::c2
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___c2_2;
};

// Unity.Mathematics.float4x4
struct float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 
{
	// Unity.Mathematics.float4 Unity.Mathematics.float4x4::c0
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___c0_0;
	// Unity.Mathematics.float4 Unity.Mathematics.float4x4::c1
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___c1_1;
	// Unity.Mathematics.float4 Unity.Mathematics.float4x4::c2
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___c2_2;
	// Unity.Mathematics.float4 Unity.Mathematics.float4x4::c3
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___c3_3;
};

// Unity.Mathematics.quaternion
struct quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 
{
	// Unity.Mathematics.float4 Unity.Mathematics.quaternion::value
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___value_0;
};

// Unity.Mathematics.RigidTransform
struct RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD 
{
	// Unity.Mathematics.quaternion Unity.Mathematics.RigidTransform::rot
	quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 ___rot_0;
	// Unity.Mathematics.float3 Unity.Mathematics.RigidTransform::pos
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___pos_1;
};

// System.SystemException
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};

// System.ArgumentException
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
	// System.String System.ArgumentException::_paramName
	String_t* ____paramName_18;
};

// Unity.Mathematics.Geometry.Math

// Unity.Mathematics.Geometry.Math

// System.String
struct String_t_StaticFields
{
	// System.String System.String::Empty
	String_t* ___Empty_6;
};

// System.String

// System.Boolean
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	// System.String System.Boolean::TrueString
	String_t* ___TrueString_5;
	// System.String System.Boolean::FalseString
	String_t* ___FalseString_6;
};

// System.Boolean

// System.Double

// System.Double

// System.Int32

// System.Int32

// System.Single

// System.Single

// System.UInt32

// System.UInt32

// System.Void

// System.Void

// Unity.Mathematics.bool3

// Unity.Mathematics.bool3

// Unity.Mathematics.float3
struct float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E_StaticFields
{
	// Unity.Mathematics.float3 Unity.Mathematics.float3::zero
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___zero_3;
};

// Unity.Mathematics.float3

// Unity.Mathematics.float4
struct float4_t89D9A294E7A79BD81BFBDD18654508532958555E_StaticFields
{
	// Unity.Mathematics.float4 Unity.Mathematics.float4::zero
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___zero_4;
};

// Unity.Mathematics.float4

// Unity.Mathematics.uint3
struct uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B_StaticFields
{
	// Unity.Mathematics.uint3 Unity.Mathematics.uint3::zero
	uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B ___zero_3;
};

// Unity.Mathematics.uint3

// Unity.Mathematics.math/IntFloatUnion

// Unity.Mathematics.math/IntFloatUnion

// Unity.Mathematics.Geometry.MinMaxAABB

// Unity.Mathematics.Geometry.MinMaxAABB

// Unity.Mathematics.Geometry.Plane

// Unity.Mathematics.Geometry.Plane

// Unity.Mathematics.float3x3
struct float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79_StaticFields
{
	// Unity.Mathematics.float3x3 Unity.Mathematics.float3x3::identity
	float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79 ___identity_3;
	// Unity.Mathematics.float3x3 Unity.Mathematics.float3x3::zero
	float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79 ___zero_4;
};

// Unity.Mathematics.float3x3

// Unity.Mathematics.float4x4
struct float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2_StaticFields
{
	// Unity.Mathematics.float4x4 Unity.Mathematics.float4x4::identity
	float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 ___identity_4;
	// Unity.Mathematics.float4x4 Unity.Mathematics.float4x4::zero
	float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 ___zero_5;
};

// Unity.Mathematics.float4x4

// Unity.Mathematics.quaternion
struct quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4_StaticFields
{
	// Unity.Mathematics.quaternion Unity.Mathematics.quaternion::identity
	quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 ___identity_1;
};

// Unity.Mathematics.quaternion

// Unity.Mathematics.RigidTransform
struct RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD_StaticFields
{
	// Unity.Mathematics.RigidTransform Unity.Mathematics.RigidTransform::identity
	RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD ___identity_2;
};

// Unity.Mathematics.RigidTransform

// System.ArgumentException

// System.ArgumentException
#ifdef __clang__
#pragma clang diagnostic pop
#endif



// System.Void Unity.Mathematics.Geometry.MinMaxAABB::.ctor(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_min, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_max, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.float3::op_Multiply(Unity.Mathematics.float3,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float ___1_rhs, const RuntimeMethod* method) ;
// Unity.Mathematics.Geometry.MinMaxAABB Unity.Mathematics.Geometry.MinMaxAABB::CreateFromCenterAndHalfExtents(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 MinMaxAABB_CreateFromCenterAndHalfExtents_mF18074A621916107BF592378F72B6F113294F508_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_center, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_halfExtents, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.float3::op_Subtraction(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.float3::op_Addition(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.Geometry.MinMaxAABB::get_Extents()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_Extents_m7E246B61BA832B3FB5EB8DCA4A35EC93688F105C (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.Geometry.MinMaxAABB::get_HalfExtents()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_HalfExtents_m3656E833DBE99FE9D023F0E4B1497CF35F6BC948 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.Geometry.MinMaxAABB::get_Center()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_Center_mC27A51CF13A95A053DEA07566A1F1900CFD98F4C (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) ;
// Unity.Mathematics.bool3 Unity.Mathematics.float3::op_LessThanOrEqual(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) ;
// System.Boolean Unity.Mathematics.math::all(Unity.Mathematics.bool3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline (bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___0_x, const RuntimeMethod* method) ;
// System.Boolean Unity.Mathematics.Geometry.MinMaxAABB::get_IsValid()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MinMaxAABB_get_IsValid_m84D96167E3AE9516D86FEC54B1CD138BD53FC5B8 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.float3::get_yzx()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) ;
// System.Single Unity.Mathematics.math::dot(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) ;
// System.Single Unity.Mathematics.Geometry.MinMaxAABB::get_SurfaceArea()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float MinMaxAABB_get_SurfaceArea_m542F0EA64B3A4C7975BAB0C3EBD354D79F34B1CE (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) ;
// Unity.Mathematics.bool3 Unity.Mathematics.float3::op_GreaterThanOrEqual(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 float3_op_GreaterThanOrEqual_m01767B59951623AD803736AB63E12D9BC6FC1AAE_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) ;
// Unity.Mathematics.bool3 Unity.Mathematics.bool3::op_BitwiseAnd(Unity.Mathematics.bool3,Unity.Mathematics.bool3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 bool3_op_BitwiseAnd_mE44CC5838094A40F1F605A7798994197165A63E1_inline (bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___0_lhs, bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___1_rhs, const RuntimeMethod* method) ;
// System.Boolean Unity.Mathematics.Geometry.MinMaxAABB::Contains(Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MinMaxAABB_Contains_m507D89756D76CA0E2CBD8CAD23C6CE27A5F39089_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) ;
// System.Boolean Unity.Mathematics.Geometry.MinMaxAABB::Contains(Unity.Mathematics.Geometry.MinMaxAABB)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MinMaxAABB_Contains_m8AB762A87FD22983A2A5ED2C96D3C559876D8823_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) ;
// System.Boolean Unity.Mathematics.Geometry.MinMaxAABB::Overlaps(Unity.Mathematics.Geometry.MinMaxAABB)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MinMaxAABB_Overlaps_mEEC801434D524543F9E6D5D77A642A5184F8C4D9_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.float3::op_Subtraction(Unity.Mathematics.float3,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Subtraction_m111BEEA770E140739DDC6A3410736DFF7EE32045_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float ___1_rhs, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.float3::op_Addition(Unity.Mathematics.float3,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Addition_mABF24BC9A16C272B9F5AB21A601B9D9A831F8C43_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float ___1_rhs, const RuntimeMethod* method) ;
// System.Void Unity.Mathematics.Geometry.MinMaxAABB::Expand(System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MinMaxAABB_Expand_m8574B6375684AC91E413410881D03B71C400DC75_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float ___0_signedDistance, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.math::min(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_min_m13CC8D5B7844D954C3125DD72831C693AB8A7FF5_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.math::max(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_max_m247D41258606F80861E72309300DF6A3F8B50AE4_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) ;
// System.Void Unity.Mathematics.Geometry.MinMaxAABB::Encapsulate(Unity.Mathematics.Geometry.MinMaxAABB)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MinMaxAABB_Encapsulate_m7043627B976BE96D6D734288EEA09563352F753B_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) ;
// System.Void Unity.Mathematics.Geometry.MinMaxAABB::Encapsulate(Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MinMaxAABB_Encapsulate_mE01CED4767A6B50D846AC30AFCA49A91BA820CD5_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) ;
// System.Boolean Unity.Mathematics.float3::Equals(Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_rhs, const RuntimeMethod* method) ;
// System.Boolean Unity.Mathematics.Geometry.MinMaxAABB::Equals(Unity.Mathematics.Geometry.MinMaxAABB)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MinMaxAABB_Equals_m6DC492AB1804679250EC6C97CC6DF32299EA8E11_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_other, const RuntimeMethod* method) ;
// System.String System.String::Format(System.String,System.Object,System.Object)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Format_mFB7DA489BD99F4670881FF50EC017BFB0A5C0987 (String_t* ___0_format, RuntimeObject* ___1_arg0, RuntimeObject* ___2_arg1, const RuntimeMethod* method) ;
// System.String Unity.Mathematics.Geometry.MinMaxAABB::ToString()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* MinMaxAABB_ToString_mD299FEC6092F6072F7BC91773DA766E0E61DEC3F_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) ;
// System.Void Unity.Mathematics.float3::.ctor(System.Single,System.Single,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.math::rotate(Unity.Mathematics.quaternion,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_rotate_m68CD27B1D0643EA356D0AB41ECB004CE094FDA3F_inline (quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 ___0_q, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_v, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.math::abs(Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_abs_mC7F2BBD861835C82A0A47A47A44B73E704D7F63B_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.math::transform(Unity.Mathematics.RigidTransform,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_transform_m5F6B69A9C0E6E1AF63D8112D8753394891972E44_inline (RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD ___0_a, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_pos, const RuntimeMethod* method) ;
// System.Void Unity.Mathematics.float3x3::.ctor(Unity.Mathematics.float4x4)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void float3x3__ctor_m3EA9552B4922CACBAC36054687E8EF9C1ED99951 (float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79* __this, float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 ___0_f4x4, const RuntimeMethod* method) ;
// Unity.Mathematics.Geometry.MinMaxAABB Unity.Mathematics.Geometry.Math::Transform(Unity.Mathematics.float3x3,Unity.Mathematics.Geometry.MinMaxAABB)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 Math_Transform_m762480F0988F685C5039CBFBFA540C85385A53CC_inline (float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79 ___0_transform, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___1_aabb, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.float4::get_xyz()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E* __this, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.float3::get_xyz()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.float3::get_xxx()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_xxx_mFD7DFB9FF23BB0B3437F12CC35DB3D1E0ADF7B20_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.float3::op_Multiply(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) ;
// Unity.Mathematics.bool3 Unity.Mathematics.float3::op_LessThan(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 float3_op_LessThan_m540182EDEF57B5A865B1C22972CF5C3E862B9C51_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.math::select(Unity.Mathematics.float3,Unity.Mathematics.float3,Unity.Mathematics.bool3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_a, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_b, bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___2_c, const RuntimeMethod* method) ;
// Unity.Mathematics.bool3 Unity.Mathematics.bool3::op_LogicalNot(Unity.Mathematics.bool3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 bool3_op_LogicalNot_m85C703CC4098B3731505A162957F91C0373548BD_inline (bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___0_val, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.float3::get_yyy()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_yyy_m6FCA12991237EDC77F7C4B6A7F73710338330CCD_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.float3::get_zzz()
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_zzz_m1C7C995F170030A7EF534E2C99E6AFE6928AE9D4_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) ;
// System.Void Unity.Mathematics.float4::.ctor(System.Single,System.Single,System.Single,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E* __this, float ___0_x, float ___1_y, float ___2_z, float ___3_w, const RuntimeMethod* method) ;
// Unity.Mathematics.float4 Unity.Mathematics.Geometry.Plane::Normalize(Unity.Mathematics.float4)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E Plane_Normalize_mFF53F95372AE2A79C71B546A70B0F152EC308544_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_planeCoefficients, const RuntimeMethod* method) ;
// System.Void Unity.Mathematics.Geometry.Plane::.ctor(System.Single,System.Single,System.Single,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Plane__ctor_mE41939B3E3E2AE7802AA9571AB429BAB47C56A65_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float ___0_coefficientA, float ___1_coefficientB, float ___2_coefficientC, float ___3_coefficientD, const RuntimeMethod* method) ;
// System.Void Unity.Mathematics.float4::.ctor(Unity.Mathematics.float3,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_xyz, float ___1_w, const RuntimeMethod* method) ;
// System.Void Unity.Mathematics.Geometry.Plane::.ctor(Unity.Mathematics.float3,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Plane__ctor_mAEEAADCE34CB243E12A9FE0240D4E508913C1153_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_normal, float ___1_distance, const RuntimeMethod* method) ;
// System.Void Unity.Mathematics.Geometry.Plane::.ctor(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Plane__ctor_m645C0F13FB29D9E443284F1BC42C02CE3B5C27D4_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_normal, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_pointInPlane, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.math::cross(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) ;
// System.Void Unity.Mathematics.Geometry.Plane::.ctor(Unity.Mathematics.float3,Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Plane__ctor_m77B64CCE37D396DD70CD0A841F4E6E4F72D1B20A_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_vector1InPlane, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_vector2InPlane, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___2_pointInPlane, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.Geometry.Plane::get_Normal()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E Plane_get_Normal_mAA5C1BEAEFB0848A4CD29E254CC9EF010DD6FE4B (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, const RuntimeMethod* method) ;
// System.Void Unity.Mathematics.float4::set_xyz(Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float4_set_xyz_m331D16059D51A5C6CA8AE8FD1E13A68C0570A9C7_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_value, const RuntimeMethod* method) ;
// System.Void Unity.Mathematics.Geometry.Plane::set_Normal(Unity.Mathematics.float3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane_set_Normal_m88265A5E767B48CF718AC03AB03BBF15DC82A837 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_value, const RuntimeMethod* method) ;
// System.Single Unity.Mathematics.Geometry.Plane::get_Distance()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Plane_get_Distance_m66B8C8674B20E3B19B0CFD363D33AA8A67CA75FE (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, const RuntimeMethod* method) ;
// System.Void Unity.Mathematics.Geometry.Plane::set_Distance(System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane_set_Distance_m6DDC9F56E9FEE8D4DC5A61AB2A8322695F855181 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float ___0_value, const RuntimeMethod* method) ;
// System.Single Unity.Mathematics.math::lengthsq(Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_lengthsq_mC699F3F214F05B26BEBAF1B46E3AA3C00407A532_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, const RuntimeMethod* method) ;
// System.Single Unity.Mathematics.math::rsqrt(System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_rsqrt_mC67B3430EAADA7C5347E87B23859C569BC010E72_inline (float ___0_x, const RuntimeMethod* method) ;
// Unity.Mathematics.float4 Unity.Mathematics.float4::op_Multiply(Unity.Mathematics.float4,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E float4_op_Multiply_m712573F441DA8AF0843DE2167927FB76E642B1EB_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_lhs, float ___1_rhs, const RuntimeMethod* method) ;
// Unity.Mathematics.float4 Unity.Mathematics.Geometry.Plane::op_Implicit(Unity.Mathematics.Geometry.Plane)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E Plane_op_Implicit_mD02477CC24787906751D3F5E401D2E11BF99AC98_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 ___0_plane, const RuntimeMethod* method) ;
// System.Single Unity.Mathematics.math::dot(Unity.Mathematics.float4,Unity.Mathematics.float4)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_dot_m20F2285F7227DC308D9CF2DCB8EAAD3E774501D4_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_x, float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___1_y, const RuntimeMethod* method) ;
// System.Single Unity.Mathematics.Geometry.Plane::SignedDistanceToPoint(Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Plane_SignedDistanceToPoint_mE52778BC70A3A0FF9DDB0FE52D71C587D837F993_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.Geometry.Plane::Projection(Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E Plane_Projection_mFF8C23401C366A3B4EB017B4DAAAF4E8A9132CFE_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) ;
// Unity.Mathematics.float4 Unity.Mathematics.float4::op_UnaryNegation(Unity.Mathematics.float4)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E float4_op_UnaryNegation_m5A491FC1978650D62EBEDC51992CF4B2113C8C5B_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_val, const RuntimeMethod* method) ;
// Unity.Mathematics.Geometry.Plane Unity.Mathematics.Geometry.Plane::get_Flipped()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 Plane_get_Flipped_m6D004985368EE6234BA9A5D2800557FFB3A351FA (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, const RuntimeMethod* method) ;
// System.Void System.ArgumentException::.ctor(System.String)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentException__ctor_m026938A67AF9D36BB7ED27F80425D7194B514465 (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* __this, String_t* ___0_message, const RuntimeMethod* method) ;
// System.Void Unity.Mathematics.Geometry.Plane::CheckPlaneIsNormalized()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane_CheckPlaneIsNormalized_m6408EC4BE1D7A0ADB6832288E286D9C70404B888 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, const RuntimeMethod* method) ;
// System.Void Unity.Mathematics.bool3::.ctor(System.Boolean,System.Boolean,System.Boolean)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2_inline (bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861* __this, bool ___0_x, bool ___1_y, bool ___2_z, const RuntimeMethod* method) ;
// System.Single Unity.Mathematics.math::min(System.Single,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline (float ___0_x, float ___1_y, const RuntimeMethod* method) ;
// System.Single Unity.Mathematics.math::max(System.Single,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline (float ___0_x, float ___1_y, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.float3::op_Multiply(System.Single,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350_inline (float ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) ;
// Unity.Mathematics.uint3 Unity.Mathematics.math::asuint(Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B math_asuint_m4AEE8C17FEDA05D4C77C427818D1C9EF5E31521E_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, const RuntimeMethod* method) ;
// Unity.Mathematics.uint3 Unity.Mathematics.uint3::op_BitwiseAnd(Unity.Mathematics.uint3,System.UInt32)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B uint3_op_BitwiseAnd_m772BFC3A60526C264937ABCA92F1CAAFC2B0D634_inline (uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.math::asfloat(Unity.Mathematics.uint3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_asfloat_m7A90E1FAABD250FCEC00839D01B098BB046F7933_inline (uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B ___0_x, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.math::mul(Unity.Mathematics.quaternion,Unity.Mathematics.float3)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_mul_mE9E04B2868E4D4BA5BD873E4F876D550D36C2E99_inline (quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 ___0_q, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_v, const RuntimeMethod* method) ;
// System.Single Unity.Mathematics.math::sqrt(System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_sqrt_mEF31DE7BD0179009683C5D7B0C58E6571B30CF4A_inline (float ___0_x, const RuntimeMethod* method) ;
// System.Boolean System.Single::IsNaN(System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Single_IsNaN_mFE637F6ECA9F7697CE8EFF56427858F4C5EDF75D_inline (float ___0_f, const RuntimeMethod* method) ;
// System.UInt32 Unity.Mathematics.math::asuint(System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint32_t math_asuint_m503D1ABF19E4BA615FD8AE1BF1A2E103BBED6139_inline (float ___0_x, const RuntimeMethod* method) ;
// Unity.Mathematics.uint3 Unity.Mathematics.math::uint3(System.UInt32,System.UInt32,System.UInt32)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B math_uint3_mC94DDA8B357EA045D5A36B81CECD0C5C223B71B0_inline (uint32_t ___0_x, uint32_t ___1_y, uint32_t ___2_z, const RuntimeMethod* method) ;
// System.Void Unity.Mathematics.uint3::.ctor(System.UInt32,System.UInt32,System.UInt32)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF_inline (uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B* __this, uint32_t ___0_x, uint32_t ___1_y, uint32_t ___2_z, const RuntimeMethod* method) ;
// System.Single Unity.Mathematics.math::asfloat(System.UInt32)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_asfloat_m20D259DAAB46464B59BD8BF5678F9D59800F70A9_inline (uint32_t ___0_x, const RuntimeMethod* method) ;
// Unity.Mathematics.float3 Unity.Mathematics.math::float3(System.Single,System.Single,System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_float3_m4F96A74FEE1D6C85241B8E62386C5DE1C439837F_inline (float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) ;
// System.Int32 System.BitConverter::SingleToInt32Bits(System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t BitConverter_SingleToInt32Bits_mC760C7CFC89725E3CF68DC45BE3A9A42A7E7DA73_inline (float ___0_value, const RuntimeMethod* method) ;
// System.Int32 Unity.Mathematics.math::asint(System.Single)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t math_asint_mBDED7FE966CA65F6A8ACEAEF8FD779B1B8998288_inline (float ___0_x, const RuntimeMethod* method) ;
// System.Single Unity.Mathematics.math::asfloat(System.Int32)
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_asfloat_m9FA56DE5C61FCEF3DCD0675252D40DFD9C9B712F_inline (int32_t ___0_x, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void Unity.Mathematics.Geometry.MinMaxAABB::.ctor(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_min, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_max, const RuntimeMethod* method) 
{
	{
		// Min = min;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_min;
		__this->___Min_0 = L_0;
		// Max = max;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___1_max;
		__this->___Max_1 = L_1;
		// }
		return;
	}
}
IL2CPP_EXTERN_C  void MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_min, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_max, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_inline(_thisAdjusted, ___0_min, ___1_max, method);
}
// Unity.Mathematics.Geometry.MinMaxAABB Unity.Mathematics.Geometry.MinMaxAABB::CreateFromCenterAndExtents(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 MinMaxAABB_CreateFromCenterAndExtents_mD8638596E84DB7E4E76C346D75B707EBE96AD411 (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_center, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_extents, const RuntimeMethod* method) 
{
	{
		// return CreateFromCenterAndHalfExtents(center, extents * 0.5f);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_center;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___1_extents;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81_inline(L_1, (0.5f), NULL);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_3;
		L_3 = MinMaxAABB_CreateFromCenterAndHalfExtents_mF18074A621916107BF592378F72B6F113294F508_inline(L_0, L_2, NULL);
		return L_3;
	}
}
// Unity.Mathematics.Geometry.MinMaxAABB Unity.Mathematics.Geometry.MinMaxAABB::CreateFromCenterAndHalfExtents(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 MinMaxAABB_CreateFromCenterAndHalfExtents_mF18074A621916107BF592378F72B6F113294F508 (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_center, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_halfExtents, const RuntimeMethod* method) 
{
	{
		// return new MinMaxAABB(center - halfExtents, center + halfExtents);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_center;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___1_halfExtents;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___0_center;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___1_halfExtents;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_3, L_4, NULL);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_6;
		memset((&L_6), 0, sizeof(L_6));
		MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_inline((&L_6), L_2, L_5, /*hidden argument*/NULL);
		return L_6;
	}
}
// Unity.Mathematics.float3 Unity.Mathematics.Geometry.MinMaxAABB::get_Extents()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_Extents_m7E246B61BA832B3FB5EB8DCA4A35EC93688F105C (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) 
{
	{
		// public float3 Extents => Max - Min;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Max_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = __this->___Min_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C  float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_Extents_m7E246B61BA832B3FB5EB8DCA4A35EC93688F105C_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E _returnValue;
	_returnValue = MinMaxAABB_get_Extents_m7E246B61BA832B3FB5EB8DCA4A35EC93688F105C(_thisAdjusted, method);
	return _returnValue;
}
// Unity.Mathematics.float3 Unity.Mathematics.Geometry.MinMaxAABB::get_HalfExtents()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_HalfExtents_m3656E833DBE99FE9D023F0E4B1497CF35F6BC948 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) 
{
	{
		// public float3 HalfExtents => (Max - Min) * 0.5f;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Max_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = __this->___Min_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81_inline(L_2, (0.5f), NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C  float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_HalfExtents_m3656E833DBE99FE9D023F0E4B1497CF35F6BC948_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E _returnValue;
	_returnValue = MinMaxAABB_get_HalfExtents_m3656E833DBE99FE9D023F0E4B1497CF35F6BC948(_thisAdjusted, method);
	return _returnValue;
}
// Unity.Mathematics.float3 Unity.Mathematics.Geometry.MinMaxAABB::get_Center()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_Center_mC27A51CF13A95A053DEA07566A1F1900CFD98F4C (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) 
{
	{
		// public float3 Center => (Max + Min) * 0.5f;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Max_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = __this->___Min_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81_inline(L_2, (0.5f), NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C  float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E MinMaxAABB_get_Center_mC27A51CF13A95A053DEA07566A1F1900CFD98F4C_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E _returnValue;
	_returnValue = MinMaxAABB_get_Center_mC27A51CF13A95A053DEA07566A1F1900CFD98F4C(_thisAdjusted, method);
	return _returnValue;
}
// System.Boolean Unity.Mathematics.Geometry.MinMaxAABB::get_IsValid()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MinMaxAABB_get_IsValid_m84D96167E3AE9516D86FEC54B1CD138BD53FC5B8 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) 
{
	{
		// public bool IsValid => math.all(Min <= Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = __this->___Max_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_2;
		L_2 = float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline(L_0, L_1, NULL);
		bool L_3;
		L_3 = math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline(L_2, NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C  bool MinMaxAABB_get_IsValid_m84D96167E3AE9516D86FEC54B1CD138BD53FC5B8_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	bool _returnValue;
	_returnValue = MinMaxAABB_get_IsValid_m84D96167E3AE9516D86FEC54B1CD138BD53FC5B8(_thisAdjusted, method);
	return _returnValue;
}
// System.Single Unity.Mathematics.Geometry.MinMaxAABB::get_SurfaceArea()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float MinMaxAABB_get_SurfaceArea_m542F0EA64B3A4C7975BAB0C3EBD354D79F34B1CE (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		// float3 diff = Max - Min;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Max_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = __this->___Min_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_0, L_1, NULL);
		V_0 = L_2;
		// return 2 * math.dot(diff, diff.yzx);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4;
		L_4 = float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_inline((&V_0), NULL);
		float L_5;
		L_5 = math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD_inline(L_3, L_4, NULL);
		return ((float)il2cpp_codegen_multiply((2.0f), L_5));
	}
}
IL2CPP_EXTERN_C  float MinMaxAABB_get_SurfaceArea_m542F0EA64B3A4C7975BAB0C3EBD354D79F34B1CE_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	float _returnValue;
	_returnValue = MinMaxAABB_get_SurfaceArea_m542F0EA64B3A4C7975BAB0C3EBD354D79F34B1CE(_thisAdjusted, method);
	return _returnValue;
}
// System.Boolean Unity.Mathematics.Geometry.MinMaxAABB::Contains(Unity.Mathematics.float3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MinMaxAABB_Contains_m507D89756D76CA0E2CBD8CAD23C6CE27A5F39089 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) 
{
	{
		// public bool Contains(float3 point) => math.all(point >= Min & point <= Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = __this->___Min_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_2;
		L_2 = float3_op_GreaterThanOrEqual_m01767B59951623AD803736AB63E12D9BC6FC1AAE_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = __this->___Max_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_5;
		L_5 = float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline(L_3, L_4, NULL);
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_6;
		L_6 = bool3_op_BitwiseAnd_mE44CC5838094A40F1F605A7798994197165A63E1_inline(L_2, L_5, NULL);
		bool L_7;
		L_7 = math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline(L_6, NULL);
		return L_7;
	}
}
IL2CPP_EXTERN_C  bool MinMaxAABB_Contains_m507D89756D76CA0E2CBD8CAD23C6CE27A5F39089_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	bool _returnValue;
	_returnValue = MinMaxAABB_Contains_m507D89756D76CA0E2CBD8CAD23C6CE27A5F39089_inline(_thisAdjusted, ___0_point, method);
	return _returnValue;
}
// System.Boolean Unity.Mathematics.Geometry.MinMaxAABB::Contains(Unity.Mathematics.Geometry.MinMaxAABB)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MinMaxAABB_Contains_m8AB762A87FD22983A2A5ED2C96D3C559876D8823 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) 
{
	{
		// public bool Contains(MinMaxAABB aabb) => math.all((Min <= aabb.Min) & (Max >= aabb.Max));
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min_0;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_1 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = L_1.___Min_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_3;
		L_3 = float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline(L_0, L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = __this->___Max_1;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_5 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = L_5.___Max_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_7;
		L_7 = float3_op_GreaterThanOrEqual_m01767B59951623AD803736AB63E12D9BC6FC1AAE_inline(L_4, L_6, NULL);
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_8;
		L_8 = bool3_op_BitwiseAnd_mE44CC5838094A40F1F605A7798994197165A63E1_inline(L_3, L_7, NULL);
		bool L_9;
		L_9 = math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline(L_8, NULL);
		return L_9;
	}
}
IL2CPP_EXTERN_C  bool MinMaxAABB_Contains_m8AB762A87FD22983A2A5ED2C96D3C559876D8823_AdjustorThunk (RuntimeObject* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	bool _returnValue;
	_returnValue = MinMaxAABB_Contains_m8AB762A87FD22983A2A5ED2C96D3C559876D8823_inline(_thisAdjusted, ___0_aabb, method);
	return _returnValue;
}
// System.Boolean Unity.Mathematics.Geometry.MinMaxAABB::Overlaps(Unity.Mathematics.Geometry.MinMaxAABB)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MinMaxAABB_Overlaps_mEEC801434D524543F9E6D5D77A642A5184F8C4D9 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) 
{
	{
		// return math.all(Max >= aabb.Min & Min <= aabb.Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Max_1;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_1 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = L_1.___Min_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_3;
		L_3 = float3_op_GreaterThanOrEqual_m01767B59951623AD803736AB63E12D9BC6FC1AAE_inline(L_0, L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = __this->___Min_0;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_5 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = L_5.___Max_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_7;
		L_7 = float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline(L_4, L_6, NULL);
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_8;
		L_8 = bool3_op_BitwiseAnd_mE44CC5838094A40F1F605A7798994197165A63E1_inline(L_3, L_7, NULL);
		bool L_9;
		L_9 = math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline(L_8, NULL);
		return L_9;
	}
}
IL2CPP_EXTERN_C  bool MinMaxAABB_Overlaps_mEEC801434D524543F9E6D5D77A642A5184F8C4D9_AdjustorThunk (RuntimeObject* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	bool _returnValue;
	_returnValue = MinMaxAABB_Overlaps_mEEC801434D524543F9E6D5D77A642A5184F8C4D9_inline(_thisAdjusted, ___0_aabb, method);
	return _returnValue;
}
// System.Void Unity.Mathematics.Geometry.MinMaxAABB::Expand(System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MinMaxAABB_Expand_m8574B6375684AC91E413410881D03B71C400DC75 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float ___0_signedDistance, const RuntimeMethod* method) 
{
	{
		// Min -= signedDistance;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min_0;
		float L_1 = ___0_signedDistance;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Subtraction_m111BEEA770E140739DDC6A3410736DFF7EE32045_inline(L_0, L_1, NULL);
		__this->___Min_0 = L_2;
		// Max += signedDistance;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = __this->___Max_1;
		float L_4 = ___0_signedDistance;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = float3_op_Addition_mABF24BC9A16C272B9F5AB21A601B9D9A831F8C43_inline(L_3, L_4, NULL);
		__this->___Max_1 = L_5;
		// }
		return;
	}
}
IL2CPP_EXTERN_C  void MinMaxAABB_Expand_m8574B6375684AC91E413410881D03B71C400DC75_AdjustorThunk (RuntimeObject* __this, float ___0_signedDistance, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	MinMaxAABB_Expand_m8574B6375684AC91E413410881D03B71C400DC75_inline(_thisAdjusted, ___0_signedDistance, method);
}
// System.Void Unity.Mathematics.Geometry.MinMaxAABB::Encapsulate(Unity.Mathematics.Geometry.MinMaxAABB)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MinMaxAABB_Encapsulate_m7043627B976BE96D6D734288EEA09563352F753B (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) 
{
	{
		// Min = math.min(Min, aabb.Min);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min_0;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_1 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = L_1.___Min_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = math_min_m13CC8D5B7844D954C3125DD72831C693AB8A7FF5_inline(L_0, L_2, NULL);
		__this->___Min_0 = L_3;
		// Max = math.max(Max, aabb.Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = __this->___Max_1;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_5 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = L_5.___Max_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_7;
		L_7 = math_max_m247D41258606F80861E72309300DF6A3F8B50AE4_inline(L_4, L_6, NULL);
		__this->___Max_1 = L_7;
		// }
		return;
	}
}
IL2CPP_EXTERN_C  void MinMaxAABB_Encapsulate_m7043627B976BE96D6D734288EEA09563352F753B_AdjustorThunk (RuntimeObject* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	MinMaxAABB_Encapsulate_m7043627B976BE96D6D734288EEA09563352F753B_inline(_thisAdjusted, ___0_aabb, method);
}
// System.Void Unity.Mathematics.Geometry.MinMaxAABB::Encapsulate(Unity.Mathematics.float3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MinMaxAABB_Encapsulate_mE01CED4767A6B50D846AC30AFCA49A91BA820CD5 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) 
{
	{
		// Min = math.min(Min, point);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = math_min_m13CC8D5B7844D954C3125DD72831C693AB8A7FF5_inline(L_0, L_1, NULL);
		__this->___Min_0 = L_2;
		// Max = math.max(Max, point);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = __this->___Max_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = math_max_m247D41258606F80861E72309300DF6A3F8B50AE4_inline(L_3, L_4, NULL);
		__this->___Max_1 = L_5;
		// }
		return;
	}
}
IL2CPP_EXTERN_C  void MinMaxAABB_Encapsulate_mE01CED4767A6B50D846AC30AFCA49A91BA820CD5_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	MinMaxAABB_Encapsulate_mE01CED4767A6B50D846AC30AFCA49A91BA820CD5_inline(_thisAdjusted, ___0_point, method);
}
// System.Boolean Unity.Mathematics.Geometry.MinMaxAABB::Equals(Unity.Mathematics.Geometry.MinMaxAABB)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool MinMaxAABB_Equals_m6DC492AB1804679250EC6C97CC6DF32299EA8E11 (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_other, const RuntimeMethod* method) 
{
	{
		// return Min.Equals(other.Min) && Max.Equals(other.Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_0 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&__this->___Min_0);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_1 = ___0_other;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = L_1.___Min_0;
		bool L_3;
		L_3 = float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_inline(L_0, L_2, NULL);
		if (!L_3)
		{
			goto IL_0025;
		}
	}
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_4 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&__this->___Max_1);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_5 = ___0_other;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = L_5.___Max_1;
		bool L_7;
		L_7 = float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_inline(L_4, L_6, NULL);
		return L_7;
	}

IL_0025:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C  bool MinMaxAABB_Equals_m6DC492AB1804679250EC6C97CC6DF32299EA8E11_AdjustorThunk (RuntimeObject* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_other, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	bool _returnValue;
	_returnValue = MinMaxAABB_Equals_m6DC492AB1804679250EC6C97CC6DF32299EA8E11_inline(_thisAdjusted, ___0_other, method);
	return _returnValue;
}
// System.String Unity.Mathematics.Geometry.MinMaxAABB::ToString()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* MinMaxAABB_ToString_mD299FEC6092F6072F7BC91773DA766E0E61DEC3F (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFEB995DBF7004A48F3EDB181C599FB99B4A380FB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		// return string.Format("MinMaxAABB({0}, {1})", Min, Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = L_0;
		RuntimeObject* L_2 = Box(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E_il2cpp_TypeInfo_var, &L_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = __this->___Max_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = L_3;
		RuntimeObject* L_5 = Box(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E_il2cpp_TypeInfo_var, &L_4);
		String_t* L_6;
		L_6 = String_Format_mFB7DA489BD99F4670881FF50EC017BFB0A5C0987(_stringLiteralFEB995DBF7004A48F3EDB181C599FB99B4A380FB, L_2, L_5, NULL);
		return L_6;
	}
}
IL2CPP_EXTERN_C  String_t* MinMaxAABB_ToString_mD299FEC6092F6072F7BC91773DA766E0E61DEC3F_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = MinMaxAABB_ToString_mD299FEC6092F6072F7BC91773DA766E0E61DEC3F_inline(_thisAdjusted, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// Unity.Mathematics.Geometry.MinMaxAABB Unity.Mathematics.Geometry.Math::Transform(Unity.Mathematics.RigidTransform,Unity.Mathematics.Geometry.MinMaxAABB)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 Math_Transform_m412573C4965FDBD6C72DB99EA7B13301A5C7F068 (RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD ___0_transform, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___1_aabb, const RuntimeMethod* method) 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_0;
	memset((&V_0), 0, sizeof(V_0));
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_1;
	memset((&V_1), 0, sizeof(V_1));
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_2;
	memset((&V_2), 0, sizeof(V_2));
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_3;
	memset((&V_3), 0, sizeof(V_3));
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_4;
	memset((&V_4), 0, sizeof(V_4));
	{
		// float3 halfExtentsInA = aabb.HalfExtents;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0;
		L_0 = MinMaxAABB_get_HalfExtents_m3656E833DBE99FE9D023F0E4B1497CF35F6BC948((&___1_aabb), NULL);
		V_0 = L_0;
		// float3 x = math.rotate(transform.rot, new float3(halfExtentsInA.x, 0, 0));
		RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD L_1 = ___0_transform;
		quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 L_2 = L_1.___rot_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = V_0;
		float L_4 = L_3.___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		memset((&L_5), 0, sizeof(L_5));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_5), L_4, (0.0f), (0.0f), /*hidden argument*/NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6;
		L_6 = math_rotate_m68CD27B1D0643EA356D0AB41ECB004CE094FDA3F_inline(L_2, L_5, NULL);
		// float3 y = math.rotate(transform.rot, new float3(0, halfExtentsInA.y, 0));
		RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD L_7 = ___0_transform;
		quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 L_8 = L_7.___rot_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9 = V_0;
		float L_10 = L_9.___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_11;
		memset((&L_11), 0, sizeof(L_11));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_11), (0.0f), L_10, (0.0f), /*hidden argument*/NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_12;
		L_12 = math_rotate_m68CD27B1D0643EA356D0AB41ECB004CE094FDA3F_inline(L_8, L_11, NULL);
		V_1 = L_12;
		// float3 z = math.rotate(transform.rot, new float3(0, 0, halfExtentsInA.z));
		RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD L_13 = ___0_transform;
		quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 L_14 = L_13.___rot_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_15 = V_0;
		float L_16 = L_15.___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_17;
		memset((&L_17), 0, sizeof(L_17));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_17), (0.0f), (0.0f), L_16, /*hidden argument*/NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_18;
		L_18 = math_rotate_m68CD27B1D0643EA356D0AB41ECB004CE094FDA3F_inline(L_14, L_17, NULL);
		V_2 = L_18;
		// float3 halfExtentsInB = math.abs(x) + math.abs(y) + math.abs(z);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_19;
		L_19 = math_abs_mC7F2BBD861835C82A0A47A47A44B73E704D7F63B_inline(L_6, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_20 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_21;
		L_21 = math_abs_mC7F2BBD861835C82A0A47A47A44B73E704D7F63B_inline(L_20, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_22;
		L_22 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_19, L_21, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_23 = V_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_24;
		L_24 = math_abs_mC7F2BBD861835C82A0A47A47A44B73E704D7F63B_inline(L_23, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_25;
		L_25 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_22, L_24, NULL);
		V_3 = L_25;
		// float3 centerInB = math.transform(transform, aabb.Center);
		RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD L_26 = ___0_transform;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_27;
		L_27 = MinMaxAABB_get_Center_mC27A51CF13A95A053DEA07566A1F1900CFD98F4C((&___1_aabb), NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_28;
		L_28 = math_transform_m5F6B69A9C0E6E1AF63D8112D8753394891972E44_inline(L_26, L_27, NULL);
		V_4 = L_28;
		// return new MinMaxAABB(centerInB - halfExtentsInB, centerInB + halfExtentsInB);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_29 = V_4;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_30 = V_3;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_31;
		L_31 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_29, L_30, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_32 = V_4;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_33 = V_3;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_34;
		L_34 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_32, L_33, NULL);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_35;
		memset((&L_35), 0, sizeof(L_35));
		MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_inline((&L_35), L_31, L_34, /*hidden argument*/NULL);
		return L_35;
	}
}
// Unity.Mathematics.Geometry.MinMaxAABB Unity.Mathematics.Geometry.Math::Transform(Unity.Mathematics.float4x4,Unity.Mathematics.Geometry.MinMaxAABB)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 Math_Transform_m3E33DC460F20ED83A2964AF47CCDD5C6866DA7E8 (float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 ___0_transform, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___1_aabb, const RuntimeMethod* method) 
{
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		// var transformed = Transform(new float3x3(transform), aabb);
		float4x4_t7EDD16F7F57DC7F61A6302535F7C19FB97915DF2 L_0 = ___0_transform;
		float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79 L_1;
		memset((&L_1), 0, sizeof(L_1));
		float3x3__ctor_m3EA9552B4922CACBAC36054687E8EF9C1ED99951((&L_1), L_0, /*hidden argument*/NULL);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_2 = ___1_aabb;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_3;
		L_3 = Math_Transform_m762480F0988F685C5039CBFBFA540C85385A53CC_inline(L_1, L_2, NULL);
		V_0 = L_3;
		// transformed.Min += transform.c3.xyz;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_4 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_0)->___Min_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_5 = L_4;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_5);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_7 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&(&___0_transform)->___c3_3);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8;
		L_8 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline(L_7, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9;
		L_9 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_6, L_8, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_5 = L_9;
		// transformed.Max += transform.c3.xyz;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_10 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_0)->___Max_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_11 = L_10;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_12 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_11);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_13 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&(&___0_transform)->___c3_3);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_14;
		L_14 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline(L_13, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_15;
		L_15 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_12, L_14, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_11 = L_15;
		// return transformed;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_16 = V_0;
		return L_16;
	}
}
// Unity.Mathematics.Geometry.MinMaxAABB Unity.Mathematics.Geometry.Math::Transform(Unity.Mathematics.float3x3,Unity.Mathematics.Geometry.MinMaxAABB)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 Math_Transform_m762480F0988F685C5039CBFBFA540C85385A53CC (float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79 ___0_transform, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___1_aabb, const RuntimeMethod* method) 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_0;
	memset((&V_0), 0, sizeof(V_0));
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_1;
	memset((&V_1), 0, sizeof(V_1));
	bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 V_2;
	memset((&V_2), 0, sizeof(V_2));
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 V_3;
	memset((&V_3), 0, sizeof(V_3));
	{
		// var t1 = transform.c0.xyz * aabb.Min.xxx;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_0 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c0_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_0, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_2 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Min_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = float3_get_xxx_mFD7DFB9FF23BB0B3437F12CC35DB3D1E0ADF7B20_inline(L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4;
		L_4 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_1, L_3, NULL);
		V_0 = L_4;
		// var t2 = transform.c0.xyz * aabb.Max.xxx;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_5 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c0_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6;
		L_6 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_5, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_7 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Max_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8;
		L_8 = float3_get_xxx_mFD7DFB9FF23BB0B3437F12CC35DB3D1E0ADF7B20_inline(L_7, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9;
		L_9 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_6, L_8, NULL);
		V_1 = L_9;
		// var minMask = t1 < t2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_11 = V_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_12;
		L_12 = float3_op_LessThan_m540182EDEF57B5A865B1C22972CF5C3E862B9C51_inline(L_10, L_11, NULL);
		V_2 = L_12;
		// var transformed = new MinMaxAABB(select(t2, t1, minMask), select(t2, t1, !minMask));
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_13 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_14 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_15 = V_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_16;
		L_16 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_13, L_14, L_15, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_17 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_18 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_19 = V_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_20;
		L_20 = bool3_op_LogicalNot_m85C703CC4098B3731505A162957F91C0373548BD_inline(L_19, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_21;
		L_21 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_17, L_18, L_20, NULL);
		MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_inline((&V_3), L_16, L_21, NULL);
		// t1 = transform.c1.xyz * aabb.Min.yyy;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_22 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c1_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_23;
		L_23 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_22, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_24 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Min_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_25;
		L_25 = float3_get_yyy_m6FCA12991237EDC77F7C4B6A7F73710338330CCD_inline(L_24, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_26;
		L_26 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_23, L_25, NULL);
		V_0 = L_26;
		// t2 = transform.c1.xyz * aabb.Max.yyy;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_27 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c1_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_28;
		L_28 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_27, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_29 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Max_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_30;
		L_30 = float3_get_yyy_m6FCA12991237EDC77F7C4B6A7F73710338330CCD_inline(L_29, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_31;
		L_31 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_28, L_30, NULL);
		V_1 = L_31;
		// minMask = t1 < t2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_32 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_33 = V_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_34;
		L_34 = float3_op_LessThan_m540182EDEF57B5A865B1C22972CF5C3E862B9C51_inline(L_32, L_33, NULL);
		V_2 = L_34;
		// transformed.Min += select(t2, t1, minMask);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_35 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_3)->___Min_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_36 = L_35;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_37 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_36);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_38 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_39 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_40 = V_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_41;
		L_41 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_38, L_39, L_40, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_42;
		L_42 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_37, L_41, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_36 = L_42;
		// transformed.Max += select(t2, t1, !minMask);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_43 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_3)->___Max_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_44 = L_43;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_45 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_44);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_46 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_47 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_48 = V_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_49;
		L_49 = bool3_op_LogicalNot_m85C703CC4098B3731505A162957F91C0373548BD_inline(L_48, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_50;
		L_50 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_46, L_47, L_49, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_51;
		L_51 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_45, L_50, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_44 = L_51;
		// t1 = transform.c2.xyz * aabb.Min.zzz;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_52 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c2_2);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_53;
		L_53 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_52, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_54 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Min_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_55;
		L_55 = float3_get_zzz_m1C7C995F170030A7EF534E2C99E6AFE6928AE9D4_inline(L_54, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_56;
		L_56 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_53, L_55, NULL);
		V_0 = L_56;
		// t2 = transform.c2.xyz * aabb.Max.zzz;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_57 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c2_2);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_58;
		L_58 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_57, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_59 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Max_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_60;
		L_60 = float3_get_zzz_m1C7C995F170030A7EF534E2C99E6AFE6928AE9D4_inline(L_59, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_61;
		L_61 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_58, L_60, NULL);
		V_1 = L_61;
		// minMask = t1 < t2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_62 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_63 = V_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_64;
		L_64 = float3_op_LessThan_m540182EDEF57B5A865B1C22972CF5C3E862B9C51_inline(L_62, L_63, NULL);
		V_2 = L_64;
		// transformed.Min += select(t2, t1, minMask);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_65 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_3)->___Min_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_66 = L_65;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_67 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_66);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_68 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_69 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_70 = V_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_71;
		L_71 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_68, L_69, L_70, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_72;
		L_72 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_67, L_71, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_66 = L_72;
		// transformed.Max += select(t2, t1, !minMask);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_73 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_3)->___Max_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_74 = L_73;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_75 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_74);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_76 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_77 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_78 = V_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_79;
		L_79 = bool3_op_LogicalNot_m85C703CC4098B3731505A162957F91C0373548BD_inline(L_78, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_80;
		L_80 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_76, L_77, L_79, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_81;
		L_81 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_75, L_80, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_74 = L_81;
		// return transformed;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_82 = V_3;
		return L_82;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
// System.Void Unity.Mathematics.Geometry.Plane::.ctor(System.Single,System.Single,System.Single,System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane__ctor_mE41939B3E3E2AE7802AA9571AB429BAB47C56A65 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float ___0_coefficientA, float ___1_coefficientB, float ___2_coefficientC, float ___3_coefficientD, const RuntimeMethod* method) 
{
	{
		// NormalAndDistance = Normalize(new float4(coefficientA, coefficientB, coefficientC, coefficientD));
		float L_0 = ___0_coefficientA;
		float L_1 = ___1_coefficientB;
		float L_2 = ___2_coefficientC;
		float L_3 = ___3_coefficientD;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_4;
		memset((&L_4), 0, sizeof(L_4));
		float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D_inline((&L_4), L_0, L_1, L_2, L_3, /*hidden argument*/NULL);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_5;
		L_5 = Plane_Normalize_mFF53F95372AE2A79C71B546A70B0F152EC308544_inline(L_4, NULL);
		__this->___NormalAndDistance_0 = L_5;
		// }
		return;
	}
}
IL2CPP_EXTERN_C  void Plane__ctor_mE41939B3E3E2AE7802AA9571AB429BAB47C56A65_AdjustorThunk (RuntimeObject* __this, float ___0_coefficientA, float ___1_coefficientB, float ___2_coefficientC, float ___3_coefficientD, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	Plane__ctor_mE41939B3E3E2AE7802AA9571AB429BAB47C56A65_inline(_thisAdjusted, ___0_coefficientA, ___1_coefficientB, ___2_coefficientC, ___3_coefficientD, method);
}
// System.Void Unity.Mathematics.Geometry.Plane::.ctor(Unity.Mathematics.float3,System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane__ctor_mAEEAADCE34CB243E12A9FE0240D4E508913C1153 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_normal, float ___1_distance, const RuntimeMethod* method) 
{
	{
		// NormalAndDistance = Normalize(new float4(normal, distance));
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_normal;
		float L_1 = ___1_distance;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_2;
		memset((&L_2), 0, sizeof(L_2));
		float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_inline((&L_2), L_0, L_1, /*hidden argument*/NULL);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_3;
		L_3 = Plane_Normalize_mFF53F95372AE2A79C71B546A70B0F152EC308544_inline(L_2, NULL);
		__this->___NormalAndDistance_0 = L_3;
		// }
		return;
	}
}
IL2CPP_EXTERN_C  void Plane__ctor_mAEEAADCE34CB243E12A9FE0240D4E508913C1153_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_normal, float ___1_distance, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	Plane__ctor_mAEEAADCE34CB243E12A9FE0240D4E508913C1153_inline(_thisAdjusted, ___0_normal, ___1_distance, method);
}
// System.Void Unity.Mathematics.Geometry.Plane::.ctor(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane__ctor_m645C0F13FB29D9E443284F1BC42C02CE3B5C27D4 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_normal, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_pointInPlane, const RuntimeMethod* method) 
{
	{
		// : this(normal, -math.dot(normal, pointInPlane))
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_normal;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_normal;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_pointInPlane;
		float L_3;
		L_3 = math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD_inline(L_1, L_2, NULL);
		Plane__ctor_mAEEAADCE34CB243E12A9FE0240D4E508913C1153_inline(__this, L_0, ((-L_3)), NULL);
		// }
		return;
	}
}
IL2CPP_EXTERN_C  void Plane__ctor_m645C0F13FB29D9E443284F1BC42C02CE3B5C27D4_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_normal, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_pointInPlane, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	Plane__ctor_m645C0F13FB29D9E443284F1BC42C02CE3B5C27D4_inline(_thisAdjusted, ___0_normal, ___1_pointInPlane, method);
}
// System.Void Unity.Mathematics.Geometry.Plane::.ctor(Unity.Mathematics.float3,Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane__ctor_m77B64CCE37D396DD70CD0A841F4E6E4F72D1B20A (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_vector1InPlane, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_vector2InPlane, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___2_pointInPlane, const RuntimeMethod* method) 
{
	{
		// : this(math.cross(vector1InPlane, vector2InPlane), pointInPlane)
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_vector1InPlane;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___1_vector2InPlane;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___2_pointInPlane;
		Plane__ctor_m645C0F13FB29D9E443284F1BC42C02CE3B5C27D4_inline(__this, L_2, L_3, NULL);
		// }
		return;
	}
}
IL2CPP_EXTERN_C  void Plane__ctor_m77B64CCE37D396DD70CD0A841F4E6E4F72D1B20A_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_vector1InPlane, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_vector2InPlane, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___2_pointInPlane, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	Plane__ctor_m77B64CCE37D396DD70CD0A841F4E6E4F72D1B20A_inline(_thisAdjusted, ___0_vector1InPlane, ___1_vector2InPlane, ___2_pointInPlane, method);
}
// Unity.Mathematics.Geometry.Plane Unity.Mathematics.Geometry.Plane::CreateFromUnitNormalAndDistance(Unity.Mathematics.float3,System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 Plane_CreateFromUnitNormalAndDistance_mBE602081B26029F3D51C338D9D94BBFBC27539A5 (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_unitNormal, float ___1_distance, const RuntimeMethod* method) 
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		// return new Plane { NormalAndDistance = new float4(unitNormal, distance) };
		il2cpp_codegen_initobj((&V_0), sizeof(Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82));
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_unitNormal;
		float L_1 = ___1_distance;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_2;
		memset((&L_2), 0, sizeof(L_2));
		float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_inline((&L_2), L_0, L_1, /*hidden argument*/NULL);
		(&V_0)->___NormalAndDistance_0 = L_2;
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_3 = V_0;
		return L_3;
	}
}
// Unity.Mathematics.Geometry.Plane Unity.Mathematics.Geometry.Plane::CreateFromUnitNormalAndPointInPlane(Unity.Mathematics.float3,Unity.Mathematics.float3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 Plane_CreateFromUnitNormalAndPointInPlane_m41976CA41AE866D620744C534DF0CEE994039D95 (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_unitNormal, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_pointInPlane, const RuntimeMethod* method) 
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		// return new Plane { NormalAndDistance = new float4(unitNormal, -math.dot(unitNormal, pointInPlane)) };
		il2cpp_codegen_initobj((&V_0), sizeof(Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82));
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_unitNormal;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_unitNormal;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_pointInPlane;
		float L_3;
		L_3 = math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD_inline(L_1, L_2, NULL);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_4;
		memset((&L_4), 0, sizeof(L_4));
		float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_inline((&L_4), L_0, ((-L_3)), /*hidden argument*/NULL);
		(&V_0)->___NormalAndDistance_0 = L_4;
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_5 = V_0;
		return L_5;
	}
}
// Unity.Mathematics.float3 Unity.Mathematics.Geometry.Plane::get_Normal()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E Plane_get_Normal_mAA5C1BEAEFB0848A4CD29E254CC9EF010DD6FE4B (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, const RuntimeMethod* method) 
{
	{
		// get => NormalAndDistance.xyz;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_0 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&__this->___NormalAndDistance_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C  float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E Plane_get_Normal_mAA5C1BEAEFB0848A4CD29E254CC9EF010DD6FE4B_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E _returnValue;
	_returnValue = Plane_get_Normal_mAA5C1BEAEFB0848A4CD29E254CC9EF010DD6FE4B(_thisAdjusted, method);
	return _returnValue;
}
// System.Void Unity.Mathematics.Geometry.Plane::set_Normal(Unity.Mathematics.float3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane_set_Normal_m88265A5E767B48CF718AC03AB03BBF15DC82A837 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_value, const RuntimeMethod* method) 
{
	{
		// set => NormalAndDistance.xyz = value;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_0 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&__this->___NormalAndDistance_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_value;
		float4_set_xyz_m331D16059D51A5C6CA8AE8FD1E13A68C0570A9C7_inline(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C  void Plane_set_Normal_m88265A5E767B48CF718AC03AB03BBF15DC82A837_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_value, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	Plane_set_Normal_m88265A5E767B48CF718AC03AB03BBF15DC82A837(_thisAdjusted, ___0_value, method);
}
// System.Single Unity.Mathematics.Geometry.Plane::get_Distance()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Plane_get_Distance_m66B8C8674B20E3B19B0CFD363D33AA8A67CA75FE (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, const RuntimeMethod* method) 
{
	{
		// get => NormalAndDistance.w;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_0 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&__this->___NormalAndDistance_0);
		float L_1 = L_0->___w_3;
		return L_1;
	}
}
IL2CPP_EXTERN_C  float Plane_get_Distance_m66B8C8674B20E3B19B0CFD363D33AA8A67CA75FE_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	float _returnValue;
	_returnValue = Plane_get_Distance_m66B8C8674B20E3B19B0CFD363D33AA8A67CA75FE(_thisAdjusted, method);
	return _returnValue;
}
// System.Void Unity.Mathematics.Geometry.Plane::set_Distance(System.Single)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane_set_Distance_m6DDC9F56E9FEE8D4DC5A61AB2A8322695F855181 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float ___0_value, const RuntimeMethod* method) 
{
	{
		// set => NormalAndDistance.w = value;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_0 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&__this->___NormalAndDistance_0);
		float L_1 = ___0_value;
		L_0->___w_3 = L_1;
		return;
	}
}
IL2CPP_EXTERN_C  void Plane_set_Distance_m6DDC9F56E9FEE8D4DC5A61AB2A8322695F855181_AdjustorThunk (RuntimeObject* __this, float ___0_value, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	Plane_set_Distance_m6DDC9F56E9FEE8D4DC5A61AB2A8322695F855181(_thisAdjusted, ___0_value, method);
}
// Unity.Mathematics.Geometry.Plane Unity.Mathematics.Geometry.Plane::Normalize(Unity.Mathematics.Geometry.Plane)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 Plane_Normalize_m620F20BC9EEECB986839A98C2166EC4B8D05E3E6 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 ___0_plane, const RuntimeMethod* method) 
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		// return new Plane { NormalAndDistance = Normalize(plane.NormalAndDistance) };
		il2cpp_codegen_initobj((&V_0), sizeof(Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82));
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_0 = ___0_plane;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_1 = L_0.___NormalAndDistance_0;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_2;
		L_2 = Plane_Normalize_mFF53F95372AE2A79C71B546A70B0F152EC308544_inline(L_1, NULL);
		(&V_0)->___NormalAndDistance_0 = L_2;
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_3 = V_0;
		return L_3;
	}
}
// Unity.Mathematics.float4 Unity.Mathematics.Geometry.Plane::Normalize(Unity.Mathematics.float4)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E Plane_Normalize_mFF53F95372AE2A79C71B546A70B0F152EC308544 (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_planeCoefficients, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 V_1;
	memset((&V_1), 0, sizeof(V_1));
	{
		// float recipLength = math.rsqrt(math.lengthsq(planeCoefficients.xyz));
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0;
		L_0 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline((&___0_planeCoefficients), NULL);
		float L_1;
		L_1 = math_lengthsq_mC699F3F214F05B26BEBAF1B46E3AA3C00407A532_inline(L_0, NULL);
		float L_2;
		L_2 = math_rsqrt_mC67B3430EAADA7C5347E87B23859C569BC010E72_inline(L_1, NULL);
		V_0 = L_2;
		// return new Plane { NormalAndDistance = planeCoefficients * recipLength };
		il2cpp_codegen_initobj((&V_1), sizeof(Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82));
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_3 = ___0_planeCoefficients;
		float L_4 = V_0;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_5;
		L_5 = float4_op_Multiply_m712573F441DA8AF0843DE2167927FB76E642B1EB_inline(L_3, L_4, NULL);
		(&V_1)->___NormalAndDistance_0 = L_5;
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_6 = V_1;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_7;
		L_7 = Plane_op_Implicit_mD02477CC24787906751D3F5E401D2E11BF99AC98_inline(L_6, NULL);
		return L_7;
	}
}
// System.Single Unity.Mathematics.Geometry.Plane::SignedDistanceToPoint(Unity.Mathematics.float3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Plane_SignedDistanceToPoint_mE52778BC70A3A0FF9DDB0FE52D71C587D837F993 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) 
{
	{
		// return math.dot(NormalAndDistance, new float4(point, 1.0f));
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_0 = __this->___NormalAndDistance_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_point;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_2;
		memset((&L_2), 0, sizeof(L_2));
		float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_inline((&L_2), L_1, (1.0f), /*hidden argument*/NULL);
		float L_3;
		L_3 = math_dot_m20F2285F7227DC308D9CF2DCB8EAAD3E774501D4_inline(L_0, L_2, NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C  float Plane_SignedDistanceToPoint_mE52778BC70A3A0FF9DDB0FE52D71C587D837F993_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	float _returnValue;
	_returnValue = Plane_SignedDistanceToPoint_mE52778BC70A3A0FF9DDB0FE52D71C587D837F993_inline(_thisAdjusted, ___0_point, method);
	return _returnValue;
}
// Unity.Mathematics.float3 Unity.Mathematics.Geometry.Plane::Projection(Unity.Mathematics.float3)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E Plane_Projection_mFF8C23401C366A3B4EB017B4DAAAF4E8A9132CFE (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) 
{
	{
		// return point - Normal * SignedDistanceToPoint(point);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = Plane_get_Normal_mAA5C1BEAEFB0848A4CD29E254CC9EF010DD6FE4B(__this, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___0_point;
		float L_3;
		L_3 = Plane_SignedDistanceToPoint_mE52778BC70A3A0FF9DDB0FE52D71C587D837F993_inline(__this, L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4;
		L_4 = float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81_inline(L_1, L_3, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_0, L_4, NULL);
		return L_5;
	}
}
IL2CPP_EXTERN_C  float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E Plane_Projection_mFF8C23401C366A3B4EB017B4DAAAF4E8A9132CFE_AdjustorThunk (RuntimeObject* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E _returnValue;
	_returnValue = Plane_Projection_mFF8C23401C366A3B4EB017B4DAAAF4E8A9132CFE_inline(_thisAdjusted, ___0_point, method);
	return _returnValue;
}
// Unity.Mathematics.Geometry.Plane Unity.Mathematics.Geometry.Plane::get_Flipped()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 Plane_get_Flipped_m6D004985368EE6234BA9A5D2800557FFB3A351FA (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, const RuntimeMethod* method) 
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		// public Plane Flipped => new Plane { NormalAndDistance = -NormalAndDistance };
		il2cpp_codegen_initobj((&V_0), sizeof(Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82));
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_0 = __this->___NormalAndDistance_0;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_1;
		L_1 = float4_op_UnaryNegation_m5A491FC1978650D62EBEDC51992CF4B2113C8C5B_inline(L_0, NULL);
		(&V_0)->___NormalAndDistance_0 = L_1;
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_2 = V_0;
		return L_2;
	}
}
IL2CPP_EXTERN_C  Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 Plane_get_Flipped_m6D004985368EE6234BA9A5D2800557FFB3A351FA_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 _returnValue;
	_returnValue = Plane_get_Flipped_m6D004985368EE6234BA9A5D2800557FFB3A351FA(_thisAdjusted, method);
	return _returnValue;
}
// Unity.Mathematics.float4 Unity.Mathematics.Geometry.Plane::op_Implicit(Unity.Mathematics.Geometry.Plane)
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E Plane_op_Implicit_mD02477CC24787906751D3F5E401D2E11BF99AC98 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 ___0_plane, const RuntimeMethod* method) 
{
	{
		// public static implicit operator float4(Plane plane) => plane.NormalAndDistance;
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_0 = ___0_plane;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_1 = L_0.___NormalAndDistance_0;
		return L_1;
	}
}
// System.Void Unity.Mathematics.Geometry.Plane::CheckPlaneIsNormalized()
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Plane_CheckPlaneIsNormalized_m6408EC4BE1D7A0ADB6832288E286D9C70404B888 (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_1;
	memset((&V_1), 0, sizeof(V_1));
	{
		// float ll = math.lengthsq(Normal.xyz);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0;
		L_0 = Plane_get_Normal_mAA5C1BEAEFB0848A4CD29E254CC9EF010DD6FE4B(__this, NULL);
		V_1 = L_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline((&V_1), NULL);
		float L_2;
		L_2 = math_lengthsq_mC699F3F214F05B26BEBAF1B46E3AA3C00407A532_inline(L_1, NULL);
		V_0 = L_2;
		// if (ll < lowerBound || ll > upperBound)
		float L_3 = V_0;
		if ((((float)L_3) < ((float)(0.998001039f))))
		{
			goto IL_0024;
		}
	}
	{
		float L_4 = V_0;
		if ((!(((float)L_4) > ((float)(1.00200105f)))))
		{
			goto IL_002f;
		}
	}

IL_0024:
	{
		// throw new System.ArgumentException("Plane must be normalized. Call Plane.Normalize() to normalize plane.");
		ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* L_5 = (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var)));
		NullCheck(L_5);
		ArgumentException__ctor_m026938A67AF9D36BB7ED27F80425D7194B514465(L_5, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralFB182D98F776AC1C061FA5C163FE7F6E7C08B5BD)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_5, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Plane_CheckPlaneIsNormalized_m6408EC4BE1D7A0ADB6832288E286D9C70404B888_RuntimeMethod_var)));
	}

IL_002f:
	{
		// }
		return;
	}
}
IL2CPP_EXTERN_C  void Plane_CheckPlaneIsNormalized_m6408EC4BE1D7A0ADB6832288E286D9C70404B888_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82*>(__this + _offset);
	Plane_CheckPlaneIsNormalized_m6408EC4BE1D7A0ADB6832288E286D9C70404B888(_thisAdjusted, method);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_min, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_max, const RuntimeMethod* method) 
{
	{
		// Min = min;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_min;
		__this->___Min_0 = L_0;
		// Max = max;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___1_max;
		__this->___Max_1 = L_1;
		// }
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float ___1_rhs, const RuntimeMethod* method) 
{
	{
		// public static float3 operator * (float3 lhs, float rhs) { return new float3 (lhs.x * rhs, lhs.y * rhs, lhs.z * rhs); }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x_0;
		float L_2 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___0_lhs;
		float L_4 = L_3.___y_1;
		float L_5 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___0_lhs;
		float L_7 = L_6.___z_2;
		float L_8 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9;
		memset((&L_9), 0, sizeof(L_9));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_9), ((float)il2cpp_codegen_multiply(L_1, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_5)), ((float)il2cpp_codegen_multiply(L_7, L_8)), /*hidden argument*/NULL);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 MinMaxAABB_CreateFromCenterAndHalfExtents_mF18074A621916107BF592378F72B6F113294F508_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_center, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_halfExtents, const RuntimeMethod* method) 
{
	{
		// return new MinMaxAABB(center - halfExtents, center + halfExtents);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_center;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___1_halfExtents;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___0_center;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___1_halfExtents;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_3, L_4, NULL);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_6;
		memset((&L_6), 0, sizeof(L_6));
		MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_inline((&L_6), L_2, L_5, /*hidden argument*/NULL);
		return L_6;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) 
{
	{
		// public static float3 operator - (float3 lhs, float3 rhs) { return new float3 (lhs.x - rhs.x, lhs.y - rhs.y, lhs.z - rhs.z); }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_rhs;
		float L_3 = L_2.___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_lhs;
		float L_5 = L_4.___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___1_rhs;
		float L_7 = L_6.___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_lhs;
		float L_9 = L_8.___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_rhs;
		float L_11 = L_10.___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_12;
		memset((&L_12), 0, sizeof(L_12));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_12), ((float)il2cpp_codegen_subtract(L_1, L_3)), ((float)il2cpp_codegen_subtract(L_5, L_7)), ((float)il2cpp_codegen_subtract(L_9, L_11)), /*hidden argument*/NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) 
{
	{
		// public static float3 operator + (float3 lhs, float3 rhs) { return new float3 (lhs.x + rhs.x, lhs.y + rhs.y, lhs.z + rhs.z); }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_rhs;
		float L_3 = L_2.___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_lhs;
		float L_5 = L_4.___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___1_rhs;
		float L_7 = L_6.___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_lhs;
		float L_9 = L_8.___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_rhs;
		float L_11 = L_10.___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_12;
		memset((&L_12), 0, sizeof(L_12));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_12), ((float)il2cpp_codegen_add(L_1, L_3)), ((float)il2cpp_codegen_add(L_5, L_7)), ((float)il2cpp_codegen_add(L_9, L_11)), /*hidden argument*/NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) 
{
	{
		// public static bool3 operator <= (float3 lhs, float3 rhs) { return new bool3 (lhs.x <= rhs.x, lhs.y <= rhs.y, lhs.z <= rhs.z); }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_rhs;
		float L_3 = L_2.___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_lhs;
		float L_5 = L_4.___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___1_rhs;
		float L_7 = L_6.___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_lhs;
		float L_9 = L_8.___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_rhs;
		float L_11 = L_10.___z_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2_inline((&L_12), (bool)((((int32_t)((!(((float)L_1) <= ((float)L_3)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((float)L_5) <= ((float)L_7)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((float)L_9) <= ((float)L_11)))? 1 : 0)) == ((int32_t)0))? 1 : 0), /*hidden argument*/NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline (bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___0_x, const RuntimeMethod* method) 
{
	{
		// public static bool all(bool3 x) { return x.x && x.y && x.z; }
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_0 = ___0_x;
		bool L_1 = L_0.___x_0;
		if (!L_1)
		{
			goto IL_0017;
		}
	}
	{
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_2 = ___0_x;
		bool L_3 = L_2.___y_1;
		if (!L_3)
		{
			goto IL_0017;
		}
	}
	{
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_4 = ___0_x;
		bool L_5 = L_4.___z_2;
		return L_5;
	}

IL_0017:
	{
		return (bool)0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) 
{
	{
		// get { return new float3(y, z, x); }
		float L_0 = __this->___y_1;
		float L_1 = __this->___z_2;
		float L_2 = __this->___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		memset((&L_3), 0, sizeof(L_3));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_3), L_0, L_1, L_2, /*hidden argument*/NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) 
{
	{
		// public static float dot(float3 x, float3 y) { return x.x * y.x + x.y * y.y + x.z * y.z; }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_x;
		float L_1 = L_0.___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_y;
		float L_3 = L_2.___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_x;
		float L_5 = L_4.___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___1_y;
		float L_7 = L_6.___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_x;
		float L_9 = L_8.___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_y;
		float L_11 = L_10.___z_2;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7)))), ((float)il2cpp_codegen_multiply(L_9, L_11))));
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 float3_op_GreaterThanOrEqual_m01767B59951623AD803736AB63E12D9BC6FC1AAE_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) 
{
	{
		// public static bool3 operator >= (float3 lhs, float3 rhs) { return new bool3 (lhs.x >= rhs.x, lhs.y >= rhs.y, lhs.z >= rhs.z); }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_rhs;
		float L_3 = L_2.___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_lhs;
		float L_5 = L_4.___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___1_rhs;
		float L_7 = L_6.___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_lhs;
		float L_9 = L_8.___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_rhs;
		float L_11 = L_10.___z_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2_inline((&L_12), (bool)((((int32_t)((!(((float)L_1) >= ((float)L_3)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((float)L_5) >= ((float)L_7)))? 1 : 0)) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)((!(((float)L_9) >= ((float)L_11)))? 1 : 0)) == ((int32_t)0))? 1 : 0), /*hidden argument*/NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 bool3_op_BitwiseAnd_mE44CC5838094A40F1F605A7798994197165A63E1_inline (bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___0_lhs, bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___1_rhs, const RuntimeMethod* method) 
{
	{
		// public static bool3 operator & (bool3 lhs, bool3 rhs) { return new bool3 (lhs.x & rhs.x, lhs.y & rhs.y, lhs.z & rhs.z); }
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_0 = ___0_lhs;
		bool L_1 = L_0.___x_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_2 = ___1_rhs;
		bool L_3 = L_2.___x_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_4 = ___0_lhs;
		bool L_5 = L_4.___y_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_6 = ___1_rhs;
		bool L_7 = L_6.___y_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_8 = ___0_lhs;
		bool L_9 = L_8.___z_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_10 = ___1_rhs;
		bool L_11 = L_10.___z_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2_inline((&L_12), (bool)((int32_t)((int32_t)L_1&(int32_t)L_3)), (bool)((int32_t)((int32_t)L_5&(int32_t)L_7)), (bool)((int32_t)((int32_t)L_9&(int32_t)L_11)), /*hidden argument*/NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MinMaxAABB_Contains_m507D89756D76CA0E2CBD8CAD23C6CE27A5F39089_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) 
{
	{
		// public bool Contains(float3 point) => math.all(point >= Min & point <= Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = __this->___Min_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_2;
		L_2 = float3_op_GreaterThanOrEqual_m01767B59951623AD803736AB63E12D9BC6FC1AAE_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = __this->___Max_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_5;
		L_5 = float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline(L_3, L_4, NULL);
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_6;
		L_6 = bool3_op_BitwiseAnd_mE44CC5838094A40F1F605A7798994197165A63E1_inline(L_2, L_5, NULL);
		bool L_7;
		L_7 = math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline(L_6, NULL);
		return L_7;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MinMaxAABB_Contains_m8AB762A87FD22983A2A5ED2C96D3C559876D8823_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) 
{
	{
		// public bool Contains(MinMaxAABB aabb) => math.all((Min <= aabb.Min) & (Max >= aabb.Max));
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min_0;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_1 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = L_1.___Min_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_3;
		L_3 = float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline(L_0, L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = __this->___Max_1;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_5 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = L_5.___Max_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_7;
		L_7 = float3_op_GreaterThanOrEqual_m01767B59951623AD803736AB63E12D9BC6FC1AAE_inline(L_4, L_6, NULL);
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_8;
		L_8 = bool3_op_BitwiseAnd_mE44CC5838094A40F1F605A7798994197165A63E1_inline(L_3, L_7, NULL);
		bool L_9;
		L_9 = math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline(L_8, NULL);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MinMaxAABB_Overlaps_mEEC801434D524543F9E6D5D77A642A5184F8C4D9_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) 
{
	{
		// return math.all(Max >= aabb.Min & Min <= aabb.Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Max_1;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_1 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = L_1.___Min_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_3;
		L_3 = float3_op_GreaterThanOrEqual_m01767B59951623AD803736AB63E12D9BC6FC1AAE_inline(L_0, L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = __this->___Min_0;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_5 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = L_5.___Max_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_7;
		L_7 = float3_op_LessThanOrEqual_m18273875F0537224587D1622DD53562971D9FF48_inline(L_4, L_6, NULL);
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_8;
		L_8 = bool3_op_BitwiseAnd_mE44CC5838094A40F1F605A7798994197165A63E1_inline(L_3, L_7, NULL);
		bool L_9;
		L_9 = math_all_mB8957D1E684773F171F74448AD9591F3619890A4_inline(L_8, NULL);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Subtraction_m111BEEA770E140739DDC6A3410736DFF7EE32045_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float ___1_rhs, const RuntimeMethod* method) 
{
	{
		// public static float3 operator - (float3 lhs, float rhs) { return new float3 (lhs.x - rhs, lhs.y - rhs, lhs.z - rhs); }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x_0;
		float L_2 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___0_lhs;
		float L_4 = L_3.___y_1;
		float L_5 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___0_lhs;
		float L_7 = L_6.___z_2;
		float L_8 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9;
		memset((&L_9), 0, sizeof(L_9));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_9), ((float)il2cpp_codegen_subtract(L_1, L_2)), ((float)il2cpp_codegen_subtract(L_4, L_5)), ((float)il2cpp_codegen_subtract(L_7, L_8)), /*hidden argument*/NULL);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Addition_mABF24BC9A16C272B9F5AB21A601B9D9A831F8C43_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float ___1_rhs, const RuntimeMethod* method) 
{
	{
		// public static float3 operator + (float3 lhs, float rhs) { return new float3 (lhs.x + rhs, lhs.y + rhs, lhs.z + rhs); }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x_0;
		float L_2 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___0_lhs;
		float L_4 = L_3.___y_1;
		float L_5 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___0_lhs;
		float L_7 = L_6.___z_2;
		float L_8 = ___1_rhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9;
		memset((&L_9), 0, sizeof(L_9));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_9), ((float)il2cpp_codegen_add(L_1, L_2)), ((float)il2cpp_codegen_add(L_4, L_5)), ((float)il2cpp_codegen_add(L_7, L_8)), /*hidden argument*/NULL);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MinMaxAABB_Expand_m8574B6375684AC91E413410881D03B71C400DC75_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float ___0_signedDistance, const RuntimeMethod* method) 
{
	{
		// Min -= signedDistance;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min_0;
		float L_1 = ___0_signedDistance;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Subtraction_m111BEEA770E140739DDC6A3410736DFF7EE32045_inline(L_0, L_1, NULL);
		__this->___Min_0 = L_2;
		// Max += signedDistance;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = __this->___Max_1;
		float L_4 = ___0_signedDistance;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = float3_op_Addition_mABF24BC9A16C272B9F5AB21A601B9D9A831F8C43_inline(L_3, L_4, NULL);
		__this->___Max_1 = L_5;
		// }
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_min_m13CC8D5B7844D954C3125DD72831C693AB8A7FF5_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) 
{
	{
		// public static float3 min(float3 x, float3 y) { return new float3(min(x.x, y.x), min(x.y, y.y), min(x.z, y.z)); }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_x;
		float L_1 = L_0.___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_y;
		float L_3 = L_2.___x_0;
		float L_4;
		L_4 = math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline(L_1, L_3, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5 = ___0_x;
		float L_6 = L_5.___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_7 = ___1_y;
		float L_8 = L_7.___y_1;
		float L_9;
		L_9 = math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline(L_6, L_8, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___0_x;
		float L_11 = L_10.___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_12 = ___1_y;
		float L_13 = L_12.___z_2;
		float L_14;
		L_14 = math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline(L_11, L_13, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_15;
		memset((&L_15), 0, sizeof(L_15));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_15), L_4, L_9, L_14, /*hidden argument*/NULL);
		return L_15;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_max_m247D41258606F80861E72309300DF6A3F8B50AE4_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) 
{
	{
		// public static float3 max(float3 x, float3 y) { return new float3(max(x.x, y.x), max(x.y, y.y), max(x.z, y.z)); }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_x;
		float L_1 = L_0.___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_y;
		float L_3 = L_2.___x_0;
		float L_4;
		L_4 = math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline(L_1, L_3, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5 = ___0_x;
		float L_6 = L_5.___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_7 = ___1_y;
		float L_8 = L_7.___y_1;
		float L_9;
		L_9 = math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline(L_6, L_8, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___0_x;
		float L_11 = L_10.___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_12 = ___1_y;
		float L_13 = L_12.___z_2;
		float L_14;
		L_14 = math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline(L_11, L_13, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_15;
		memset((&L_15), 0, sizeof(L_15));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_15), L_4, L_9, L_14, /*hidden argument*/NULL);
		return L_15;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MinMaxAABB_Encapsulate_m7043627B976BE96D6D734288EEA09563352F753B_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_aabb, const RuntimeMethod* method) 
{
	{
		// Min = math.min(Min, aabb.Min);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min_0;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_1 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = L_1.___Min_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = math_min_m13CC8D5B7844D954C3125DD72831C693AB8A7FF5_inline(L_0, L_2, NULL);
		__this->___Min_0 = L_3;
		// Max = math.max(Max, aabb.Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = __this->___Max_1;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_5 = ___0_aabb;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = L_5.___Max_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_7;
		L_7 = math_max_m247D41258606F80861E72309300DF6A3F8B50AE4_inline(L_4, L_6, NULL);
		__this->___Max_1 = L_7;
		// }
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MinMaxAABB_Encapsulate_mE01CED4767A6B50D846AC30AFCA49A91BA820CD5_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) 
{
	{
		// Min = math.min(Min, point);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = math_min_m13CC8D5B7844D954C3125DD72831C693AB8A7FF5_inline(L_0, L_1, NULL);
		__this->___Min_0 = L_2;
		// Max = math.max(Max, point);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = __this->___Max_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = math_max_m247D41258606F80861E72309300DF6A3F8B50AE4_inline(L_3, L_4, NULL);
		__this->___Max_1 = L_5;
		// }
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_rhs, const RuntimeMethod* method) 
{
	{
		// public bool Equals(float3 rhs) { return x == rhs.x && y == rhs.y && z == rhs.z; }
		float L_0 = __this->___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_rhs;
		float L_2 = L_1.___x_0;
		if ((!(((float)L_0) == ((float)L_2))))
		{
			goto IL_002b;
		}
	}
	{
		float L_3 = __this->___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_rhs;
		float L_5 = L_4.___y_1;
		if ((!(((float)L_3) == ((float)L_5))))
		{
			goto IL_002b;
		}
	}
	{
		float L_6 = __this->___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_7 = ___0_rhs;
		float L_8 = L_7.___z_2;
		return (bool)((((float)L_6) == ((float)L_8))? 1 : 0);
	}

IL_002b:
	{
		return (bool)0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MinMaxAABB_Equals_m6DC492AB1804679250EC6C97CC6DF32299EA8E11_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___0_other, const RuntimeMethod* method) 
{
	{
		// return Min.Equals(other.Min) && Max.Equals(other.Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_0 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&__this->___Min_0);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_1 = ___0_other;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = L_1.___Min_0;
		bool L_3;
		L_3 = float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_inline(L_0, L_2, NULL);
		if (!L_3)
		{
			goto IL_0025;
		}
	}
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_4 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&__this->___Max_1);
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_5 = ___0_other;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = L_5.___Max_1;
		bool L_7;
		L_7 = float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_inline(L_4, L_6, NULL);
		return L_7;
	}

IL_0025:
	{
		return (bool)0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR String_t* MinMaxAABB_ToString_mD299FEC6092F6072F7BC91773DA766E0E61DEC3F_inline (MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFEB995DBF7004A48F3EDB181C599FB99B4A380FB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		// return string.Format("MinMaxAABB({0}, {1})", Min, Max);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = __this->___Min_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = L_0;
		RuntimeObject* L_2 = Box(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E_il2cpp_TypeInfo_var, &L_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = __this->___Max_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = L_3;
		RuntimeObject* L_5 = Box(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E_il2cpp_TypeInfo_var, &L_4);
		String_t* L_6;
		L_6 = String_Format_mFB7DA489BD99F4670881FF50EC017BFB0A5C0987(_stringLiteralFEB995DBF7004A48F3EDB181C599FB99B4A380FB, L_2, L_5, NULL);
		return L_6;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) 
{
	{
		// this.x = x;
		float L_0 = ___0_x;
		__this->___x_0 = L_0;
		// this.y = y;
		float L_1 = ___1_y;
		__this->___y_1 = L_1;
		// this.z = z;
		float L_2 = ___2_z;
		__this->___z_2 = L_2;
		// }
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_rotate_m68CD27B1D0643EA356D0AB41ECB004CE094FDA3F_inline (quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 ___0_q, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_v, const RuntimeMethod* method) 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		// float3 t = 2 * cross(q.value.xyz, v);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_0 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&(&___0_q)->___value_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline(L_0, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_v;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline(L_1, L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4;
		L_4 = float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350_inline((2.0f), L_3, NULL);
		V_0 = L_4;
		// return v + q.value.w * t + cross(q.value.xyz, t);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5 = ___1_v;
		quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 L_6 = ___0_q;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_7 = L_6.___value_0;
		float L_8 = L_7.___w_3;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10;
		L_10 = float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350_inline(L_8, L_9, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_11;
		L_11 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_5, L_10, NULL);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_12 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&(&___0_q)->___value_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_13;
		L_13 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline(L_12, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_14 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_15;
		L_15 = math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline(L_13, L_14, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_16;
		L_16 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_abs_mC7F2BBD861835C82A0A47A47A44B73E704D7F63B_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, const RuntimeMethod* method) 
{
	{
		// public static float3 abs(float3 x) { return asfloat(asuint(x) & 0x7FFFFFFF); }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_x;
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_1;
		L_1 = math_asuint_m4AEE8C17FEDA05D4C77C427818D1C9EF5E31521E_inline(L_0, NULL);
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_2;
		L_2 = uint3_op_BitwiseAnd_m772BFC3A60526C264937ABCA92F1CAAFC2B0D634_inline(L_1, ((int32_t)2147483647LL), NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = math_asfloat_m7A90E1FAABD250FCEC00839D01B098BB046F7933_inline(L_2, NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_transform_m5F6B69A9C0E6E1AF63D8112D8753394891972E44_inline (RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD ___0_a, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_pos, const RuntimeMethod* method) 
{
	{
		// return mul(a.rot, pos) + a.pos;
		RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD L_0 = ___0_a;
		quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 L_1 = L_0.___rot_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_pos;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = math_mul_mE9E04B2868E4D4BA5BD873E4F876D550D36C2E99_inline(L_1, L_2, NULL);
		RigidTransform_tDC22CD9569EC04E16791C2AB53DA5ABD34E88FDD L_4 = ___0_a;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5 = L_4.___pos_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6;
		L_6 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_3, L_5, NULL);
		return L_6;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 Math_Transform_m762480F0988F685C5039CBFBFA540C85385A53CC_inline (float3x3_tB318DB8C7E54B6CA9E14EB9AC7F5964C1189FC79 ___0_transform, MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 ___1_aabb, const RuntimeMethod* method) 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_0;
	memset((&V_0), 0, sizeof(V_0));
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_1;
	memset((&V_1), 0, sizeof(V_1));
	bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 V_2;
	memset((&V_2), 0, sizeof(V_2));
	MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 V_3;
	memset((&V_3), 0, sizeof(V_3));
	{
		// var t1 = transform.c0.xyz * aabb.Min.xxx;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_0 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c0_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_0, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_2 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Min_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = float3_get_xxx_mFD7DFB9FF23BB0B3437F12CC35DB3D1E0ADF7B20_inline(L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4;
		L_4 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_1, L_3, NULL);
		V_0 = L_4;
		// var t2 = transform.c0.xyz * aabb.Max.xxx;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_5 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c0_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6;
		L_6 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_5, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_7 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Max_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8;
		L_8 = float3_get_xxx_mFD7DFB9FF23BB0B3437F12CC35DB3D1E0ADF7B20_inline(L_7, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9;
		L_9 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_6, L_8, NULL);
		V_1 = L_9;
		// var minMask = t1 < t2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_11 = V_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_12;
		L_12 = float3_op_LessThan_m540182EDEF57B5A865B1C22972CF5C3E862B9C51_inline(L_10, L_11, NULL);
		V_2 = L_12;
		// var transformed = new MinMaxAABB(select(t2, t1, minMask), select(t2, t1, !minMask));
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_13 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_14 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_15 = V_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_16;
		L_16 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_13, L_14, L_15, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_17 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_18 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_19 = V_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_20;
		L_20 = bool3_op_LogicalNot_m85C703CC4098B3731505A162957F91C0373548BD_inline(L_19, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_21;
		L_21 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_17, L_18, L_20, NULL);
		MinMaxAABB__ctor_m225BF25AF8235CE330D06E176EC984858B81EF6C_inline((&V_3), L_16, L_21, NULL);
		// t1 = transform.c1.xyz * aabb.Min.yyy;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_22 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c1_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_23;
		L_23 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_22, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_24 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Min_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_25;
		L_25 = float3_get_yyy_m6FCA12991237EDC77F7C4B6A7F73710338330CCD_inline(L_24, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_26;
		L_26 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_23, L_25, NULL);
		V_0 = L_26;
		// t2 = transform.c1.xyz * aabb.Max.yyy;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_27 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c1_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_28;
		L_28 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_27, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_29 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Max_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_30;
		L_30 = float3_get_yyy_m6FCA12991237EDC77F7C4B6A7F73710338330CCD_inline(L_29, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_31;
		L_31 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_28, L_30, NULL);
		V_1 = L_31;
		// minMask = t1 < t2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_32 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_33 = V_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_34;
		L_34 = float3_op_LessThan_m540182EDEF57B5A865B1C22972CF5C3E862B9C51_inline(L_32, L_33, NULL);
		V_2 = L_34;
		// transformed.Min += select(t2, t1, minMask);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_35 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_3)->___Min_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_36 = L_35;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_37 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_36);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_38 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_39 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_40 = V_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_41;
		L_41 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_38, L_39, L_40, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_42;
		L_42 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_37, L_41, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_36 = L_42;
		// transformed.Max += select(t2, t1, !minMask);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_43 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_3)->___Max_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_44 = L_43;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_45 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_44);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_46 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_47 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_48 = V_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_49;
		L_49 = bool3_op_LogicalNot_m85C703CC4098B3731505A162957F91C0373548BD_inline(L_48, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_50;
		L_50 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_46, L_47, L_49, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_51;
		L_51 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_45, L_50, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_44 = L_51;
		// t1 = transform.c2.xyz * aabb.Min.zzz;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_52 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c2_2);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_53;
		L_53 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_52, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_54 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Min_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_55;
		L_55 = float3_get_zzz_m1C7C995F170030A7EF534E2C99E6AFE6928AE9D4_inline(L_54, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_56;
		L_56 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_53, L_55, NULL);
		V_0 = L_56;
		// t2 = transform.c2.xyz * aabb.Max.zzz;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_57 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___0_transform)->___c2_2);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_58;
		L_58 = float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline(L_57, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_59 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&___1_aabb)->___Max_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_60;
		L_60 = float3_get_zzz_m1C7C995F170030A7EF534E2C99E6AFE6928AE9D4_inline(L_59, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_61;
		L_61 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_58, L_60, NULL);
		V_1 = L_61;
		// minMask = t1 < t2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_62 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_63 = V_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_64;
		L_64 = float3_op_LessThan_m540182EDEF57B5A865B1C22972CF5C3E862B9C51_inline(L_62, L_63, NULL);
		V_2 = L_64;
		// transformed.Min += select(t2, t1, minMask);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_65 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_3)->___Min_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_66 = L_65;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_67 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_66);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_68 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_69 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_70 = V_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_71;
		L_71 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_68, L_69, L_70, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_72;
		L_72 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_67, L_71, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_66 = L_72;
		// transformed.Max += select(t2, t1, !minMask);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_73 = (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)(&(&V_3)->___Max_1);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* L_74 = L_73;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_75 = (*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_74);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_76 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_77 = V_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_78 = V_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_79;
		L_79 = bool3_op_LogicalNot_m85C703CC4098B3731505A162957F91C0373548BD_inline(L_78, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_80;
		L_80 = math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline(L_76, L_77, L_79, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_81;
		L_81 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_75, L_80, NULL);
		*(float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E*)L_74 = L_81;
		// return transformed;
		MinMaxAABB_tF3782F16AC56D8B43613443DAFD7ED95A0690887 L_82 = V_3;
		return L_82;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E* __this, const RuntimeMethod* method) 
{
	{
		// get { return new float3(x, y, z); }
		float L_0 = __this->___x_0;
		float L_1 = __this->___y_1;
		float L_2 = __this->___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		memset((&L_3), 0, sizeof(L_3));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_3), L_0, L_1, L_2, /*hidden argument*/NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) 
{
	{
		// get { return new float3(x, y, z); }
		float L_0 = __this->___x_0;
		float L_1 = __this->___y_1;
		float L_2 = __this->___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		memset((&L_3), 0, sizeof(L_3));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_3), L_0, L_1, L_2, /*hidden argument*/NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_xxx_mFD7DFB9FF23BB0B3437F12CC35DB3D1E0ADF7B20_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) 
{
	{
		// get { return new float3(x, x, x); }
		float L_0 = __this->___x_0;
		float L_1 = __this->___x_0;
		float L_2 = __this->___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		memset((&L_3), 0, sizeof(L_3));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_3), L_0, L_1, L_2, /*hidden argument*/NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) 
{
	{
		// public static float3 operator * (float3 lhs, float3 rhs) { return new float3 (lhs.x * rhs.x, lhs.y * rhs.y, lhs.z * rhs.z); }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_rhs;
		float L_3 = L_2.___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_lhs;
		float L_5 = L_4.___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___1_rhs;
		float L_7 = L_6.___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_lhs;
		float L_9 = L_8.___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_rhs;
		float L_11 = L_10.___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_12;
		memset((&L_12), 0, sizeof(L_12));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_12), ((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7)), ((float)il2cpp_codegen_multiply(L_9, L_11)), /*hidden argument*/NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 float3_op_LessThan_m540182EDEF57B5A865B1C22972CF5C3E862B9C51_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) 
{
	{
		// public static bool3 operator < (float3 lhs, float3 rhs) { return new bool3 (lhs.x < rhs.x, lhs.y < rhs.y, lhs.z < rhs.z); }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_rhs;
		float L_3 = L_2.___x_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_lhs;
		float L_5 = L_4.___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___1_rhs;
		float L_7 = L_6.___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_lhs;
		float L_9 = L_8.___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_rhs;
		float L_11 = L_10.___z_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_12;
		memset((&L_12), 0, sizeof(L_12));
		bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2_inline((&L_12), (bool)((((float)L_1) < ((float)L_3))? 1 : 0), (bool)((((float)L_5) < ((float)L_7))? 1 : 0), (bool)((((float)L_9) < ((float)L_11))? 1 : 0), /*hidden argument*/NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_select_m70FF17D80BE3CA32463B1D77C0CD20AA21B887AA_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_a, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_b, bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___2_c, const RuntimeMethod* method) 
{
	float G_B3_0 = 0.0f;
	float G_B5_0 = 0.0f;
	float G_B4_0 = 0.0f;
	float G_B6_0 = 0.0f;
	float G_B6_1 = 0.0f;
	float G_B8_0 = 0.0f;
	float G_B8_1 = 0.0f;
	float G_B7_0 = 0.0f;
	float G_B7_1 = 0.0f;
	float G_B9_0 = 0.0f;
	float G_B9_1 = 0.0f;
	float G_B9_2 = 0.0f;
	{
		// public static float3 select(float3 a, float3 b, bool3 c) { return new float3(c.x ? b.x : a.x, c.y ? b.y : a.y, c.z ? b.z : a.z); }
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_0 = ___2_c;
		bool L_1 = L_0.___x_0;
		if (L_1)
		{
			goto IL_0010;
		}
	}
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___0_a;
		float L_3 = L_2.___x_0;
		G_B3_0 = L_3;
		goto IL_0016;
	}

IL_0010:
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___1_b;
		float L_5 = L_4.___x_0;
		G_B3_0 = L_5;
	}

IL_0016:
	{
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_6 = ___2_c;
		bool L_7 = L_6.___y_1;
		G_B4_0 = G_B3_0;
		if (L_7)
		{
			G_B5_0 = G_B3_0;
			goto IL_0026;
		}
	}
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_a;
		float L_9 = L_8.___y_1;
		G_B6_0 = L_9;
		G_B6_1 = G_B4_0;
		goto IL_002c;
	}

IL_0026:
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_b;
		float L_11 = L_10.___y_1;
		G_B6_0 = L_11;
		G_B6_1 = G_B5_0;
	}

IL_002c:
	{
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_12 = ___2_c;
		bool L_13 = L_12.___z_2;
		G_B7_0 = G_B6_0;
		G_B7_1 = G_B6_1;
		if (L_13)
		{
			G_B8_0 = G_B6_0;
			G_B8_1 = G_B6_1;
			goto IL_003c;
		}
	}
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_14 = ___0_a;
		float L_15 = L_14.___z_2;
		G_B9_0 = L_15;
		G_B9_1 = G_B7_0;
		G_B9_2 = G_B7_1;
		goto IL_0042;
	}

IL_003c:
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_16 = ___1_b;
		float L_17 = L_16.___z_2;
		G_B9_0 = L_17;
		G_B9_1 = G_B8_0;
		G_B9_2 = G_B8_1;
	}

IL_0042:
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_18;
		memset((&L_18), 0, sizeof(L_18));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_18), G_B9_2, G_B9_1, G_B9_0, /*hidden argument*/NULL);
		return L_18;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 bool3_op_LogicalNot_m85C703CC4098B3731505A162957F91C0373548BD_inline (bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 ___0_val, const RuntimeMethod* method) 
{
	{
		// public static bool3 operator ! (bool3 val) { return new bool3 (!val.x, !val.y, !val.z); }
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_0 = ___0_val;
		bool L_1 = L_0.___x_0;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_2 = ___0_val;
		bool L_3 = L_2.___y_1;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_4 = ___0_val;
		bool L_5 = L_4.___z_2;
		bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861 L_6;
		memset((&L_6), 0, sizeof(L_6));
		bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2_inline((&L_6), (bool)((((int32_t)L_1) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)L_3) == ((int32_t)0))? 1 : 0), (bool)((((int32_t)L_5) == ((int32_t)0))? 1 : 0), /*hidden argument*/NULL);
		return L_6;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_yyy_m6FCA12991237EDC77F7C4B6A7F73710338330CCD_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) 
{
	{
		// get { return new float3(y, y, y); }
		float L_0 = __this->___y_1;
		float L_1 = __this->___y_1;
		float L_2 = __this->___y_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		memset((&L_3), 0, sizeof(L_3));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_3), L_0, L_1, L_2, /*hidden argument*/NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_zzz_m1C7C995F170030A7EF534E2C99E6AFE6928AE9D4_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) 
{
	{
		// get { return new float3(z, z, z); }
		float L_0 = __this->___z_2;
		float L_1 = __this->___z_2;
		float L_2 = __this->___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		memset((&L_3), 0, sizeof(L_3));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_3), L_0, L_1, L_2, /*hidden argument*/NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E* __this, float ___0_x, float ___1_y, float ___2_z, float ___3_w, const RuntimeMethod* method) 
{
	{
		// this.x = x;
		float L_0 = ___0_x;
		__this->___x_0 = L_0;
		// this.y = y;
		float L_1 = ___1_y;
		__this->___y_1 = L_1;
		// this.z = z;
		float L_2 = ___2_z;
		__this->___z_2 = L_2;
		// this.w = w;
		float L_3 = ___3_w;
		__this->___w_3 = L_3;
		// }
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E Plane_Normalize_mFF53F95372AE2A79C71B546A70B0F152EC308544_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_planeCoefficients, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 V_1;
	memset((&V_1), 0, sizeof(V_1));
	{
		// float recipLength = math.rsqrt(math.lengthsq(planeCoefficients.xyz));
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0;
		L_0 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline((&___0_planeCoefficients), NULL);
		float L_1;
		L_1 = math_lengthsq_mC699F3F214F05B26BEBAF1B46E3AA3C00407A532_inline(L_0, NULL);
		float L_2;
		L_2 = math_rsqrt_mC67B3430EAADA7C5347E87B23859C569BC010E72_inline(L_1, NULL);
		V_0 = L_2;
		// return new Plane { NormalAndDistance = planeCoefficients * recipLength };
		il2cpp_codegen_initobj((&V_1), sizeof(Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82));
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_3 = ___0_planeCoefficients;
		float L_4 = V_0;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_5;
		L_5 = float4_op_Multiply_m712573F441DA8AF0843DE2167927FB76E642B1EB_inline(L_3, L_4, NULL);
		(&V_1)->___NormalAndDistance_0 = L_5;
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_6 = V_1;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_7;
		L_7 = Plane_op_Implicit_mD02477CC24787906751D3F5E401D2E11BF99AC98_inline(L_6, NULL);
		return L_7;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Plane__ctor_mE41939B3E3E2AE7802AA9571AB429BAB47C56A65_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float ___0_coefficientA, float ___1_coefficientB, float ___2_coefficientC, float ___3_coefficientD, const RuntimeMethod* method) 
{
	{
		// NormalAndDistance = Normalize(new float4(coefficientA, coefficientB, coefficientC, coefficientD));
		float L_0 = ___0_coefficientA;
		float L_1 = ___1_coefficientB;
		float L_2 = ___2_coefficientC;
		float L_3 = ___3_coefficientD;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_4;
		memset((&L_4), 0, sizeof(L_4));
		float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D_inline((&L_4), L_0, L_1, L_2, L_3, /*hidden argument*/NULL);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_5;
		L_5 = Plane_Normalize_mFF53F95372AE2A79C71B546A70B0F152EC308544_inline(L_4, NULL);
		__this->___NormalAndDistance_0 = L_5;
		// }
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_xyz, float ___1_w, const RuntimeMethod* method) 
{
	{
		// this.x = xyz.x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_xyz;
		float L_1 = L_0.___x_0;
		__this->___x_0 = L_1;
		// this.y = xyz.y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___0_xyz;
		float L_3 = L_2.___y_1;
		__this->___y_1 = L_3;
		// this.z = xyz.z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_xyz;
		float L_5 = L_4.___z_2;
		__this->___z_2 = L_5;
		// this.w = w;
		float L_6 = ___1_w;
		__this->___w_3 = L_6;
		// }
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Plane__ctor_mAEEAADCE34CB243E12A9FE0240D4E508913C1153_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_normal, float ___1_distance, const RuntimeMethod* method) 
{
	{
		// NormalAndDistance = Normalize(new float4(normal, distance));
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_normal;
		float L_1 = ___1_distance;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_2;
		memset((&L_2), 0, sizeof(L_2));
		float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_inline((&L_2), L_0, L_1, /*hidden argument*/NULL);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_3;
		L_3 = Plane_Normalize_mFF53F95372AE2A79C71B546A70B0F152EC308544_inline(L_2, NULL);
		__this->___NormalAndDistance_0 = L_3;
		// }
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Plane__ctor_m645C0F13FB29D9E443284F1BC42C02CE3B5C27D4_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_normal, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_pointInPlane, const RuntimeMethod* method) 
{
	{
		// : this(normal, -math.dot(normal, pointInPlane))
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_normal;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_normal;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_pointInPlane;
		float L_3;
		L_3 = math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD_inline(L_1, L_2, NULL);
		Plane__ctor_mAEEAADCE34CB243E12A9FE0240D4E508913C1153_inline(__this, L_0, ((-L_3)), NULL);
		// }
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		// public static float3 cross(float3 x, float3 y) { return (x * y.yzx - x.yzx * y).yzx; }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_inline((&___1_y), NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_inline((&___0_x), NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___1_y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_3, L_4, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6;
		L_6 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_2, L_5, NULL);
		V_0 = L_6;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_7;
		L_7 = float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_inline((&V_0), NULL);
		return L_7;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Plane__ctor_m77B64CCE37D396DD70CD0A841F4E6E4F72D1B20A_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_vector1InPlane, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_vector2InPlane, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___2_pointInPlane, const RuntimeMethod* method) 
{
	{
		// : this(math.cross(vector1InPlane, vector2InPlane), pointInPlane)
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_vector1InPlane;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___1_vector2InPlane;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___2_pointInPlane;
		Plane__ctor_m645C0F13FB29D9E443284F1BC42C02CE3B5C27D4_inline(__this, L_2, L_3, NULL);
		// }
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float4_set_xyz_m331D16059D51A5C6CA8AE8FD1E13A68C0570A9C7_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_value, const RuntimeMethod* method) 
{
	{
		// set { x = value.x; y = value.y; z = value.z; }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_value;
		float L_1 = L_0.___x_0;
		__this->___x_0 = L_1;
		// set { x = value.x; y = value.y; z = value.z; }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___0_value;
		float L_3 = L_2.___y_1;
		__this->___y_1 = L_3;
		// set { x = value.x; y = value.y; z = value.z; }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_value;
		float L_5 = L_4.___z_2;
		__this->___z_2 = L_5;
		// set { x = value.x; y = value.y; z = value.z; }
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_lengthsq_mC699F3F214F05B26BEBAF1B46E3AA3C00407A532_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, const RuntimeMethod* method) 
{
	{
		// public static float lengthsq(float3 x) { return dot(x, x); }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_x;
		float L_2;
		L_2 = math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD_inline(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_rsqrt_mC67B3430EAADA7C5347E87B23859C569BC010E72_inline (float ___0_x, const RuntimeMethod* method) 
{
	{
		// public static float rsqrt(float x) { return 1.0f / sqrt(x); }
		float L_0 = ___0_x;
		float L_1;
		L_1 = math_sqrt_mEF31DE7BD0179009683C5D7B0C58E6571B30CF4A_inline(L_0, NULL);
		return ((float)((1.0f)/L_1));
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E float4_op_Multiply_m712573F441DA8AF0843DE2167927FB76E642B1EB_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_lhs, float ___1_rhs, const RuntimeMethod* method) 
{
	{
		// public static float4 operator * (float4 lhs, float rhs) { return new float4 (lhs.x * rhs, lhs.y * rhs, lhs.z * rhs, lhs.w * rhs); }
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_0 = ___0_lhs;
		float L_1 = L_0.___x_0;
		float L_2 = ___1_rhs;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_3 = ___0_lhs;
		float L_4 = L_3.___y_1;
		float L_5 = ___1_rhs;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_6 = ___0_lhs;
		float L_7 = L_6.___z_2;
		float L_8 = ___1_rhs;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_9 = ___0_lhs;
		float L_10 = L_9.___w_3;
		float L_11 = ___1_rhs;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_12;
		memset((&L_12), 0, sizeof(L_12));
		float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D_inline((&L_12), ((float)il2cpp_codegen_multiply(L_1, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_5)), ((float)il2cpp_codegen_multiply(L_7, L_8)), ((float)il2cpp_codegen_multiply(L_10, L_11)), /*hidden argument*/NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E Plane_op_Implicit_mD02477CC24787906751D3F5E401D2E11BF99AC98_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 ___0_plane, const RuntimeMethod* method) 
{
	{
		// public static implicit operator float4(Plane plane) => plane.NormalAndDistance;
		Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82 L_0 = ___0_plane;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_1 = L_0.___NormalAndDistance_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_dot_m20F2285F7227DC308D9CF2DCB8EAAD3E774501D4_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_x, float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___1_y, const RuntimeMethod* method) 
{
	{
		// public static float dot(float4 x, float4 y) { return x.x * y.x + x.y * y.y + x.z * y.z + x.w * y.w; }
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_0 = ___0_x;
		float L_1 = L_0.___x_0;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_2 = ___1_y;
		float L_3 = L_2.___x_0;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_4 = ___0_x;
		float L_5 = L_4.___y_1;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_6 = ___1_y;
		float L_7 = L_6.___y_1;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_8 = ___0_x;
		float L_9 = L_8.___z_2;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_10 = ___1_y;
		float L_11 = L_10.___z_2;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_12 = ___0_x;
		float L_13 = L_12.___w_3;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_14 = ___1_y;
		float L_15 = L_14.___w_3;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7)))), ((float)il2cpp_codegen_multiply(L_9, L_11)))), ((float)il2cpp_codegen_multiply(L_13, L_15))));
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Plane_SignedDistanceToPoint_mE52778BC70A3A0FF9DDB0FE52D71C587D837F993_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) 
{
	{
		// return math.dot(NormalAndDistance, new float4(point, 1.0f));
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_0 = __this->___NormalAndDistance_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___0_point;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_2;
		memset((&L_2), 0, sizeof(L_2));
		float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_inline((&L_2), L_1, (1.0f), /*hidden argument*/NULL);
		float L_3;
		L_3 = math_dot_m20F2285F7227DC308D9CF2DCB8EAAD3E774501D4_inline(L_0, L_2, NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E Plane_Projection_mFF8C23401C366A3B4EB017B4DAAAF4E8A9132CFE_inline (Plane_t50367039DBDD14AAC4BFC716C7444664F1FB5A82* __this, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_point, const RuntimeMethod* method) 
{
	{
		// return point - Normal * SignedDistanceToPoint(point);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_point;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = Plane_get_Normal_mAA5C1BEAEFB0848A4CD29E254CC9EF010DD6FE4B(__this, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___0_point;
		float L_3;
		L_3 = Plane_SignedDistanceToPoint_mE52778BC70A3A0FF9DDB0FE52D71C587D837F993_inline(__this, L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4;
		L_4 = float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81_inline(L_1, L_3, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_0, L_4, NULL);
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E float4_op_UnaryNegation_m5A491FC1978650D62EBEDC51992CF4B2113C8C5B_inline (float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___0_val, const RuntimeMethod* method) 
{
	{
		// public static float4 operator - (float4 val) { return new float4 (-val.x, -val.y, -val.z, -val.w); }
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_0 = ___0_val;
		float L_1 = L_0.___x_0;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_2 = ___0_val;
		float L_3 = L_2.___y_1;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_4 = ___0_val;
		float L_5 = L_4.___z_2;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_6 = ___0_val;
		float L_7 = L_6.___w_3;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_8;
		memset((&L_8), 0, sizeof(L_8));
		float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D_inline((&L_8), ((-L_1)), ((-L_3)), ((-L_5)), ((-L_7)), /*hidden argument*/NULL);
		return L_8;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void bool3__ctor_m3683F21E6C110670CDDA02E4C1F6E063E936FEE2_inline (bool3_tFD58BE2745DA207A4938AB2F604C829410F0A861* __this, bool ___0_x, bool ___1_y, bool ___2_z, const RuntimeMethod* method) 
{
	{
		// this.x = x;
		bool L_0 = ___0_x;
		__this->___x_0 = L_0;
		// this.y = y;
		bool L_1 = ___1_y;
		__this->___y_1 = L_1;
		// this.z = z;
		bool L_2 = ___2_z;
		__this->___z_2 = L_2;
		// }
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline (float ___0_x, float ___1_y, const RuntimeMethod* method) 
{
	{
		// public static float min(float x, float y) { return float.IsNaN(y) || x < y ? x : y; }
		float L_0 = ___1_y;
		bool L_1;
		L_1 = Single_IsNaN_mFE637F6ECA9F7697CE8EFF56427858F4C5EDF75D_inline(L_0, NULL);
		if (L_1)
		{
			goto IL_000e;
		}
	}
	{
		float L_2 = ___0_x;
		float L_3 = ___1_y;
		if ((((float)L_2) < ((float)L_3)))
		{
			goto IL_000e;
		}
	}
	{
		float L_4 = ___1_y;
		return L_4;
	}

IL_000e:
	{
		float L_5 = ___0_x;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline (float ___0_x, float ___1_y, const RuntimeMethod* method) 
{
	{
		// public static float max(float x, float y) { return float.IsNaN(y) || x > y ? x : y; }
		float L_0 = ___1_y;
		bool L_1;
		L_1 = Single_IsNaN_mFE637F6ECA9F7697CE8EFF56427858F4C5EDF75D_inline(L_0, NULL);
		if (L_1)
		{
			goto IL_000e;
		}
	}
	{
		float L_2 = ___0_x;
		float L_3 = ___1_y;
		if ((((float)L_2) > ((float)L_3)))
		{
			goto IL_000e;
		}
	}
	{
		float L_4 = ___1_y;
		return L_4;
	}

IL_000e:
	{
		float L_5 = ___0_x;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350_inline (float ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) 
{
	{
		// public static float3 operator * (float lhs, float3 rhs) { return new float3 (lhs * rhs.x, lhs * rhs.y, lhs * rhs.z); }
		float L_0 = ___0_lhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1 = ___1_rhs;
		float L_2 = L_1.___x_0;
		float L_3 = ___0_lhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___1_rhs;
		float L_5 = L_4.___y_1;
		float L_6 = ___0_lhs;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_7 = ___1_rhs;
		float L_8 = L_7.___z_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9;
		memset((&L_9), 0, sizeof(L_9));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_9), ((float)il2cpp_codegen_multiply(L_0, L_2)), ((float)il2cpp_codegen_multiply(L_3, L_5)), ((float)il2cpp_codegen_multiply(L_6, L_8)), /*hidden argument*/NULL);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B math_asuint_m4AEE8C17FEDA05D4C77C427818D1C9EF5E31521E_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, const RuntimeMethod* method) 
{
	{
		// public static uint3 asuint(float3 x) { return uint3(asuint(x.x), asuint(x.y), asuint(x.z)); }
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_x;
		float L_1 = L_0.___x_0;
		uint32_t L_2;
		L_2 = math_asuint_m503D1ABF19E4BA615FD8AE1BF1A2E103BBED6139_inline(L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3 = ___0_x;
		float L_4 = L_3.___y_1;
		uint32_t L_5;
		L_5 = math_asuint_m503D1ABF19E4BA615FD8AE1BF1A2E103BBED6139_inline(L_4, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___0_x;
		float L_7 = L_6.___z_2;
		uint32_t L_8;
		L_8 = math_asuint_m503D1ABF19E4BA615FD8AE1BF1A2E103BBED6139_inline(L_7, NULL);
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_9;
		L_9 = math_uint3_mC94DDA8B357EA045D5A36B81CECD0C5C223B71B0_inline(L_2, L_5, L_8, NULL);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B uint3_op_BitwiseAnd_m772BFC3A60526C264937ABCA92F1CAAFC2B0D634_inline (uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B ___0_lhs, uint32_t ___1_rhs, const RuntimeMethod* method) 
{
	{
		// public static uint3 operator & (uint3 lhs, uint rhs) { return new uint3 (lhs.x & rhs, lhs.y & rhs, lhs.z & rhs); }
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_0 = ___0_lhs;
		uint32_t L_1 = L_0.___x_0;
		uint32_t L_2 = ___1_rhs;
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_3 = ___0_lhs;
		uint32_t L_4 = L_3.___y_1;
		uint32_t L_5 = ___1_rhs;
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_6 = ___0_lhs;
		uint32_t L_7 = L_6.___z_2;
		uint32_t L_8 = ___1_rhs;
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_9;
		memset((&L_9), 0, sizeof(L_9));
		uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF_inline((&L_9), ((int32_t)((int32_t)L_1&(int32_t)L_2)), ((int32_t)((int32_t)L_4&(int32_t)L_5)), ((int32_t)((int32_t)L_7&(int32_t)L_8)), /*hidden argument*/NULL);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_asfloat_m7A90E1FAABD250FCEC00839D01B098BB046F7933_inline (uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B ___0_x, const RuntimeMethod* method) 
{
	{
		// public static float3 asfloat(uint3 x) { return float3(asfloat(x.x), asfloat(x.y), asfloat(x.z)); }
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_0 = ___0_x;
		uint32_t L_1 = L_0.___x_0;
		float L_2;
		L_2 = math_asfloat_m20D259DAAB46464B59BD8BF5678F9D59800F70A9_inline(L_1, NULL);
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_3 = ___0_x;
		uint32_t L_4 = L_3.___y_1;
		float L_5;
		L_5 = math_asfloat_m20D259DAAB46464B59BD8BF5678F9D59800F70A9_inline(L_4, NULL);
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_6 = ___0_x;
		uint32_t L_7 = L_6.___z_2;
		float L_8;
		L_8 = math_asfloat_m20D259DAAB46464B59BD8BF5678F9D59800F70A9_inline(L_7, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9;
		L_9 = math_float3_m4F96A74FEE1D6C85241B8E62386C5DE1C439837F_inline(L_2, L_5, L_8, NULL);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_mul_mE9E04B2868E4D4BA5BD873E4F876D550D36C2E99_inline (quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 ___0_q, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_v, const RuntimeMethod* method) 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		// float3 t = 2 * cross(q.value.xyz, v);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_0 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&(&___0_q)->___value_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline(L_0, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_v;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline(L_1, L_2, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4;
		L_4 = float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350_inline((2.0f), L_3, NULL);
		V_0 = L_4;
		// return v + q.value.w * t + cross(q.value.xyz, t);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5 = ___1_v;
		quaternion_tD6BCBECAF088B9EBAE2345EC8534C7A1A4C910D4 L_6 = ___0_q;
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_7 = L_6.___value_0;
		float L_8 = L_7.___w_3;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_9 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10;
		L_10 = float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350_inline(L_8, L_9, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_11;
		L_11 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_5, L_10, NULL);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E* L_12 = (float4_t89D9A294E7A79BD81BFBDD18654508532958555E*)(&(&___0_q)->___value_0);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_13;
		L_13 = float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_inline(L_12, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_14 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_15;
		L_15 = math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline(L_13, L_14, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_16;
		L_16 = float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B_inline(L_11, L_15, NULL);
		return L_16;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_sqrt_mEF31DE7BD0179009683C5D7B0C58E6571B30CF4A_inline (float ___0_x, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		// public static float sqrt(float x) { return (float)System.Math.Sqrt((float)x); }
		float L_0 = ___0_x;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_1;
		L_1 = sqrt(((double)((float)L_0)));
		return ((float)L_1);
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Single_IsNaN_mFE637F6ECA9F7697CE8EFF56427858F4C5EDF75D_inline (float ___0_f, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&BitConverter_t6E99605185963BC12B3D369E13F2B88997E64A27_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		float L_0 = ___0_f;
		il2cpp_codegen_runtime_class_init_inline(BitConverter_t6E99605185963BC12B3D369E13F2B88997E64A27_il2cpp_TypeInfo_var);
		int32_t L_1;
		L_1 = BitConverter_SingleToInt32Bits_mC760C7CFC89725E3CF68DC45BE3A9A42A7E7DA73_inline(L_0, NULL);
		return (bool)((((int32_t)((int32_t)(L_1&((int32_t)2147483647LL)))) > ((int32_t)((int32_t)2139095040)))? 1 : 0);
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint32_t math_asuint_m503D1ABF19E4BA615FD8AE1BF1A2E103BBED6139_inline (float ___0_x, const RuntimeMethod* method) 
{
	{
		// public static uint asuint(float x) { return (uint)asint(x); }
		float L_0 = ___0_x;
		int32_t L_1;
		L_1 = math_asint_mBDED7FE966CA65F6A8ACEAEF8FD779B1B8998288_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B math_uint3_mC94DDA8B357EA045D5A36B81CECD0C5C223B71B0_inline (uint32_t ___0_x, uint32_t ___1_y, uint32_t ___2_z, const RuntimeMethod* method) 
{
	{
		// public static uint3 uint3(uint x, uint y, uint z) { return new uint3(x, y, z); }
		uint32_t L_0 = ___0_x;
		uint32_t L_1 = ___1_y;
		uint32_t L_2 = ___2_z;
		uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B L_3;
		memset((&L_3), 0, sizeof(L_3));
		uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF_inline((&L_3), L_0, L_1, L_2, /*hidden argument*/NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF_inline (uint3_tC1C1C817DB46ED2E6A6C7390716FDDD565917F7B* __this, uint32_t ___0_x, uint32_t ___1_y, uint32_t ___2_z, const RuntimeMethod* method) 
{
	{
		// this.x = x;
		uint32_t L_0 = ___0_x;
		__this->___x_0 = L_0;
		// this.y = y;
		uint32_t L_1 = ___1_y;
		__this->___y_1 = L_1;
		// this.z = z;
		uint32_t L_2 = ___2_z;
		__this->___z_2 = L_2;
		// }
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_asfloat_m20D259DAAB46464B59BD8BF5678F9D59800F70A9_inline (uint32_t ___0_x, const RuntimeMethod* method) 
{
	{
		// public static float  asfloat(uint x) { return asfloat((int)x); }
		uint32_t L_0 = ___0_x;
		float L_1;
		L_1 = math_asfloat_m9FA56DE5C61FCEF3DCD0675252D40DFD9C9B712F_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_float3_m4F96A74FEE1D6C85241B8E62386C5DE1C439837F_inline (float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) 
{
	{
		// public static float3 float3(float x, float y, float z) { return new float3(x, y, z); }
		float L_0 = ___0_x;
		float L_1 = ___1_y;
		float L_2 = ___2_z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		memset((&L_3), 0, sizeof(L_3));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_3), L_0, L_1, L_2, /*hidden argument*/NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t BitConverter_SingleToInt32Bits_mC760C7CFC89725E3CF68DC45BE3A9A42A7E7DA73_inline (float ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = *((int32_t*)((uintptr_t)(&___0_value)));
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t math_asint_mBDED7FE966CA65F6A8ACEAEF8FD779B1B8998288_inline (float ___0_x, const RuntimeMethod* method) 
{
	IntFloatUnion_t549256A9DD754252DD18383D9CE7EA55EBBD6D96 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		// u.intValue = 0;
		(&V_0)->___intValue_0 = 0;
		// u.floatValue = x;
		float L_0 = ___0_x;
		(&V_0)->___floatValue_1 = L_0;
		// return u.intValue;
		IntFloatUnion_t549256A9DD754252DD18383D9CE7EA55EBBD6D96 L_1 = V_0;
		int32_t L_2 = L_1.___intValue_0;
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_asfloat_m9FA56DE5C61FCEF3DCD0675252D40DFD9C9B712F_inline (int32_t ___0_x, const RuntimeMethod* method) 
{
	IntFloatUnion_t549256A9DD754252DD18383D9CE7EA55EBBD6D96 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		// u.floatValue = 0;
		(&V_0)->___floatValue_1 = (0.0f);
		// u.intValue = x;
		int32_t L_0 = ___0_x;
		(&V_0)->___intValue_0 = L_0;
		// return u.floatValue;
		IntFloatUnion_t549256A9DD754252DD18383D9CE7EA55EBBD6D96 L_1 = V_0;
		float L_2 = L_1.___floatValue_1;
		return L_2;
	}
}
