# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    artifactId: "loader-1.0.5.ForUnitySDK"
  }
  digests {
    sha256: "wD\365\340\211]\005\341\247\226\b\353:\205\0333\247^\027G?\272YUf\372\341\030\231I T"
  }
}
library {
  maven_library {
    artifactId: "tob_api-release"
  }
  digests {
    sha256: "\002\202D\\\323\277\332\235\347\267\211\265\003\016\345\270\236\326\2675)\'D\330$\321\211o/6\207?"
  }
}
library {
  maven_library {
    artifactId: "capturelib-0.0.7"
  }
  digests {
    sha256: "k:\240\a\334\001\347\261\003@\'\305\206\352A\355\260\024\004\346\247\003FtW\322nM\372\316q\""
  }
}
library {
  maven_library {
    artifactId: "CameraRenderingPlugin"
  }
  digests {
    sha256: "C\260j@S\215^BEV\300\367\321H\251\v\023dlGu\332Xw)\"w\aU(\235\236"
  }
}
library {
  maven_library {
    artifactId: "PxrPlatform"
  }
  digests {
    sha256: "+\224\337\n\205;\016\314\243\001\f\307\233\244Mgr ~\333\017\271_\035\334;\264\017z\312\336\322"
  }
}
library {
  maven_library {
    artifactId: "tobservicelib-release"
  }
  digests {
    sha256: "\341\246\262\r\346T\305d\317\377S\275\351[O\244\016+C{\364\2115\223|\374\001\252\274E\n%"
  }
}
library {
  maven_library {
    artifactId: "BAuthLib-1.0.0"
  }
  digests {
    sha256: ">\vlc}\264\377\361\371V\346\311)\250G\003i\273\325\266\221m\253\346\377\033\25053U<k"
  }
}
library {
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
}
library {
  digests {
    sha256: "\023\305\370\367t\330\033L\331\274\253\377\"\323\374Q\301\r\3163\302K\344\240\247\363$\256\321\aIn"
  }
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 1
  dependency_index: 2
  dependency_index: 3
  dependency_index: 4
  dependency_index: 5
  dependency_index: 6
  dependency_index: 7
  dependency_index: 8
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
