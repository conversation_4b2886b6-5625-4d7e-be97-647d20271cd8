Using template 'C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer\Apk\UnityManifest.xml'

android.hardware.vulkan.version was added because:
	One of the graphics devices was Vulkan
supports-gl-texture elements added because:
	TextureSupport was not set to generic
android.permission.INTERNET was added because:
	UnityEngine.Networking was present in:
		Assembly-CSharp-FeaturesChecked.txt
		PICO.Platform-FeaturesChecked.txt
		Unity.InputSystem-FeaturesChecked.txt
	UnityEngine.Network was present in:
		Assembly-CSharp-FeaturesChecked.txt
		PICO.Platform-FeaturesChecked.txt
		Unity.InputSystem-FeaturesChecked.txt
	UnityEngine.Networking.UnityWebRequest was present in:
		Assembly-CSharp-FeaturesChecked.txt
		PICO.Platform-FeaturesChecked.txt
	Analytics
	Performance Reporting
android.hardware.touchscreen.multitouch, android.hardware.touchscreen.multitouch.distinct were enabled because:
	UnityEngine.Input::GetTouch was present in:
		Unity.XR.Interaction.Toolkit-FeaturesChecked.txt
		UnityEngine.UI-FeaturesChecked.txt
	UnityEngine.Input::get_touchCount was present in:
		Unity.XR.Interaction.Toolkit-FeaturesChecked.txt
		UnityEngine.UI-FeaturesChecked.txt
