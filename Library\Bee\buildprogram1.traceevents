{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1750741932171583, "dur": 321328, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1750741932172214, "dur": 30636, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1750741932375051, "dur": 59386, "ph": "X", "name": "SetupSpecificConfigImpl libil2cpp", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1750741932378865, "dur": 44775, "ph": "X", "name": "SetupSpecificConfigImpl il2cpp", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1750741932379378, "dur": 25246, "ph": "X", "name": "SetupSpecificConfigImpl bdwgc", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1750741932404644, "dur": 2030, "ph": "X", "name": "SetupSpecificConfigImpl zlib", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1750741932477834, "dur": 2425, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1750741932480261, "dur": 12649, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1750741932480655, "dur": 10005, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1750741932495378, "dur": 1114, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1750741932495115, "dur": 1390, "ph": "X", "name": "Write chrome-trace events", "args": {} },
