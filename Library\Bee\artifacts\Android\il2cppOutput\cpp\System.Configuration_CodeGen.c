﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





// 0x00000001 System.Configuration.ConfigurationPropertyCollection System.Configuration.ConfigurationElement::get_Properties()
extern void ConfigurationElement_get_Properties_m85E584B7C5EAFA411191A245AF41DEC274DE8F93 (void);
// 0x00000002 System.Boolean System.Configuration.ConfigurationElement::IsModified()
extern void ConfigurationElement_IsModified_m03570122B9C781EE3AFC094BDDEA82F56BE2F850 (void);
// 0x00000003 System.Void System.Configuration.ConfigurationElement::Reset(System.Configuration.ConfigurationElement)
extern void ConfigurationElement_Reset_mA1EA05A353D2606B81CF9B50BDBC9D5F9B6DF8AF (void);
// 0x00000004 System.Void System.Configuration.ConfigurationElement::ResetModified()
extern void ConfigurationElement_ResetModified_m1CCB91632C7E81454C9E3A7F259AD72C06BED4B7 (void);
// 0x00000005 System.Void System.Configuration.ConfigurationSection::DeserializeSection(System.Xml.XmlReader)
extern void ConfigurationSection_DeserializeSection_m337F6D10C212ACA2900FCEFC8098393D7776A0CD (void);
// 0x00000006 System.Boolean System.Configuration.ConfigurationSection::IsModified()
extern void ConfigurationSection_IsModified_m65E5503E4AB960336F17AF49AD94FDCA63EC7DD0 (void);
// 0x00000007 System.Void System.Configuration.ConfigurationSection::ResetModified()
extern void ConfigurationSection_ResetModified_m3A4EF275904DF31400B33FD9C4F22537D2922844 (void);
// 0x00000008 System.String System.Configuration.ConfigurationSection::SerializeSection(System.Configuration.ConfigurationElement,System.String,System.Configuration.ConfigurationSaveMode)
extern void ConfigurationSection_SerializeSection_m4526B82EBA81F4B2A049AA668905A27C58A07540 (void);
// 0x00000009 System.Void System.Configuration.ConfigurationCollectionAttribute::.ctor(System.Type)
extern void ConfigurationCollectionAttribute__ctor_m1C1204D379E75BB9D1AC794CAD78B0C95FDEDB8D (void);
// 0x0000000A System.Void System.Configuration.IgnoreSection::.ctor()
extern void IgnoreSection__ctor_mDF97B44EFE0B08DF0D7E89F7B79553E010597066 (void);
// 0x0000000B System.Configuration.ConfigurationPropertyCollection System.Configuration.IgnoreSection::get_Properties()
extern void IgnoreSection_get_Properties_mE3DBA6242176B6E6438CEEBEB3A48319E9EFF133 (void);
// 0x0000000C System.Void System.Configuration.IgnoreSection::DeserializeSection(System.Xml.XmlReader)
extern void IgnoreSection_DeserializeSection_m622C6FAE1160DCC952A4E36FC9E2DCB9DCC34CEC (void);
// 0x0000000D System.Boolean System.Configuration.IgnoreSection::IsModified()
extern void IgnoreSection_IsModified_mB1D57799DA9AE024B99CB05766D5497A3DD8F19F (void);
// 0x0000000E System.Void System.Configuration.IgnoreSection::Reset(System.Configuration.ConfigurationElement)
extern void IgnoreSection_Reset_m8A41B00CEC8C72D608FEE005D438864B5638B84E (void);
// 0x0000000F System.Void System.Configuration.IgnoreSection::ResetModified()
extern void IgnoreSection_ResetModified_m13E416D3841F85E3B334CF9EB517FFBE9F7E224C (void);
// 0x00000010 System.String System.Configuration.IgnoreSection::SerializeSection(System.Configuration.ConfigurationElement,System.String,System.Configuration.ConfigurationSaveMode)
extern void IgnoreSection_SerializeSection_m12BD59834DBCACE13758DA83BD3DEF2B8A6F3DBE (void);
// 0x00000011 System.Void Unity.ThrowStub::ThrowNotSupportedException()
extern void ThrowStub_ThrowNotSupportedException_mA14F496FFE8A1B92C4565A9F18F2113E1C1F2A77 (void);
static Il2CppMethodPointer s_methodPointers[17] = 
{
	ConfigurationElement_get_Properties_m85E584B7C5EAFA411191A245AF41DEC274DE8F93,
	ConfigurationElement_IsModified_m03570122B9C781EE3AFC094BDDEA82F56BE2F850,
	ConfigurationElement_Reset_mA1EA05A353D2606B81CF9B50BDBC9D5F9B6DF8AF,
	ConfigurationElement_ResetModified_m1CCB91632C7E81454C9E3A7F259AD72C06BED4B7,
	ConfigurationSection_DeserializeSection_m337F6D10C212ACA2900FCEFC8098393D7776A0CD,
	ConfigurationSection_IsModified_m65E5503E4AB960336F17AF49AD94FDCA63EC7DD0,
	ConfigurationSection_ResetModified_m3A4EF275904DF31400B33FD9C4F22537D2922844,
	ConfigurationSection_SerializeSection_m4526B82EBA81F4B2A049AA668905A27C58A07540,
	ConfigurationCollectionAttribute__ctor_m1C1204D379E75BB9D1AC794CAD78B0C95FDEDB8D,
	IgnoreSection__ctor_mDF97B44EFE0B08DF0D7E89F7B79553E010597066,
	IgnoreSection_get_Properties_mE3DBA6242176B6E6438CEEBEB3A48319E9EFF133,
	IgnoreSection_DeserializeSection_m622C6FAE1160DCC952A4E36FC9E2DCB9DCC34CEC,
	IgnoreSection_IsModified_mB1D57799DA9AE024B99CB05766D5497A3DD8F19F,
	IgnoreSection_Reset_m8A41B00CEC8C72D608FEE005D438864B5638B84E,
	IgnoreSection_ResetModified_m13E416D3841F85E3B334CF9EB517FFBE9F7E224C,
	IgnoreSection_SerializeSection_m12BD59834DBCACE13758DA83BD3DEF2B8A6F3DBE,
	ThrowStub_ThrowNotSupportedException_mA14F496FFE8A1B92C4565A9F18F2113E1C1F2A77,
};
static const int32_t s_InvokerIndices[17] = 
{
	6088,
	5994,
	4880,
	6215,
	4880,
	5994,
	6215,
	1160,
	4880,
	6215,
	6088,
	4880,
	5994,
	4880,
	6215,
	1160,
	10922,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_System_Configuration_CodeGenModule;
const Il2CppCodeGenModule g_System_Configuration_CodeGenModule = 
{
	"System.Configuration.dll",
	17,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL, // module initializer,
	NULL,
	NULL,
	NULL,
};
