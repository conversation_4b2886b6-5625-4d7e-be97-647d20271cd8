<dependencies>
  <compile
      roots="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle@@:unityLibrary::release">
    <dependency
        name="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle@@:unityLibrary::release"
        simpleName="Gradle:unityLibrary"/>
  </compile>
  <package
      roots="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle@@:unityLibrary::release,__local_aars__:D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\gson-2.10.1.jar:unspecified@jar,__local_aars__:D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\unity-classes.jar:unspecified@jar,:loader-1.0.5.ForUnitySDK:@aar,:tob_api-release:@aar,:capturelib-0.0.7:@aar,:CameraRenderingPlugin:@aar,:PxrPlatform:@aar,:tobservicelib-release:@aar,:BAuthLib-1.0.0:@aar,D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle@@:unityLibrary:xrmanifest.androidlib::release">
    <dependency
        name="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle@@:unityLibrary::release"
        simpleName="Gradle:unityLibrary"/>
    <dependency
        name="__local_aars__:D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\gson-2.10.1.jar:unspecified@jar"
        simpleName="__local_aars__:D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\gson-2.10.1.jar"/>
    <dependency
        name="__local_aars__:D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\unity-classes.jar:unspecified@jar"
        simpleName="__local_aars__:D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\unity-classes.jar"/>
    <dependency
        name=":loader-1.0.5.ForUnitySDK:@aar"
        simpleName=":loader-1.0.5.ForUnitySDK"/>
    <dependency
        name=":tob_api-release:@aar"
        simpleName=":tob_api-release"/>
    <dependency
        name=":capturelib-0.0.7:@aar"
        simpleName=":capturelib-0.0.7"/>
    <dependency
        name=":CameraRenderingPlugin:@aar"
        simpleName=":CameraRenderingPlugin"/>
    <dependency
        name=":PxrPlatform:@aar"
        simpleName=":PxrPlatform"/>
    <dependency
        name=":tobservicelib-release:@aar"
        simpleName=":tobservicelib-release"/>
    <dependency
        name=":BAuthLib-1.0.0:@aar"
        simpleName=":BAuthLib-1.0.0"/>
    <dependency
        name="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle@@:unityLibrary:xrmanifest.androidlib::release"
        simpleName="Gradle.unityLibrary:xrmanifest.androidlib"/>
  </package>
</dependencies>
