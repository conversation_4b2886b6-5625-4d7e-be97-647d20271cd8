<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\jniLibs"><file name="arm64-v8a/libil2cpp.so" path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\jniLibs\arm64-v8a\libil2cpp.so"/><file name="arm64-v8a/libmain.so" path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\jniLibs\arm64-v8a\libmain.so"/><file name="arm64-v8a/libPicoAmbisonicDecoder.so" path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\jniLibs\arm64-v8a\libPicoAmbisonicDecoder.so"/><file name="arm64-v8a/libPicoAudioRouter.so" path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\jniLibs\arm64-v8a\libPicoAudioRouter.so"/><file name="arm64-v8a/libPicoSpatializer.so" path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\jniLibs\arm64-v8a\libPicoSpatializer.so"/><file name="arm64-v8a/libpxrplatformloader.so" path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\jniLibs\arm64-v8a\libpxrplatformloader.so"/><file name="arm64-v8a/libunity.so" path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\jniLibs\arm64-v8a\libunity.so"/></source></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\nwu\Assembly\UnityProjects\VRAssembly\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\release\jniLibs"/></dataSet></merger>