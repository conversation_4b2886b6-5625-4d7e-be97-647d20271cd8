apply plugin: 'com.android.library'


dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation(name: 'loader-1.0.5.ForUnitySDK', ext:'aar')
    implementation(name: 'tob_api-release', ext:'aar')
    implementation(name: 'capturelib-0.0.7', ext:'aar')
    implementation(name: 'CameraRenderingPlugin', ext:'aar')
    implementation(name: 'PxrPlatform', ext:'aar')
    implementation(name: 'tobservicelib-release', ext:'aar')
    implementation(name: 'BAuthLib-1.0.0', ext:'aar')
    implementation project('xrmanifest.androidlib')

}

android {
    namespace "com.unity3d.player"
    ndkPath "C:/Program Files/Unity/Hub/Editor/2021.3.44f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK"
    compileSdkVersion 30
    buildToolsVersion '34.0.0'

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    defaultConfig {
        minSdkVersion 29
        targetSdkVersion 29
        ndk {
            abiFilters 'arm64-v8a'
        }
        versionCode 1
        versionName '0.1'
        consumerProguardFiles 'proguard-unity.txt'
    }

    lintOptions {
        abortOnError false
    }

    aaptOptions {
        noCompress = ['.unity3d', '.ress', '.resource', '.obb', '.bundle', '.unityexp'] + unityStreamingAssets.tokenize(', ')
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"
    }

    packagingOptions {
        doNotStrip '*/arm64-v8a/*.so'
        jniLibs {
            useLegacyPackaging true
        }
    }
}



